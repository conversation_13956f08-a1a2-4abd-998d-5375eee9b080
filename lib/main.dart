import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' show ProviderScope;
import 'package:techrar_captain/app/gym_app/view/auth_view.dart';
import 'package:techrar_captain/app/gym_app/view/home_view.dart';
import 'package:techrar_captain/app/gym_app/view/class_booking_view.dart';
import 'package:techrar_captain/app/gym_app/view/achievements_view.dart';
import 'package:techrar_captain/app/gym_app/view/subscription_view.dart';

import 'core/exports/core.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  runApp(const ProviderScope(child: MyApp()));
}

class MyApp extends StatefulWidget {
  const MyApp({Key? key}) : super(key: key);

  static final GlobalKey<NavigatorState> _appKey = GlobalKey<NavigatorState>();
  static GlobalKey<NavigatorState> get appKey => _appKey;

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  Locale? _locale;
  setLocale(Locale locale) {
    setState(() {
      _locale = locale;
    });
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      navigatorKey: MyApp._appKey,
      debugShowCheckedModeBanner: false,
      title: 'Gym App',
      locale: _locale,
      supportedLocales: appSupportedLocales,
      localizationsDelegates: const [
        AppLocalization.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate
      ],
      theme: ThemeData(
        primaryColor: kPrimaryColor,
        scaffoldBackgroundColor: kBackgroundColor,
        fontFamily: 'Poppins',
      ),
      initialRoute: '/auth',
      routes: {
        '/auth': (context) => const AuthView(),
        '/home': (context) => const HomeView(),
        '/classes': (context) => const ClassBookingView(),
        '/achievements': (context) => const AchievementsView(),
        '/subscription': (context) => const SubscriptionView(),
      },
    );
  }
}
