import 'package:flutter/material.dart' show Color, Colors;

// [Main] Colors
const kPrimaryColor = Color(0xFF1e71b8);
const kSecondaryColor = Color(0xFFec5800);
// const kSecondaryColorDark = Color(0xFFE40B6F);
// const kSecondaryColorLight = Color(0xFFFA8FC0);
const kTransparent = Colors.transparent;
const kWhite = Colors.white;
const kBlack = Colors.black;
const kFontsColor = Color(0xFF313131);
const kGrey = Color(0xFF8898A4);
const kGrey2 = Color(0xFFCFD2D5);
const kLightGrey = Color(0xFFEBEBF0);
const kLightGrey2 = Color(0xFFF6F6F6);
const kInActiveColor = Colors.grey; //Color(0xFF555555);
const kHintFontsColor = Colors.grey; //Color(0xFF555555);
const kBackgroundColor = Colors.white; //Color(0xFFEEF4F2);
const kBackground2Color = Color(0xFFf8f7f7);

const kButtonColor = kSecondaryColor;

const kRed0 = Color(0xFFE53535);
const kRed1 = Color(0xFFFF3B3B);
const kRed2 = Color(0xFFFF5C5C);
const kRed3 = Color(0xFFFF8080);
const kRed4 = Color(0xFFFFE5E5);

const kYellow0 = Color(0xFFE5B800);
const kYellow1 = Color(0xFFFFCC00);
const kYellow2 = Color(0xFFFDDD48);
const kYellow3 = Color(0xFFFDED72);
const kYellow4 = Color(0xFFFFFEE5);
const kYellow5 = Color(0xFFE7AE00);
const kLightYellow = Color(0xFFFFF0C1);

const kGreen0 = Color(0xFF00ac5f);
const kGreen1 = Color(0xFF00c96f);
const kGreen2 = Color(0xFF00e089);
const kGreen3 = Color(0xFF00f2a0);
const kGreen4 = Color(0xFFd5fff1);

const kPurple0 = Color(0xFF5c009a);
const kPurple1 = Color(0xFF7a00ce);
const kPurple2 = Color(0xFFc44fdb);
const kPurple3 = Color(0xFFf19fea);
const kPurple4 = Color(0xFFffe3ff);
const kOrange0 = Color(0xFFff6f00);
const kOrange1 = Color(0xFFff7c00);
const kOrange2 = Color(0xFFffa639);
const kOrange3 = Color(0xFFffca71);
const kOrange4 = Color(0xFFfff8e4);
const kGreen = Color(0xFF39D98A);
const kBlue = Color(0xFF5B8DEF);
const kOrange = Color(0xFFFDAC42);
const kTeal0 = Color(0xFF00bcc5);
const kTeal1 = Color(0xFF00d5df);
const kTeal2 = Color(0xFF00e4e8);
const kTeal3 = Color(0xFF78f3f3);
const kTeal4 = Color(0xFFd8ffff);

// [Core Design System] Colors
const Color kLight0 = Color(0xFFE4E4EB);
const Color kLight1 = Color(0xFFEBEBF0);
const Color kLight2 = Color(0xFFF2F2F2);
const Color kLight3 = Color(0xFFFAFAFC);
const Color kLight4 = Color(0xFFFFFFFF);
const Color kDark1 = Color(0xFF28293D);
const Color kDark2 = Color(0xFF555770);
const Color kDark3 = Color(0xFF8F90A6);
const Color kDark4 = Color(0xFFC7C9D9);
