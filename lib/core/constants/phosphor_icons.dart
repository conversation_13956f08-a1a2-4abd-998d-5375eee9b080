// Place fonts/Phosphor.ttf in your fonts/ directory and
// add the following to your pubspec.yaml
// flutter:
//   fonts:
//    - family: Phosphor
//      fonts:
//       - asset: fonts/Phosphor.ttf
// ignore_for_file: constant_identifier_names

import 'package:flutter/widgets.dart';

class PhosphorIcons {
  PhosphorIcons._();

  static const String _fontFamily = 'Phosphor';

  static const IconData activity_thin = IconData(0xea02, fontFamily: _fontFamily);
  static const IconData address_book_thin = IconData(0xea03, fontFamily: _fontFamily);
  static const IconData airplane_in_flight_thin = IconData(0xea04, fontFamily: _fontFamily);
  static const IconData airplane_landing_thin = IconData(0xea05, fontFamily: _fontFamily);
  static const IconData airplane_takeoff_thin = IconData(0xea06, fontFamily: _fontFamily);
  static const IconData airplane_thin = IconData(0xea07, fontFamily: _fontFamily);
  static const IconData airplane_tilt_thin = IconData(0xea08, fontFamily: _fontFamily);
  static const IconData airplay_thin = IconData(0xea09, fontFamily: _fontFamily);
  static const IconData alarm_thin = IconData(0xea0a, fontFamily: _fontFamily);
  static const IconData alien_thin = IconData(0xea0b, fontFamily: _fontFamily);
  static const IconData align_bottom_simple_thin = IconData(0xea0c, fontFamily: _fontFamily);
  static const IconData align_bottom_thin = IconData(0xea0d, fontFamily: _fontFamily);
  static const IconData align_center_horizontal_simple_thin = IconData(0xea0e, fontFamily: _fontFamily);
  static const IconData align_center_horizontal_thin = IconData(0xea0f, fontFamily: _fontFamily);
  static const IconData align_center_vertical_simple_thin = IconData(0xea10, fontFamily: _fontFamily);
  static const IconData align_center_vertical_thin = IconData(0xea11, fontFamily: _fontFamily);
  static const IconData align_left_simple_thin = IconData(0xea12, fontFamily: _fontFamily);
  static const IconData align_left_thin = IconData(0xea13, fontFamily: _fontFamily);
  static const IconData align_right_simple_thin = IconData(0xea14, fontFamily: _fontFamily);
  static const IconData align_right_thin = IconData(0xea15, fontFamily: _fontFamily);
  static const IconData align_top_simple_thin = IconData(0xea16, fontFamily: _fontFamily);
  static const IconData align_top_thin = IconData(0xea17, fontFamily: _fontFamily);
  static const IconData anchor_simple_thin = IconData(0xea18, fontFamily: _fontFamily);
  static const IconData anchor_thin = IconData(0xea19, fontFamily: _fontFamily);
  static const IconData android_logo_thin = IconData(0xea1a, fontFamily: _fontFamily);
  static const IconData angular_logo_thin = IconData(0xea1b, fontFamily: _fontFamily);
  static const IconData aperture_thin = IconData(0xea1c, fontFamily: _fontFamily);
  static const IconData app_store_logo_thin = IconData(0xea1d, fontFamily: _fontFamily);
  static const IconData app_window_thin = IconData(0xea1e, fontFamily: _fontFamily);
  static const IconData apple_logo_thin = IconData(0xea1f, fontFamily: _fontFamily);
  static const IconData apple_podcasts_logo_thin = IconData(0xea20, fontFamily: _fontFamily);
  static const IconData archive_box_thin = IconData(0xea21, fontFamily: _fontFamily);
  static const IconData archive_thin = IconData(0xea22, fontFamily: _fontFamily);
  static const IconData archive_tray_thin = IconData(0xea23, fontFamily: _fontFamily);
  static const IconData armchair_thin = IconData(0xea24, fontFamily: _fontFamily);
  static const IconData arrow_arc_left_thin = IconData(0xea25, fontFamily: _fontFamily);
  static const IconData arrow_arc_right_thin = IconData(0xea26, fontFamily: _fontFamily);
  static const IconData arrow_bend_double_up_left_thin = IconData(0xea27, fontFamily: _fontFamily);
  static const IconData arrow_bend_double_up_right_thin = IconData(0xea28, fontFamily: _fontFamily);
  static const IconData arrow_bend_down_left_thin = IconData(0xea29, fontFamily: _fontFamily);
  static const IconData arrow_bend_down_right_thin = IconData(0xea2a, fontFamily: _fontFamily);
  static const IconData arrow_bend_left_down_thin = IconData(0xea2b, fontFamily: _fontFamily);
  static const IconData arrow_bend_left_up_thin = IconData(0xea2c, fontFamily: _fontFamily);
  static const IconData arrow_bend_right_down_thin = IconData(0xea2d, fontFamily: _fontFamily);
  static const IconData arrow_bend_right_up_thin = IconData(0xea2e, fontFamily: _fontFamily);
  static const IconData arrow_bend_up_left_thin = IconData(0xea2f, fontFamily: _fontFamily);
  static const IconData arrow_bend_up_right_thin = IconData(0xea30, fontFamily: _fontFamily);
  static const IconData arrow_circle_down_left_thin = IconData(0xea31, fontFamily: _fontFamily);
  static const IconData arrow_circle_down_right_thin = IconData(0xea32, fontFamily: _fontFamily);
  static const IconData arrow_circle_down_thin = IconData(0xea33, fontFamily: _fontFamily);
  static const IconData arrow_circle_left_thin = IconData(0xea34, fontFamily: _fontFamily);
  static const IconData arrow_circle_right_thin = IconData(0xea35, fontFamily: _fontFamily);
  static const IconData arrow_circle_up_left_thin = IconData(0xea36, fontFamily: _fontFamily);
  static const IconData arrow_circle_up_right_thin = IconData(0xea37, fontFamily: _fontFamily);
  static const IconData arrow_circle_up_thin = IconData(0xea38, fontFamily: _fontFamily);
  static const IconData arrow_clockwise_thin = IconData(0xea39, fontFamily: _fontFamily);
  static const IconData arrow_counter_clockwise_thin = IconData(0xea3a, fontFamily: _fontFamily);
  static const IconData arrow_down_left_thin = IconData(0xea3b, fontFamily: _fontFamily);
  static const IconData arrow_down_right_thin = IconData(0xea3c, fontFamily: _fontFamily);
  static const IconData arrow_down_thin = IconData(0xea3d, fontFamily: _fontFamily);
  static const IconData arrow_elbow_down_left_thin = IconData(0xea3e, fontFamily: _fontFamily);
  static const IconData arrow_elbow_down_right_thin = IconData(0xea3f, fontFamily: _fontFamily);
  static const IconData arrow_elbow_left_down_thin = IconData(0xea40, fontFamily: _fontFamily);
  static const IconData arrow_elbow_left_thin = IconData(0xea41, fontFamily: _fontFamily);
  static const IconData arrow_elbow_left_up_thin = IconData(0xea42, fontFamily: _fontFamily);
  static const IconData arrow_elbow_right_down_thin = IconData(0xea43, fontFamily: _fontFamily);
  static const IconData arrow_elbow_right_thin = IconData(0xea44, fontFamily: _fontFamily);
  static const IconData arrow_elbow_right_up_thin = IconData(0xea45, fontFamily: _fontFamily);
  static const IconData arrow_elbow_up_left_thin = IconData(0xea46, fontFamily: _fontFamily);
  static const IconData arrow_elbow_up_right_thin = IconData(0xea47, fontFamily: _fontFamily);
  static const IconData arrow_fat_down_thin = IconData(0xea48, fontFamily: _fontFamily);
  static const IconData arrow_fat_left_thin = IconData(0xea49, fontFamily: _fontFamily);
  static const IconData arrow_fat_line_down_thin = IconData(0xea4a, fontFamily: _fontFamily);
  static const IconData arrow_fat_line_left_thin = IconData(0xea4b, fontFamily: _fontFamily);
  static const IconData arrow_fat_line_right_thin = IconData(0xea4c, fontFamily: _fontFamily);
  static const IconData arrow_fat_line_up_thin = IconData(0xea4d, fontFamily: _fontFamily);
  static const IconData arrow_fat_lines_down_thin = IconData(0xea4e, fontFamily: _fontFamily);
  static const IconData arrow_fat_lines_left_thin = IconData(0xea4f, fontFamily: _fontFamily);
  static const IconData arrow_fat_lines_right_thin = IconData(0xea50, fontFamily: _fontFamily);
  static const IconData arrow_fat_lines_up_thin = IconData(0xea51, fontFamily: _fontFamily);
  static const IconData arrow_fat_right_thin = IconData(0xea52, fontFamily: _fontFamily);
  static const IconData arrow_fat_up_thin = IconData(0xea53, fontFamily: _fontFamily);
  static const IconData arrow_left_thin = IconData(0xea54, fontFamily: _fontFamily);
  static const IconData arrow_line_down_left_thin = IconData(0xea55, fontFamily: _fontFamily);
  static const IconData arrow_line_down_right_thin = IconData(0xea56, fontFamily: _fontFamily);
  static const IconData arrow_line_down_thin = IconData(0xea57, fontFamily: _fontFamily);
  static const IconData arrow_line_left_thin = IconData(0xea58, fontFamily: _fontFamily);
  static const IconData arrow_line_right_thin = IconData(0xea59, fontFamily: _fontFamily);
  static const IconData arrow_line_up_left_thin = IconData(0xea5a, fontFamily: _fontFamily);
  static const IconData arrow_line_up_right_thin = IconData(0xea5b, fontFamily: _fontFamily);
  static const IconData arrow_line_up_thin = IconData(0xea5c, fontFamily: _fontFamily);
  static const IconData arrow_right_thin = IconData(0xea5d, fontFamily: _fontFamily);
  static const IconData arrow_square_down_left_thin = IconData(0xea5e, fontFamily: _fontFamily);
  static const IconData arrow_square_down_right_thin = IconData(0xea5f, fontFamily: _fontFamily);
  static const IconData arrow_square_down_thin = IconData(0xea60, fontFamily: _fontFamily);
  static const IconData arrow_square_in_thin = IconData(0xea61, fontFamily: _fontFamily);
  static const IconData arrow_square_left_thin = IconData(0xea62, fontFamily: _fontFamily);
  static const IconData arrow_square_out_thin = IconData(0xea63, fontFamily: _fontFamily);
  static const IconData arrow_square_right_thin = IconData(0xea64, fontFamily: _fontFamily);
  static const IconData arrow_square_up_left_thin = IconData(0xea65, fontFamily: _fontFamily);
  static const IconData arrow_square_up_right_thin = IconData(0xea66, fontFamily: _fontFamily);
  static const IconData arrow_square_up_thin = IconData(0xea67, fontFamily: _fontFamily);
  static const IconData arrow_u_down_left_thin = IconData(0xea68, fontFamily: _fontFamily);
  static const IconData arrow_u_down_right_thin = IconData(0xea69, fontFamily: _fontFamily);
  static const IconData arrow_u_left_down_thin = IconData(0xea6a, fontFamily: _fontFamily);
  static const IconData arrow_u_left_up_thin = IconData(0xea6b, fontFamily: _fontFamily);
  static const IconData arrow_u_right_down_thin = IconData(0xea6c, fontFamily: _fontFamily);
  static const IconData arrow_u_right_up_thin = IconData(0xea6d, fontFamily: _fontFamily);
  static const IconData arrow_u_up_left_thin = IconData(0xea6e, fontFamily: _fontFamily);
  static const IconData arrow_u_up_right_thin = IconData(0xea6f, fontFamily: _fontFamily);
  static const IconData arrow_up_left_thin = IconData(0xea70, fontFamily: _fontFamily);
  static const IconData arrow_up_right_thin = IconData(0xea71, fontFamily: _fontFamily);
  static const IconData arrow_up_thin = IconData(0xea72, fontFamily: _fontFamily);
  static const IconData arrows_clockwise_thin = IconData(0xea73, fontFamily: _fontFamily);
  static const IconData arrows_counter_clockwise_thin = IconData(0xea74, fontFamily: _fontFamily);
  static const IconData arrows_down_up_thin = IconData(0xea75, fontFamily: _fontFamily);
  static const IconData arrows_horizontal_thin = IconData(0xea76, fontFamily: _fontFamily);
  static const IconData arrows_in_cardinal_thin = IconData(0xea77, fontFamily: _fontFamily);
  static const IconData arrows_in_line_horizontal_thin = IconData(0xea78, fontFamily: _fontFamily);
  static const IconData arrows_in_line_vertical_thin = IconData(0xea79, fontFamily: _fontFamily);
  static const IconData arrows_in_simple_thin = IconData(0xea7a, fontFamily: _fontFamily);
  static const IconData arrows_in_thin = IconData(0xea7b, fontFamily: _fontFamily);
  static const IconData arrows_left_right_thin = IconData(0xea7c, fontFamily: _fontFamily);
  static const IconData arrows_out_cardinal_thin = IconData(0xea7d, fontFamily: _fontFamily);
  static const IconData arrows_out_line_horizontal_thin = IconData(0xea7e, fontFamily: _fontFamily);
  static const IconData arrows_out_line_vertical_thin = IconData(0xea7f, fontFamily: _fontFamily);
  static const IconData arrows_out_simple_thin = IconData(0xea80, fontFamily: _fontFamily);
  static const IconData arrows_out_thin = IconData(0xea81, fontFamily: _fontFamily);
  static const IconData arrows_vertical_thin = IconData(0xea82, fontFamily: _fontFamily);
  static const IconData article_medium_thin = IconData(0xea83, fontFamily: _fontFamily);
  static const IconData article_ny_times_thin = IconData(0xea84, fontFamily: _fontFamily);
  static const IconData article_thin = IconData(0xea85, fontFamily: _fontFamily);
  static const IconData asterisk_simple_thin = IconData(0xea86, fontFamily: _fontFamily);
  static const IconData asterisk_thin = IconData(0xea87, fontFamily: _fontFamily);
  static const IconData at_thin = IconData(0xea88, fontFamily: _fontFamily);
  static const IconData atom_thin = IconData(0xea89, fontFamily: _fontFamily);
  static const IconData baby_thin = IconData(0xea8a, fontFamily: _fontFamily);
  static const IconData backpack_thin = IconData(0xea8b, fontFamily: _fontFamily);
  static const IconData backspace_thin = IconData(0xea8c, fontFamily: _fontFamily);
  static const IconData bag_simple_thin = IconData(0xea8d, fontFamily: _fontFamily);
  static const IconData bag_thin = IconData(0xea8e, fontFamily: _fontFamily);
  static const IconData balloon_thin = IconData(0xea8f, fontFamily: _fontFamily);
  static const IconData bandaids_thin = IconData(0xea90, fontFamily: _fontFamily);
  static const IconData bank_thin = IconData(0xea91, fontFamily: _fontFamily);
  static const IconData barbell_thin = IconData(0xea92, fontFamily: _fontFamily);
  static const IconData barcode_thin = IconData(0xea93, fontFamily: _fontFamily);
  static const IconData barricade_thin = IconData(0xea94, fontFamily: _fontFamily);
  static const IconData baseball_thin = IconData(0xea95, fontFamily: _fontFamily);
  static const IconData basketball_thin = IconData(0xea96, fontFamily: _fontFamily);
  static const IconData bathtub_thin = IconData(0xea97, fontFamily: _fontFamily);
  static const IconData battery_charging_thin = IconData(0xea98, fontFamily: _fontFamily);
  static const IconData battery_charging_vertical_thin = IconData(0xea99, fontFamily: _fontFamily);
  static const IconData battery_empty_thin = IconData(0xea9a, fontFamily: _fontFamily);
  static const IconData battery_full_thin = IconData(0xea9b, fontFamily: _fontFamily);
  static const IconData battery_high_thin = IconData(0xea9c, fontFamily: _fontFamily);
  static const IconData battery_low_thin = IconData(0xea9d, fontFamily: _fontFamily);
  static const IconData battery_medium_thin = IconData(0xea9e, fontFamily: _fontFamily);
  static const IconData battery_plus_thin = IconData(0xea9f, fontFamily: _fontFamily);
  static const IconData battery_warning_thin = IconData(0xeaa0, fontFamily: _fontFamily);
  static const IconData battery_warning_vertical_thin = IconData(0xeaa1, fontFamily: _fontFamily);
  static const IconData bed_thin = IconData(0xeaa2, fontFamily: _fontFamily);
  static const IconData beer_bottle_thin = IconData(0xeaa3, fontFamily: _fontFamily);
  static const IconData behance_logo_thin = IconData(0xeaa4, fontFamily: _fontFamily);
  static const IconData bell_ringing_thin = IconData(0xeaa5, fontFamily: _fontFamily);
  static const IconData bell_simple_ringing_thin = IconData(0xeaa6, fontFamily: _fontFamily);
  static const IconData bell_simple_slash_thin = IconData(0xeaa7, fontFamily: _fontFamily);
  static const IconData bell_simple_thin = IconData(0xeaa8, fontFamily: _fontFamily);
  static const IconData bell_simple_z_thin = IconData(0xeaa9, fontFamily: _fontFamily);
  static const IconData bell_slash_thin = IconData(0xeaaa, fontFamily: _fontFamily);
  static const IconData bell_thin = IconData(0xeaab, fontFamily: _fontFamily);
  static const IconData bell_z_thin = IconData(0xeaac, fontFamily: _fontFamily);
  static const IconData bezier_curve_thin = IconData(0xeaad, fontFamily: _fontFamily);
  static const IconData bicycle_thin = IconData(0xeaae, fontFamily: _fontFamily);
  static const IconData binoculars_thin = IconData(0xeaaf, fontFamily: _fontFamily);
  static const IconData bird_thin = IconData(0xeab0, fontFamily: _fontFamily);
  static const IconData bluetooth_connected_thin = IconData(0xeab1, fontFamily: _fontFamily);
  static const IconData bluetooth_slash_thin = IconData(0xeab2, fontFamily: _fontFamily);
  static const IconData bluetooth_thin = IconData(0xeab3, fontFamily: _fontFamily);
  static const IconData bluetooth_x_thin = IconData(0xeab4, fontFamily: _fontFamily);
  static const IconData boat_thin = IconData(0xeab5, fontFamily: _fontFamily);
  static const IconData book_bookmark_thin = IconData(0xeab6, fontFamily: _fontFamily);
  static const IconData book_open_thin = IconData(0xeab7, fontFamily: _fontFamily);
  static const IconData book_thin = IconData(0xeab8, fontFamily: _fontFamily);
  static const IconData bookmark_simple_thin = IconData(0xeab9, fontFamily: _fontFamily);
  static const IconData bookmark_thin = IconData(0xeaba, fontFamily: _fontFamily);
  static const IconData bookmarks_simple_thin = IconData(0xeabb, fontFamily: _fontFamily);
  static const IconData bookmarks_thin = IconData(0xeabc, fontFamily: _fontFamily);
  static const IconData books_thin = IconData(0xeabd, fontFamily: _fontFamily);
  static const IconData bounding_box_thin = IconData(0xeabe, fontFamily: _fontFamily);
  static const IconData brackets_angle_thin = IconData(0xeabf, fontFamily: _fontFamily);
  static const IconData brackets_curly_thin = IconData(0xeac0, fontFamily: _fontFamily);
  static const IconData brackets_round_thin = IconData(0xeac1, fontFamily: _fontFamily);
  static const IconData brackets_square_thin = IconData(0xeac2, fontFamily: _fontFamily);
  static const IconData brain_thin = IconData(0xeac3, fontFamily: _fontFamily);
  static const IconData brandy_thin = IconData(0xeac4, fontFamily: _fontFamily);
  static const IconData briefcase_metal_thin = IconData(0xeac5, fontFamily: _fontFamily);
  static const IconData briefcase_thin = IconData(0xeac6, fontFamily: _fontFamily);
  static const IconData broadcast_thin = IconData(0xeac7, fontFamily: _fontFamily);
  static const IconData browser_thin = IconData(0xeac8, fontFamily: _fontFamily);
  static const IconData browsers_thin = IconData(0xeac9, fontFamily: _fontFamily);
  static const IconData bug_beetle_thin = IconData(0xeaca, fontFamily: _fontFamily);
  static const IconData bug_droid_thin = IconData(0xeacb, fontFamily: _fontFamily);
  static const IconData bug_thin = IconData(0xeacc, fontFamily: _fontFamily);
  static const IconData buildings_thin = IconData(0xeacd, fontFamily: _fontFamily);
  static const IconData bus_thin = IconData(0xeace, fontFamily: _fontFamily);
  static const IconData butterfly_thin = IconData(0xeacf, fontFamily: _fontFamily);
  static const IconData cactus_thin = IconData(0xead0, fontFamily: _fontFamily);
  static const IconData cake_thin = IconData(0xead1, fontFamily: _fontFamily);
  static const IconData calculator_thin = IconData(0xead2, fontFamily: _fontFamily);
  static const IconData calendar_blank_thin = IconData(0xead3, fontFamily: _fontFamily);
  static const IconData calendar_check_thin = IconData(0xead4, fontFamily: _fontFamily);
  static const IconData calendar_plus_thin = IconData(0xead5, fontFamily: _fontFamily);
  static const IconData calendar_thin = IconData(0xead6, fontFamily: _fontFamily);
  static const IconData calendar_x_thin = IconData(0xead7, fontFamily: _fontFamily);
  static const IconData camera_rotate_thin = IconData(0xead8, fontFamily: _fontFamily);
  static const IconData camera_slash_thin = IconData(0xead9, fontFamily: _fontFamily);
  static const IconData camera_thin = IconData(0xeada, fontFamily: _fontFamily);
  static const IconData campfire_thin = IconData(0xeadb, fontFamily: _fontFamily);
  static const IconData car_simple_thin = IconData(0xeadc, fontFamily: _fontFamily);
  static const IconData car_thin = IconData(0xeadd, fontFamily: _fontFamily);
  static const IconData cardholder_thin = IconData(0xeade, fontFamily: _fontFamily);
  static const IconData cards_thin = IconData(0xeadf, fontFamily: _fontFamily);
  static const IconData caret_circle_double_down_thin = IconData(0xeae0, fontFamily: _fontFamily);
  static const IconData caret_circle_double_left_thin = IconData(0xeae1, fontFamily: _fontFamily);
  static const IconData caret_circle_double_right_thin = IconData(0xeae2, fontFamily: _fontFamily);
  static const IconData caret_circle_double_up_thin = IconData(0xeae3, fontFamily: _fontFamily);
  static const IconData caret_circle_down_thin = IconData(0xeae4, fontFamily: _fontFamily);
  static const IconData caret_circle_left_thin = IconData(0xeae5, fontFamily: _fontFamily);
  static const IconData caret_circle_right_thin = IconData(0xeae6, fontFamily: _fontFamily);
  static const IconData caret_circle_up_thin = IconData(0xeae7, fontFamily: _fontFamily);
  static const IconData caret_double_down_thin = IconData(0xeae8, fontFamily: _fontFamily);
  static const IconData caret_double_left_thin = IconData(0xeae9, fontFamily: _fontFamily);
  static const IconData caret_double_right_thin = IconData(0xeaea, fontFamily: _fontFamily);
  static const IconData caret_double_up_thin = IconData(0xeaeb, fontFamily: _fontFamily);
  static const IconData caret_down_thin = IconData(0xeaec, fontFamily: _fontFamily);
  static const IconData caret_left_thin = IconData(0xeaed, fontFamily: _fontFamily);
  static const IconData caret_right_thin = IconData(0xeaee, fontFamily: _fontFamily);
  static const IconData caret_up_thin = IconData(0xeaef, fontFamily: _fontFamily);
  static const IconData cat_thin = IconData(0xeaf0, fontFamily: _fontFamily);
  static const IconData cell_signal_full_thin = IconData(0xeaf1, fontFamily: _fontFamily);
  static const IconData cell_signal_high_thin = IconData(0xeaf2, fontFamily: _fontFamily);
  static const IconData cell_signal_low_thin = IconData(0xeaf3, fontFamily: _fontFamily);
  static const IconData cell_signal_medium_thin = IconData(0xeaf4, fontFamily: _fontFamily);
  static const IconData cell_signal_none_thin = IconData(0xeaf5, fontFamily: _fontFamily);
  static const IconData cell_signal_slash_thin = IconData(0xeaf6, fontFamily: _fontFamily);
  static const IconData cell_signal_x_thin = IconData(0xeaf7, fontFamily: _fontFamily);
  static const IconData chalkboard_simple_thin = IconData(0xeaf8, fontFamily: _fontFamily);
  static const IconData chalkboard_teacher_thin = IconData(0xeaf9, fontFamily: _fontFamily);
  static const IconData chalkboard_thin = IconData(0xeafa, fontFamily: _fontFamily);
  static const IconData chart_bar_horizontal_thin = IconData(0xeafb, fontFamily: _fontFamily);
  static const IconData chart_bar_thin = IconData(0xeafc, fontFamily: _fontFamily);
  static const IconData chart_line_thin = IconData(0xeafd, fontFamily: _fontFamily);
  static const IconData chart_line_up_thin = IconData(0xeafe, fontFamily: _fontFamily);
  static const IconData chart_pie_slice_thin = IconData(0xeaff, fontFamily: _fontFamily);
  static const IconData chart_pie_thin = IconData(0xeb00, fontFamily: _fontFamily);
  static const IconData chat_centered_dots_thin = IconData(0xeb01, fontFamily: _fontFamily);
  static const IconData chat_centered_text_thin = IconData(0xeb02, fontFamily: _fontFamily);
  static const IconData chat_centered_thin = IconData(0xeb03, fontFamily: _fontFamily);
  static const IconData chat_circle_dots_thin = IconData(0xeb04, fontFamily: _fontFamily);
  static const IconData chat_circle_text_thin = IconData(0xeb05, fontFamily: _fontFamily);
  static const IconData chat_circle_thin = IconData(0xeb06, fontFamily: _fontFamily);
  static const IconData chat_dots_thin = IconData(0xeb07, fontFamily: _fontFamily);
  static const IconData chat_teardrop_dots_thin = IconData(0xeb08, fontFamily: _fontFamily);
  static const IconData chat_teardrop_text_thin = IconData(0xeb09, fontFamily: _fontFamily);
  static const IconData chat_teardrop_thin = IconData(0xeb0a, fontFamily: _fontFamily);
  static const IconData chat_text_thin = IconData(0xeb0b, fontFamily: _fontFamily);
  static const IconData chat_thin = IconData(0xeb0c, fontFamily: _fontFamily);
  static const IconData chats_circle_thin = IconData(0xeb0d, fontFamily: _fontFamily);
  static const IconData chats_teardrop_thin = IconData(0xeb0e, fontFamily: _fontFamily);
  static const IconData chats_thin = IconData(0xeb0f, fontFamily: _fontFamily);
  static const IconData check_circle_thin = IconData(0xeb10, fontFamily: _fontFamily);
  static const IconData check_square_offset_thin = IconData(0xeb11, fontFamily: _fontFamily);
  static const IconData check_square_thin = IconData(0xeb12, fontFamily: _fontFamily);
  static const IconData check_thin = IconData(0xeb13, fontFamily: _fontFamily);
  static const IconData checks_thin = IconData(0xeb14, fontFamily: _fontFamily);
  static const IconData circle_dashed_thin = IconData(0xeb15, fontFamily: _fontFamily);
  static const IconData circle_half_thin = IconData(0xeb16, fontFamily: _fontFamily);
  static const IconData circle_half_tilt_thin = IconData(0xeb17, fontFamily: _fontFamily);
  static const IconData circle_notch_thin = IconData(0xeb18, fontFamily: _fontFamily);
  static const IconData circle_thin = IconData(0xeb19, fontFamily: _fontFamily);
  static const IconData circle_wavy_check_thin = IconData(0xeb1a, fontFamily: _fontFamily);
  static const IconData circle_wavy_question_thin = IconData(0xeb1b, fontFamily: _fontFamily);
  static const IconData circle_wavy_thin = IconData(0xeb1c, fontFamily: _fontFamily);
  static const IconData circle_wavy_warning_thin = IconData(0xeb1d, fontFamily: _fontFamily);
  static const IconData circles_four_thin = IconData(0xeb1e, fontFamily: _fontFamily);
  static const IconData circles_three_plus_thin = IconData(0xeb1f, fontFamily: _fontFamily);
  static const IconData circles_three_thin = IconData(0xeb20, fontFamily: _fontFamily);
  static const IconData clipboard_text_thin = IconData(0xeb21, fontFamily: _fontFamily);
  static const IconData clipboard_thin = IconData(0xeb22, fontFamily: _fontFamily);
  static const IconData clock_afternoon_thin = IconData(0xeb23, fontFamily: _fontFamily);
  static const IconData clock_clockwise_thin = IconData(0xeb24, fontFamily: _fontFamily);
  static const IconData clock_counter_clockwise_thin = IconData(0xeb25, fontFamily: _fontFamily);
  static const IconData clock_thin = IconData(0xeb26, fontFamily: _fontFamily);
  static const IconData closed_captioning_thin = IconData(0xeb27, fontFamily: _fontFamily);
  static const IconData cloud_arrow_down_thin = IconData(0xeb28, fontFamily: _fontFamily);
  static const IconData cloud_arrow_up_thin = IconData(0xeb29, fontFamily: _fontFamily);
  static const IconData cloud_check_thin = IconData(0xeb2a, fontFamily: _fontFamily);
  static const IconData cloud_fog_thin = IconData(0xeb2b, fontFamily: _fontFamily);
  static const IconData cloud_lightning_thin = IconData(0xeb2c, fontFamily: _fontFamily);
  static const IconData cloud_moon_thin = IconData(0xeb2d, fontFamily: _fontFamily);
  static const IconData cloud_rain_thin = IconData(0xeb2e, fontFamily: _fontFamily);
  static const IconData cloud_slash_thin = IconData(0xeb2f, fontFamily: _fontFamily);
  static const IconData cloud_snow_thin = IconData(0xeb30, fontFamily: _fontFamily);
  static const IconData cloud_sun_thin = IconData(0xeb31, fontFamily: _fontFamily);
  static const IconData cloud_thin = IconData(0xeb32, fontFamily: _fontFamily);
  static const IconData club_thin = IconData(0xeb33, fontFamily: _fontFamily);
  static const IconData coat_hanger_thin = IconData(0xeb34, fontFamily: _fontFamily);
  static const IconData code_simple_thin = IconData(0xeb35, fontFamily: _fontFamily);
  static const IconData code_thin = IconData(0xeb36, fontFamily: _fontFamily);
  static const IconData codepen_logo_thin = IconData(0xeb37, fontFamily: _fontFamily);
  static const IconData codesandbox_logo_thin = IconData(0xeb38, fontFamily: _fontFamily);
  static const IconData coffee_thin = IconData(0xeb39, fontFamily: _fontFamily);
  static const IconData coin_thin = IconData(0xeb3a, fontFamily: _fontFamily);
  static const IconData coin_vertical_thin = IconData(0xeb3b, fontFamily: _fontFamily);
  static const IconData coins_thin = IconData(0xeb3c, fontFamily: _fontFamily);
  static const IconData columns_thin = IconData(0xeb3d, fontFamily: _fontFamily);
  static const IconData command_thin = IconData(0xeb3e, fontFamily: _fontFamily);
  static const IconData compass_thin = IconData(0xeb3f, fontFamily: _fontFamily);
  static const IconData computer_tower_thin = IconData(0xeb40, fontFamily: _fontFamily);
  static const IconData confetti_thin = IconData(0xeb41, fontFamily: _fontFamily);
  static const IconData cookie_thin = IconData(0xeb42, fontFamily: _fontFamily);
  static const IconData cooking_pot_thin = IconData(0xeb43, fontFamily: _fontFamily);
  static const IconData copy_simple_thin = IconData(0xeb44, fontFamily: _fontFamily);
  static const IconData copy_thin = IconData(0xeb45, fontFamily: _fontFamily);
  static const IconData copyleft_thin = IconData(0xeb46, fontFamily: _fontFamily);
  static const IconData copyright_thin = IconData(0xeb47, fontFamily: _fontFamily);
  static const IconData corners_in_thin = IconData(0xeb48, fontFamily: _fontFamily);
  static const IconData corners_out_thin = IconData(0xeb49, fontFamily: _fontFamily);
  static const IconData cpu_thin = IconData(0xeb4a, fontFamily: _fontFamily);
  static const IconData credit_card_thin = IconData(0xeb4b, fontFamily: _fontFamily);
  static const IconData crop_thin = IconData(0xeb4c, fontFamily: _fontFamily);
  static const IconData crosshair_simple_thin = IconData(0xeb4d, fontFamily: _fontFamily);
  static const IconData crosshair_thin = IconData(0xeb4e, fontFamily: _fontFamily);
  static const IconData crown_simple_thin = IconData(0xeb4f, fontFamily: _fontFamily);
  static const IconData crown_thin = IconData(0xeb50, fontFamily: _fontFamily);
  static const IconData cube_thin = IconData(0xeb51, fontFamily: _fontFamily);
  static const IconData currency_btc_thin = IconData(0xeb52, fontFamily: _fontFamily);
  static const IconData currency_circle_dollar_thin = IconData(0xeb53, fontFamily: _fontFamily);
  static const IconData currency_cny_thin = IconData(0xeb54, fontFamily: _fontFamily);
  static const IconData currency_dollar_simple_thin = IconData(0xeb55, fontFamily: _fontFamily);
  static const IconData currency_dollar_thin = IconData(0xeb56, fontFamily: _fontFamily);
  static const IconData currency_eth_thin = IconData(0xeb57, fontFamily: _fontFamily);
  static const IconData currency_eur_thin = IconData(0xeb58, fontFamily: _fontFamily);
  static const IconData currency_gbp_thin = IconData(0xeb59, fontFamily: _fontFamily);
  static const IconData currency_inr_thin = IconData(0xeb5a, fontFamily: _fontFamily);
  static const IconData currency_jpy_thin = IconData(0xeb5b, fontFamily: _fontFamily);
  static const IconData currency_krw_thin = IconData(0xeb5c, fontFamily: _fontFamily);
  static const IconData currency_kzt_thin = IconData(0xeb5d, fontFamily: _fontFamily);
  static const IconData currency_ngn_thin = IconData(0xeb5e, fontFamily: _fontFamily);
  static const IconData currency_rub_thin = IconData(0xeb5f, fontFamily: _fontFamily);
  static const IconData cursor_text_thin = IconData(0xeb60, fontFamily: _fontFamily);
  static const IconData cursor_thin = IconData(0xeb61, fontFamily: _fontFamily);
  static const IconData cylinder_thin = IconData(0xeb62, fontFamily: _fontFamily);
  static const IconData database_thin = IconData(0xeb63, fontFamily: _fontFamily);
  static const IconData desktop_thin = IconData(0xeb64, fontFamily: _fontFamily);
  static const IconData desktop_tower_thin = IconData(0xeb65, fontFamily: _fontFamily);
  static const IconData detective_thin = IconData(0xeb66, fontFamily: _fontFamily);
  static const IconData device_mobile_camera_thin = IconData(0xeb67, fontFamily: _fontFamily);
  static const IconData device_mobile_speaker_thin = IconData(0xeb68, fontFamily: _fontFamily);
  static const IconData device_mobile_thin = IconData(0xeb69, fontFamily: _fontFamily);
  static const IconData device_tablet_camera_thin = IconData(0xeb6a, fontFamily: _fontFamily);
  static const IconData device_tablet_speaker_thin = IconData(0xeb6b, fontFamily: _fontFamily);
  static const IconData device_tablet_thin = IconData(0xeb6c, fontFamily: _fontFamily);
  static const IconData diamond_thin = IconData(0xeb6d, fontFamily: _fontFamily);
  static const IconData diamonds_four_thin = IconData(0xeb6e, fontFamily: _fontFamily);
  static const IconData dice_five_thin = IconData(0xeb6f, fontFamily: _fontFamily);
  static const IconData dice_four_thin = IconData(0xeb70, fontFamily: _fontFamily);
  static const IconData dice_one_thin = IconData(0xeb71, fontFamily: _fontFamily);
  static const IconData dice_six_thin = IconData(0xeb72, fontFamily: _fontFamily);
  static const IconData dice_three_thin = IconData(0xeb73, fontFamily: _fontFamily);
  static const IconData dice_two_thin = IconData(0xeb74, fontFamily: _fontFamily);
  static const IconData disc_thin = IconData(0xeb75, fontFamily: _fontFamily);
  static const IconData discord_logo_thin = IconData(0xeb76, fontFamily: _fontFamily);
  static const IconData divide_thin = IconData(0xeb77, fontFamily: _fontFamily);
  static const IconData dog_thin = IconData(0xeb78, fontFamily: _fontFamily);
  static const IconData door_thin = IconData(0xeb79, fontFamily: _fontFamily);
  static const IconData dots_nine_thin = IconData(0xeb7a, fontFamily: _fontFamily);
  static const IconData dots_six_thin = IconData(0xeb7b, fontFamily: _fontFamily);
  static const IconData dots_six_vertical_thin = IconData(0xeb7c, fontFamily: _fontFamily);
  static const IconData dots_three_circle_thin = IconData(0xeb7d, fontFamily: _fontFamily);
  static const IconData dots_three_circle_vertical_thin = IconData(0xeb7e, fontFamily: _fontFamily);
  static const IconData dots_three_outline_thin = IconData(0xeb7f, fontFamily: _fontFamily);
  static const IconData dots_three_outline_vertical_thin = IconData(0xeb80, fontFamily: _fontFamily);
  static const IconData dots_three_thin = IconData(0xeb81, fontFamily: _fontFamily);
  static const IconData dots_three_vertical_thin = IconData(0xeb82, fontFamily: _fontFamily);
  static const IconData download_simple_thin = IconData(0xeb83, fontFamily: _fontFamily);
  static const IconData download_thin = IconData(0xeb84, fontFamily: _fontFamily);
  static const IconData dribbble_logo_thin = IconData(0xeb85, fontFamily: _fontFamily);
  static const IconData drop_half_bottom_thin = IconData(0xeb86, fontFamily: _fontFamily);
  static const IconData drop_half_thin = IconData(0xeb87, fontFamily: _fontFamily);
  static const IconData drop_thin = IconData(0xeb88, fontFamily: _fontFamily);
  static const IconData ear_slash_thin = IconData(0xeb89, fontFamily: _fontFamily);
  static const IconData ear_thin = IconData(0xeb8a, fontFamily: _fontFamily);
  static const IconData egg_crack_thin = IconData(0xeb8b, fontFamily: _fontFamily);
  static const IconData egg_thin = IconData(0xeb8c, fontFamily: _fontFamily);
  static const IconData eject_simple_thin = IconData(0xeb8d, fontFamily: _fontFamily);
  static const IconData eject_thin = IconData(0xeb8e, fontFamily: _fontFamily);
  static const IconData envelope_open_thin = IconData(0xeb8f, fontFamily: _fontFamily);
  static const IconData envelope_simple_open_thin = IconData(0xeb90, fontFamily: _fontFamily);
  static const IconData envelope_simple_thin = IconData(0xeb91, fontFamily: _fontFamily);
  static const IconData envelope_thin = IconData(0xeb92, fontFamily: _fontFamily);
  static const IconData equalizer_thin = IconData(0xeb93, fontFamily: _fontFamily);
  static const IconData equals_thin = IconData(0xeb94, fontFamily: _fontFamily);
  static const IconData eraser_thin = IconData(0xeb95, fontFamily: _fontFamily);
  static const IconData exam_thin = IconData(0xeb96, fontFamily: _fontFamily);
  static const IconData export_thin = IconData(0xeb97, fontFamily: _fontFamily);
  static const IconData eye_closed_thin = IconData(0xeb98, fontFamily: _fontFamily);
  static const IconData eye_slash_thin = IconData(0xeb99, fontFamily: _fontFamily);
  static const IconData eye_thin = IconData(0xeb9a, fontFamily: _fontFamily);
  static const IconData eyedropper_sample_thin = IconData(0xeb9b, fontFamily: _fontFamily);
  static const IconData eyedropper_thin = IconData(0xeb9c, fontFamily: _fontFamily);
  static const IconData eyeglasses_thin = IconData(0xeb9d, fontFamily: _fontFamily);
  static const IconData face_mask_thin = IconData(0xeb9e, fontFamily: _fontFamily);
  static const IconData facebook_logo_thin = IconData(0xeb9f, fontFamily: _fontFamily);
  static const IconData factory_thin = IconData(0xeba0, fontFamily: _fontFamily);
  static const IconData faders_horizontal_thin = IconData(0xeba1, fontFamily: _fontFamily);
  static const IconData faders_thin = IconData(0xeba2, fontFamily: _fontFamily);
  static const IconData fast_forward_circle_thin = IconData(0xeba3, fontFamily: _fontFamily);
  static const IconData fast_forward_thin = IconData(0xeba4, fontFamily: _fontFamily);
  static const IconData figma_logo_thin = IconData(0xeba5, fontFamily: _fontFamily);
  static const IconData file_arrow_down_thin = IconData(0xeba6, fontFamily: _fontFamily);
  static const IconData file_arrow_up_thin = IconData(0xeba7, fontFamily: _fontFamily);
  static const IconData file_audio_thin = IconData(0xeba8, fontFamily: _fontFamily);
  static const IconData file_cloud_thin = IconData(0xeba9, fontFamily: _fontFamily);
  static const IconData file_code_thin = IconData(0xebaa, fontFamily: _fontFamily);
  static const IconData file_css_thin = IconData(0xebab, fontFamily: _fontFamily);
  static const IconData file_csv_thin = IconData(0xebac, fontFamily: _fontFamily);
  static const IconData file_doc_thin = IconData(0xebad, fontFamily: _fontFamily);
  static const IconData file_dotted_thin = IconData(0xebae, fontFamily: _fontFamily);
  static const IconData file_html_thin = IconData(0xebaf, fontFamily: _fontFamily);
  static const IconData file_image_thin = IconData(0xebb0, fontFamily: _fontFamily);
  static const IconData file_jpg_thin = IconData(0xebb1, fontFamily: _fontFamily);
  static const IconData file_js_thin = IconData(0xebb2, fontFamily: _fontFamily);
  static const IconData file_jsx_thin = IconData(0xebb3, fontFamily: _fontFamily);
  static const IconData file_lock_thin = IconData(0xebb4, fontFamily: _fontFamily);
  static const IconData file_minus_thin = IconData(0xebb5, fontFamily: _fontFamily);
  static const IconData file_pdf_thin = IconData(0xebb6, fontFamily: _fontFamily);
  static const IconData file_plus_thin = IconData(0xebb7, fontFamily: _fontFamily);
  static const IconData file_png_thin = IconData(0xebb8, fontFamily: _fontFamily);
  static const IconData file_ppt_thin = IconData(0xebb9, fontFamily: _fontFamily);
  static const IconData file_rs_thin = IconData(0xebba, fontFamily: _fontFamily);
  static const IconData file_search_thin = IconData(0xebbb, fontFamily: _fontFamily);
  static const IconData file_text_thin = IconData(0xebbc, fontFamily: _fontFamily);
  static const IconData file_thin = IconData(0xebbd, fontFamily: _fontFamily);
  static const IconData file_ts_thin = IconData(0xebbe, fontFamily: _fontFamily);
  static const IconData file_tsx_thin = IconData(0xebbf, fontFamily: _fontFamily);
  static const IconData file_video_thin = IconData(0xebc0, fontFamily: _fontFamily);
  static const IconData file_vue_thin = IconData(0xebc1, fontFamily: _fontFamily);
  static const IconData file_x_thin = IconData(0xebc2, fontFamily: _fontFamily);
  static const IconData file_xls_thin = IconData(0xebc3, fontFamily: _fontFamily);
  static const IconData file_zip_thin = IconData(0xebc4, fontFamily: _fontFamily);
  static const IconData files_thin = IconData(0xebc5, fontFamily: _fontFamily);
  static const IconData film_script_thin = IconData(0xebc6, fontFamily: _fontFamily);
  static const IconData film_slate_thin = IconData(0xebc7, fontFamily: _fontFamily);
  static const IconData film_strip_thin = IconData(0xebc8, fontFamily: _fontFamily);
  static const IconData fingerprint_simple_thin = IconData(0xebc9, fontFamily: _fontFamily);
  static const IconData fingerprint_thin = IconData(0xebca, fontFamily: _fontFamily);
  static const IconData finn_the_human_thin = IconData(0xebcb, fontFamily: _fontFamily);
  static const IconData fire_simple_thin = IconData(0xebcc, fontFamily: _fontFamily);
  static const IconData fire_thin = IconData(0xebcd, fontFamily: _fontFamily);
  static const IconData first_aid_kit_thin = IconData(0xebce, fontFamily: _fontFamily);
  static const IconData first_aid_thin = IconData(0xebcf, fontFamily: _fontFamily);
  static const IconData fish_simple_thin = IconData(0xebd0, fontFamily: _fontFamily);
  static const IconData fish_thin = IconData(0xebd1, fontFamily: _fontFamily);
  static const IconData flag_banner_thin = IconData(0xebd2, fontFamily: _fontFamily);
  static const IconData flag_checkered_thin = IconData(0xebd3, fontFamily: _fontFamily);
  static const IconData flag_thin = IconData(0xebd4, fontFamily: _fontFamily);
  static const IconData flame_thin = IconData(0xebd5, fontFamily: _fontFamily);
  static const IconData flashlight_thin = IconData(0xebd6, fontFamily: _fontFamily);
  static const IconData flask_thin = IconData(0xebd7, fontFamily: _fontFamily);
  static const IconData floppy_disk_back_thin = IconData(0xebd8, fontFamily: _fontFamily);
  static const IconData floppy_disk_thin = IconData(0xebd9, fontFamily: _fontFamily);
  static const IconData flow_arrow_thin = IconData(0xebda, fontFamily: _fontFamily);
  static const IconData flower_lotus_thin = IconData(0xebdb, fontFamily: _fontFamily);
  static const IconData flower_thin = IconData(0xebdc, fontFamily: _fontFamily);
  static const IconData flying_saucer_thin = IconData(0xebdd, fontFamily: _fontFamily);
  static const IconData folder_dotted_thin = IconData(0xebde, fontFamily: _fontFamily);
  static const IconData folder_lock_thin = IconData(0xebdf, fontFamily: _fontFamily);
  static const IconData folder_minus_thin = IconData(0xebe0, fontFamily: _fontFamily);
  static const IconData folder_notch_minus_thin = IconData(0xebe1, fontFamily: _fontFamily);
  static const IconData folder_notch_open_thin = IconData(0xebe2, fontFamily: _fontFamily);
  static const IconData folder_notch_plus_thin = IconData(0xebe3, fontFamily: _fontFamily);
  static const IconData folder_notch_thin = IconData(0xebe4, fontFamily: _fontFamily);
  static const IconData folder_open_thin = IconData(0xebe5, fontFamily: _fontFamily);
  static const IconData folder_plus_thin = IconData(0xebe6, fontFamily: _fontFamily);
  static const IconData folder_simple_dotted_thin = IconData(0xebe7, fontFamily: _fontFamily);
  static const IconData folder_simple_lock_thin = IconData(0xebe8, fontFamily: _fontFamily);
  static const IconData folder_simple_minus_thin = IconData(0xebe9, fontFamily: _fontFamily);
  static const IconData folder_simple_plus_thin = IconData(0xebea, fontFamily: _fontFamily);
  static const IconData folder_simple_star_thin = IconData(0xebeb, fontFamily: _fontFamily);
  static const IconData folder_simple_thin = IconData(0xebec, fontFamily: _fontFamily);
  static const IconData folder_simple_user_thin = IconData(0xebed, fontFamily: _fontFamily);
  static const IconData folder_star_thin = IconData(0xebee, fontFamily: _fontFamily);
  static const IconData folder_thin = IconData(0xebef, fontFamily: _fontFamily);
  static const IconData folder_user_thin = IconData(0xebf0, fontFamily: _fontFamily);
  static const IconData folders_thin = IconData(0xebf1, fontFamily: _fontFamily);
  static const IconData football_thin = IconData(0xebf2, fontFamily: _fontFamily);
  static const IconData fork_knife_thin = IconData(0xebf3, fontFamily: _fontFamily);
  static const IconData frame_corners_thin = IconData(0xebf4, fontFamily: _fontFamily);
  static const IconData framer_logo_thin = IconData(0xebf5, fontFamily: _fontFamily);
  static const IconData function_thin = IconData(0xebf6, fontFamily: _fontFamily);
  static const IconData funnel_simple_thin = IconData(0xebf7, fontFamily: _fontFamily);
  static const IconData funnel_thin = IconData(0xebf8, fontFamily: _fontFamily);
  static const IconData game_controller_thin = IconData(0xebf9, fontFamily: _fontFamily);
  static const IconData gas_pump_thin = IconData(0xebfa, fontFamily: _fontFamily);
  static const IconData gauge_thin = IconData(0xebfb, fontFamily: _fontFamily);
  static const IconData gear_six_thin = IconData(0xebfc, fontFamily: _fontFamily);
  static const IconData gear_thin = IconData(0xebfd, fontFamily: _fontFamily);
  static const IconData gender_female_thin = IconData(0xebfe, fontFamily: _fontFamily);
  static const IconData gender_intersex_thin = IconData(0xebff, fontFamily: _fontFamily);
  static const IconData gender_male_thin = IconData(0xec00, fontFamily: _fontFamily);
  static const IconData gender_neuter_thin = IconData(0xec01, fontFamily: _fontFamily);
  static const IconData gender_nonbinary_thin = IconData(0xec02, fontFamily: _fontFamily);
  static const IconData gender_transgender_thin = IconData(0xec03, fontFamily: _fontFamily);
  static const IconData ghost_thin = IconData(0xec04, fontFamily: _fontFamily);
  static const IconData gif_thin = IconData(0xec05, fontFamily: _fontFamily);
  static const IconData gift_thin = IconData(0xec06, fontFamily: _fontFamily);
  static const IconData git_branch_thin = IconData(0xec07, fontFamily: _fontFamily);
  static const IconData git_commit_thin = IconData(0xec08, fontFamily: _fontFamily);
  static const IconData git_diff_thin = IconData(0xec09, fontFamily: _fontFamily);
  static const IconData git_fork_thin = IconData(0xec0a, fontFamily: _fontFamily);
  static const IconData git_merge_thin = IconData(0xec0b, fontFamily: _fontFamily);
  static const IconData git_pull_request_thin = IconData(0xec0c, fontFamily: _fontFamily);
  static const IconData github_logo_thin = IconData(0xec0d, fontFamily: _fontFamily);
  static const IconData gitlab_logo_simple_thin = IconData(0xec0e, fontFamily: _fontFamily);
  static const IconData gitlab_logo_thin = IconData(0xec0f, fontFamily: _fontFamily);
  static const IconData globe_hemisphere_east_thin = IconData(0xec10, fontFamily: _fontFamily);
  static const IconData globe_hemisphere_west_thin = IconData(0xec11, fontFamily: _fontFamily);
  static const IconData globe_simple_thin = IconData(0xec12, fontFamily: _fontFamily);
  static const IconData globe_stand_thin = IconData(0xec13, fontFamily: _fontFamily);
  static const IconData globe_thin = IconData(0xec14, fontFamily: _fontFamily);
  static const IconData google_chrome_logo_thin = IconData(0xec15, fontFamily: _fontFamily);
  static const IconData google_logo_thin = IconData(0xec16, fontFamily: _fontFamily);
  static const IconData google_photos_logo_thin = IconData(0xec17, fontFamily: _fontFamily);
  static const IconData google_play_logo_thin = IconData(0xec18, fontFamily: _fontFamily);
  static const IconData google_podcasts_logo_thin = IconData(0xec19, fontFamily: _fontFamily);
  static const IconData gradient_thin = IconData(0xec1a, fontFamily: _fontFamily);
  static const IconData graduation_cap_thin = IconData(0xec1b, fontFamily: _fontFamily);
  static const IconData graph_thin = IconData(0xec1c, fontFamily: _fontFamily);
  static const IconData grid_four_thin = IconData(0xec1d, fontFamily: _fontFamily);
  static const IconData hamburger_thin = IconData(0xec1e, fontFamily: _fontFamily);
  static const IconData hand_eye_thin = IconData(0xec1f, fontFamily: _fontFamily);
  static const IconData hand_fist_thin = IconData(0xec20, fontFamily: _fontFamily);
  static const IconData hand_grabbing_thin = IconData(0xec21, fontFamily: _fontFamily);
  static const IconData hand_palm_thin = IconData(0xec22, fontFamily: _fontFamily);
  static const IconData hand_pointing_thin = IconData(0xec23, fontFamily: _fontFamily);
  static const IconData hand_soap_thin = IconData(0xec24, fontFamily: _fontFamily);
  static const IconData hand_thin = IconData(0xec25, fontFamily: _fontFamily);
  static const IconData hand_waving_thin = IconData(0xec26, fontFamily: _fontFamily);
  static const IconData handbag_simple_thin = IconData(0xec27, fontFamily: _fontFamily);
  static const IconData handbag_thin = IconData(0xec28, fontFamily: _fontFamily);
  static const IconData hands_clapping_thin = IconData(0xec29, fontFamily: _fontFamily);
  static const IconData handshake_thin = IconData(0xec2a, fontFamily: _fontFamily);
  static const IconData hard_drive_thin = IconData(0xec2b, fontFamily: _fontFamily);
  static const IconData hard_drives_thin = IconData(0xec2c, fontFamily: _fontFamily);
  static const IconData hash_straight_thin = IconData(0xec2d, fontFamily: _fontFamily);
  static const IconData hash_thin = IconData(0xec2e, fontFamily: _fontFamily);
  static const IconData headlights_thin = IconData(0xec2f, fontFamily: _fontFamily);
  static const IconData headphones_thin = IconData(0xec30, fontFamily: _fontFamily);
  static const IconData headset_thin = IconData(0xec31, fontFamily: _fontFamily);
  static const IconData heart_break_thin = IconData(0xec32, fontFamily: _fontFamily);
  static const IconData heart_straight_break_thin = IconData(0xec33, fontFamily: _fontFamily);
  static const IconData heart_straight_thin = IconData(0xec34, fontFamily: _fontFamily);
  static const IconData heart_thin = IconData(0xec35, fontFamily: _fontFamily);
  static const IconData heartbeat_thin = IconData(0xec36, fontFamily: _fontFamily);
  static const IconData hexagon_thin = IconData(0xec37, fontFamily: _fontFamily);
  static const IconData highlighter_circle_thin = IconData(0xec38, fontFamily: _fontFamily);
  static const IconData horse_thin = IconData(0xec39, fontFamily: _fontFamily);
  static const IconData hourglass_high_thin = IconData(0xec3a, fontFamily: _fontFamily);
  static const IconData hourglass_low_thin = IconData(0xec3b, fontFamily: _fontFamily);
  static const IconData hourglass_medium_thin = IconData(0xec3c, fontFamily: _fontFamily);
  static const IconData hourglass_simple_high_thin = IconData(0xec3d, fontFamily: _fontFamily);
  static const IconData hourglass_simple_low_thin = IconData(0xec3e, fontFamily: _fontFamily);
  static const IconData hourglass_simple_medium_thin = IconData(0xec3f, fontFamily: _fontFamily);
  static const IconData hourglass_simple_thin = IconData(0xec40, fontFamily: _fontFamily);
  static const IconData hourglass_thin = IconData(0xec41, fontFamily: _fontFamily);
  static const IconData house_line_thin = IconData(0xec42, fontFamily: _fontFamily);
  static const IconData house_simple_thin = IconData(0xec43, fontFamily: _fontFamily);
  static const IconData house_thin = IconData(0xec44, fontFamily: _fontFamily);
  static const IconData identification_badge_thin = IconData(0xec45, fontFamily: _fontFamily);
  static const IconData identification_card_thin = IconData(0xec46, fontFamily: _fontFamily);
  static const IconData image_square_thin = IconData(0xec47, fontFamily: _fontFamily);
  static const IconData image_thin = IconData(0xec48, fontFamily: _fontFamily);
  static const IconData infinity_thin = IconData(0xec49, fontFamily: _fontFamily);
  static const IconData info_thin = IconData(0xec4a, fontFamily: _fontFamily);
  static const IconData instagram_logo_thin = IconData(0xec4b, fontFamily: _fontFamily);
  static const IconData intersect_thin = IconData(0xec4c, fontFamily: _fontFamily);
  static const IconData jeep_thin = IconData(0xec4d, fontFamily: _fontFamily);
  static const IconData kanban_thin = IconData(0xec4e, fontFamily: _fontFamily);
  static const IconData key_return_thin = IconData(0xec4f, fontFamily: _fontFamily);
  static const IconData key_thin = IconData(0xec50, fontFamily: _fontFamily);
  static const IconData keyboard_thin = IconData(0xec51, fontFamily: _fontFamily);
  static const IconData keyhole_thin = IconData(0xec52, fontFamily: _fontFamily);
  static const IconData knife_thin = IconData(0xec53, fontFamily: _fontFamily);
  static const IconData ladder_simple_thin = IconData(0xec54, fontFamily: _fontFamily);
  static const IconData ladder_thin = IconData(0xec55, fontFamily: _fontFamily);
  static const IconData lamp_thin = IconData(0xec56, fontFamily: _fontFamily);
  static const IconData laptop_thin = IconData(0xec57, fontFamily: _fontFamily);
  static const IconData layout_thin = IconData(0xec58, fontFamily: _fontFamily);
  static const IconData leaf_thin = IconData(0xec59, fontFamily: _fontFamily);
  static const IconData lifebuoy_thin = IconData(0xec5a, fontFamily: _fontFamily);
  static const IconData lightbulb_filament_thin = IconData(0xec5b, fontFamily: _fontFamily);
  static const IconData lightbulb_thin = IconData(0xec5c, fontFamily: _fontFamily);
  static const IconData lightning_slash_thin = IconData(0xec5d, fontFamily: _fontFamily);
  static const IconData lightning_thin = IconData(0xec5e, fontFamily: _fontFamily);
  static const IconData line_segment_thin = IconData(0xec5f, fontFamily: _fontFamily);
  static const IconData line_segments_thin = IconData(0xec60, fontFamily: _fontFamily);
  static const IconData link_break_thin = IconData(0xec61, fontFamily: _fontFamily);
  static const IconData link_simple_break_thin = IconData(0xec62, fontFamily: _fontFamily);
  static const IconData link_simple_horizontal_break_thin = IconData(0xec63, fontFamily: _fontFamily);
  static const IconData link_simple_horizontal_thin = IconData(0xec64, fontFamily: _fontFamily);
  static const IconData link_simple_thin = IconData(0xec65, fontFamily: _fontFamily);
  static const IconData link_thin = IconData(0xec66, fontFamily: _fontFamily);
  static const IconData linkedin_logo_thin = IconData(0xec67, fontFamily: _fontFamily);
  static const IconData linux_logo_thin = IconData(0xec68, fontFamily: _fontFamily);
  static const IconData list_bullets_thin = IconData(0xec69, fontFamily: _fontFamily);
  static const IconData list_checks_thin = IconData(0xec6a, fontFamily: _fontFamily);
  static const IconData list_dashes_thin = IconData(0xec6b, fontFamily: _fontFamily);
  static const IconData list_numbers_thin = IconData(0xec6c, fontFamily: _fontFamily);
  static const IconData list_plus_thin = IconData(0xec6d, fontFamily: _fontFamily);
  static const IconData list_thin = IconData(0xec6e, fontFamily: _fontFamily);
  static const IconData lock_key_open_thin = IconData(0xec6f, fontFamily: _fontFamily);
  static const IconData lock_key_thin = IconData(0xec70, fontFamily: _fontFamily);
  static const IconData lock_laminated_open_thin = IconData(0xec71, fontFamily: _fontFamily);
  static const IconData lock_laminated_thin = IconData(0xec72, fontFamily: _fontFamily);
  static const IconData lock_open_thin = IconData(0xec73, fontFamily: _fontFamily);
  static const IconData lock_simple_open_thin = IconData(0xec74, fontFamily: _fontFamily);
  static const IconData lock_simple_thin = IconData(0xec75, fontFamily: _fontFamily);
  static const IconData lock_thin = IconData(0xec76, fontFamily: _fontFamily);
  static const IconData magic_wand_thin = IconData(0xec77, fontFamily: _fontFamily);
  static const IconData magnet_straight_thin = IconData(0xec78, fontFamily: _fontFamily);
  static const IconData magnet_thin = IconData(0xec79, fontFamily: _fontFamily);
  static const IconData magnifying_glass_minus_thin = IconData(0xec7a, fontFamily: _fontFamily);
  static const IconData magnifying_glass_plus_thin = IconData(0xec7b, fontFamily: _fontFamily);
  static const IconData magnifying_glass_thin = IconData(0xec7c, fontFamily: _fontFamily);
  static const IconData map_pin_line_thin = IconData(0xec7d, fontFamily: _fontFamily);
  static const IconData map_pin_thin = IconData(0xec7e, fontFamily: _fontFamily);
  static const IconData map_trifold_thin = IconData(0xec7f, fontFamily: _fontFamily);
  static const IconData marker_circle_thin = IconData(0xec80, fontFamily: _fontFamily);
  static const IconData martini_thin = IconData(0xec81, fontFamily: _fontFamily);
  static const IconData mask_happy_thin = IconData(0xec82, fontFamily: _fontFamily);
  static const IconData mask_sad_thin = IconData(0xec83, fontFamily: _fontFamily);
  static const IconData math_operations_thin = IconData(0xec84, fontFamily: _fontFamily);
  static const IconData medal_thin = IconData(0xec85, fontFamily: _fontFamily);
  static const IconData medium_logo_thin = IconData(0xec86, fontFamily: _fontFamily);
  static const IconData megaphone_simple_thin = IconData(0xec87, fontFamily: _fontFamily);
  static const IconData megaphone_thin = IconData(0xec88, fontFamily: _fontFamily);
  static const IconData messenger_logo_thin = IconData(0xec89, fontFamily: _fontFamily);
  static const IconData microphone_slash_thin = IconData(0xec8a, fontFamily: _fontFamily);
  static const IconData microphone_stage_thin = IconData(0xec8b, fontFamily: _fontFamily);
  static const IconData microphone_thin = IconData(0xec8c, fontFamily: _fontFamily);
  static const IconData microsoft_excel_logo_thin = IconData(0xec8d, fontFamily: _fontFamily);
  static const IconData microsoft_powerpoint_logo_thin = IconData(0xec8e, fontFamily: _fontFamily);
  static const IconData microsoft_teams_logo_thin = IconData(0xec8f, fontFamily: _fontFamily);
  static const IconData microsoft_word_logo_thin = IconData(0xec90, fontFamily: _fontFamily);
  static const IconData minus_circle_thin = IconData(0xec91, fontFamily: _fontFamily);
  static const IconData minus_thin = IconData(0xec92, fontFamily: _fontFamily);
  static const IconData money_thin = IconData(0xec93, fontFamily: _fontFamily);
  static const IconData monitor_play_thin = IconData(0xec94, fontFamily: _fontFamily);
  static const IconData monitor_thin = IconData(0xec95, fontFamily: _fontFamily);
  static const IconData moon_stars_thin = IconData(0xec96, fontFamily: _fontFamily);
  static const IconData moon_thin = IconData(0xec97, fontFamily: _fontFamily);
  static const IconData mountains_thin = IconData(0xec98, fontFamily: _fontFamily);
  static const IconData mouse_simple_thin = IconData(0xec99, fontFamily: _fontFamily);
  static const IconData mouse_thin = IconData(0xec9a, fontFamily: _fontFamily);
  static const IconData music_note_simple_thin = IconData(0xec9b, fontFamily: _fontFamily);
  static const IconData music_note_thin = IconData(0xec9c, fontFamily: _fontFamily);
  static const IconData music_notes_plus_thin = IconData(0xec9d, fontFamily: _fontFamily);
  static const IconData music_notes_simple_thin = IconData(0xec9e, fontFamily: _fontFamily);
  static const IconData music_notes_thin = IconData(0xec9f, fontFamily: _fontFamily);
  static const IconData navigation_arrow_thin = IconData(0xeca0, fontFamily: _fontFamily);
  static const IconData needle_thin = IconData(0xeca1, fontFamily: _fontFamily);
  static const IconData newspaper_clipping_thin = IconData(0xeca2, fontFamily: _fontFamily);
  static const IconData newspaper_thin = IconData(0xeca3, fontFamily: _fontFamily);
  static const IconData note_blank_thin = IconData(0xeca4, fontFamily: _fontFamily);
  static const IconData note_pencil_thin = IconData(0xeca5, fontFamily: _fontFamily);
  static const IconData note_thin = IconData(0xeca6, fontFamily: _fontFamily);
  static const IconData notebook_thin = IconData(0xeca7, fontFamily: _fontFamily);
  static const IconData notepad_thin = IconData(0xeca8, fontFamily: _fontFamily);
  static const IconData notification_thin = IconData(0xeca9, fontFamily: _fontFamily);
  static const IconData number_circle_eight_thin = IconData(0xecaa, fontFamily: _fontFamily);
  static const IconData number_circle_five_thin = IconData(0xecab, fontFamily: _fontFamily);
  static const IconData number_circle_four_thin = IconData(0xecac, fontFamily: _fontFamily);
  static const IconData number_circle_nine_thin = IconData(0xecad, fontFamily: _fontFamily);
  static const IconData number_circle_one_thin = IconData(0xecae, fontFamily: _fontFamily);
  static const IconData number_circle_seven_thin = IconData(0xecaf, fontFamily: _fontFamily);
  static const IconData number_circle_six_thin = IconData(0xecb0, fontFamily: _fontFamily);
  static const IconData number_circle_three_thin = IconData(0xecb1, fontFamily: _fontFamily);
  static const IconData number_circle_two_thin = IconData(0xecb2, fontFamily: _fontFamily);
  static const IconData number_circle_zero_thin = IconData(0xecb3, fontFamily: _fontFamily);
  static const IconData number_eight_thin = IconData(0xecb4, fontFamily: _fontFamily);
  static const IconData number_five_thin = IconData(0xecb5, fontFamily: _fontFamily);
  static const IconData number_four_thin = IconData(0xecb6, fontFamily: _fontFamily);
  static const IconData number_nine_thin = IconData(0xecb7, fontFamily: _fontFamily);
  static const IconData number_one_thin = IconData(0xecb8, fontFamily: _fontFamily);
  static const IconData number_seven_thin = IconData(0xecb9, fontFamily: _fontFamily);
  static const IconData number_six_thin = IconData(0xecba, fontFamily: _fontFamily);
  static const IconData number_square_eight_thin = IconData(0xecbb, fontFamily: _fontFamily);
  static const IconData number_square_five_thin = IconData(0xecbc, fontFamily: _fontFamily);
  static const IconData number_square_four_thin = IconData(0xecbd, fontFamily: _fontFamily);
  static const IconData number_square_nine_thin = IconData(0xecbe, fontFamily: _fontFamily);
  static const IconData number_square_one_thin = IconData(0xecbf, fontFamily: _fontFamily);
  static const IconData number_square_seven_thin = IconData(0xecc0, fontFamily: _fontFamily);
  static const IconData number_square_six_thin = IconData(0xecc1, fontFamily: _fontFamily);
  static const IconData number_square_three_thin = IconData(0xecc2, fontFamily: _fontFamily);
  static const IconData number_square_two_thin = IconData(0xecc3, fontFamily: _fontFamily);
  static const IconData number_square_zero_thin = IconData(0xecc4, fontFamily: _fontFamily);
  static const IconData number_three_thin = IconData(0xecc5, fontFamily: _fontFamily);
  static const IconData number_two_thin = IconData(0xecc6, fontFamily: _fontFamily);
  static const IconData number_zero_thin = IconData(0xecc7, fontFamily: _fontFamily);
  static const IconData nut_thin = IconData(0xecc8, fontFamily: _fontFamily);
  static const IconData ny_times_logo_thin = IconData(0xecc9, fontFamily: _fontFamily);
  static const IconData octagon_thin = IconData(0xecca, fontFamily: _fontFamily);
  static const IconData option_thin = IconData(0xeccb, fontFamily: _fontFamily);
  static const IconData package_thin = IconData(0xeccc, fontFamily: _fontFamily);
  static const IconData paint_brush_broad_thin = IconData(0xeccd, fontFamily: _fontFamily);
  static const IconData paint_brush_household_thin = IconData(0xecce, fontFamily: _fontFamily);
  static const IconData paint_brush_thin = IconData(0xeccf, fontFamily: _fontFamily);
  static const IconData paint_bucket_thin = IconData(0xecd0, fontFamily: _fontFamily);
  static const IconData paint_roller_thin = IconData(0xecd1, fontFamily: _fontFamily);
  static const IconData palette_thin = IconData(0xecd2, fontFamily: _fontFamily);
  static const IconData paper_plane_right_thin = IconData(0xecd3, fontFamily: _fontFamily);
  static const IconData paper_plane_thin = IconData(0xecd4, fontFamily: _fontFamily);
  static const IconData paper_plane_tilt_thin = IconData(0xecd5, fontFamily: _fontFamily);
  static const IconData paperclip_horizontal_thin = IconData(0xecd6, fontFamily: _fontFamily);
  static const IconData paperclip_thin = IconData(0xecd7, fontFamily: _fontFamily);
  static const IconData parachute_thin = IconData(0xecd8, fontFamily: _fontFamily);
  static const IconData password_thin = IconData(0xecd9, fontFamily: _fontFamily);
  static const IconData path_thin = IconData(0xecda, fontFamily: _fontFamily);
  static const IconData pause_circle_thin = IconData(0xecdb, fontFamily: _fontFamily);
  static const IconData pause_thin = IconData(0xecdc, fontFamily: _fontFamily);
  static const IconData paw_print_thin = IconData(0xecdd, fontFamily: _fontFamily);
  static const IconData peace_thin = IconData(0xecde, fontFamily: _fontFamily);
  static const IconData pen_nib_straight_thin = IconData(0xecdf, fontFamily: _fontFamily);
  static const IconData pen_nib_thin = IconData(0xece0, fontFamily: _fontFamily);
  static const IconData pen_thin = IconData(0xece1, fontFamily: _fontFamily);
  static const IconData pencil_circle_thin = IconData(0xece2, fontFamily: _fontFamily);
  static const IconData pencil_line_thin = IconData(0xece3, fontFamily: _fontFamily);
  static const IconData pencil_simple_line_thin = IconData(0xece4, fontFamily: _fontFamily);
  static const IconData pencil_simple_thin = IconData(0xece5, fontFamily: _fontFamily);
  static const IconData pencil_thin = IconData(0xece6, fontFamily: _fontFamily);
  static const IconData percent_thin = IconData(0xece7, fontFamily: _fontFamily);
  static const IconData person_simple_run_thin = IconData(0xece8, fontFamily: _fontFamily);
  static const IconData person_simple_thin = IconData(0xece9, fontFamily: _fontFamily);
  static const IconData person_simple_walk_thin = IconData(0xecea, fontFamily: _fontFamily);
  static const IconData person_thin = IconData(0xeceb, fontFamily: _fontFamily);
  static const IconData perspective_thin = IconData(0xecec, fontFamily: _fontFamily);
  static const IconData phone_call_thin = IconData(0xeced, fontFamily: _fontFamily);
  static const IconData phone_disconnect_thin = IconData(0xecee, fontFamily: _fontFamily);
  static const IconData phone_incoming_thin = IconData(0xecef, fontFamily: _fontFamily);
  static const IconData phone_outgoing_thin = IconData(0xecf0, fontFamily: _fontFamily);
  static const IconData phone_slash_thin = IconData(0xecf1, fontFamily: _fontFamily);
  static const IconData phone_thin = IconData(0xecf2, fontFamily: _fontFamily);
  static const IconData phone_x_thin = IconData(0xecf3, fontFamily: _fontFamily);
  static const IconData phosphor_logo_thin = IconData(0xecf4, fontFamily: _fontFamily);
  static const IconData piano_keys_thin = IconData(0xecf5, fontFamily: _fontFamily);
  static const IconData picture_in_picture_thin = IconData(0xecf6, fontFamily: _fontFamily);
  static const IconData pill_thin = IconData(0xecf7, fontFamily: _fontFamily);
  static const IconData pinterest_logo_thin = IconData(0xecf8, fontFamily: _fontFamily);
  static const IconData pinwheel_thin = IconData(0xecf9, fontFamily: _fontFamily);
  static const IconData pizza_thin = IconData(0xecfa, fontFamily: _fontFamily);
  static const IconData placeholder_thin = IconData(0xecfb, fontFamily: _fontFamily);
  static const IconData planet_thin = IconData(0xecfc, fontFamily: _fontFamily);
  static const IconData play_circle_thin = IconData(0xecfd, fontFamily: _fontFamily);
  static const IconData play_thin = IconData(0xecfe, fontFamily: _fontFamily);
  static const IconData playlist_thin = IconData(0xecff, fontFamily: _fontFamily);
  static const IconData plug_thin = IconData(0xed00, fontFamily: _fontFamily);
  static const IconData plugs_connected_thin = IconData(0xed01, fontFamily: _fontFamily);
  static const IconData plugs_thin = IconData(0xed02, fontFamily: _fontFamily);
  static const IconData plus_circle_thin = IconData(0xed03, fontFamily: _fontFamily);
  static const IconData plus_minus_thin = IconData(0xed04, fontFamily: _fontFamily);
  static const IconData plus_thin = IconData(0xed05, fontFamily: _fontFamily);
  static const IconData poker_chip_thin = IconData(0xed06, fontFamily: _fontFamily);
  static const IconData police_car_thin = IconData(0xed07, fontFamily: _fontFamily);
  static const IconData polygon_thin = IconData(0xed08, fontFamily: _fontFamily);
  static const IconData popcorn_thin = IconData(0xed09, fontFamily: _fontFamily);
  static const IconData power_thin = IconData(0xed0a, fontFamily: _fontFamily);
  static const IconData prescription_thin = IconData(0xed0b, fontFamily: _fontFamily);
  static const IconData presentation_chart_thin = IconData(0xed0c, fontFamily: _fontFamily);
  static const IconData presentation_thin = IconData(0xed0d, fontFamily: _fontFamily);
  static const IconData printer_thin = IconData(0xed0e, fontFamily: _fontFamily);
  static const IconData prohibit_inset_thin = IconData(0xed0f, fontFamily: _fontFamily);
  static const IconData prohibit_thin = IconData(0xed10, fontFamily: _fontFamily);
  static const IconData projector_screen_chart_thin = IconData(0xed11, fontFamily: _fontFamily);
  static const IconData projector_screen_thin = IconData(0xed12, fontFamily: _fontFamily);
  static const IconData push_pin_simple_slash_thin = IconData(0xed13, fontFamily: _fontFamily);
  static const IconData push_pin_simple_thin = IconData(0xed14, fontFamily: _fontFamily);
  static const IconData push_pin_slash_thin = IconData(0xed15, fontFamily: _fontFamily);
  static const IconData push_pin_thin = IconData(0xed16, fontFamily: _fontFamily);
  static const IconData puzzle_piece_thin = IconData(0xed17, fontFamily: _fontFamily);
  static const IconData qr_code_thin = IconData(0xed18, fontFamily: _fontFamily);
  static const IconData question_thin = IconData(0xed19, fontFamily: _fontFamily);
  static const IconData queue_thin = IconData(0xed1a, fontFamily: _fontFamily);
  static const IconData quotes_thin = IconData(0xed1b, fontFamily: _fontFamily);
  static const IconData radical_thin = IconData(0xed1c, fontFamily: _fontFamily);
  static const IconData radio_button_thin = IconData(0xed1d, fontFamily: _fontFamily);
  static const IconData radio_thin = IconData(0xed1e, fontFamily: _fontFamily);
  static const IconData rainbow_cloud_thin = IconData(0xed1f, fontFamily: _fontFamily);
  static const IconData rainbow_thin = IconData(0xed20, fontFamily: _fontFamily);
  static const IconData receipt_thin = IconData(0xed21, fontFamily: _fontFamily);
  static const IconData record_thin = IconData(0xed22, fontFamily: _fontFamily);
  static const IconData rectangle_thin = IconData(0xed23, fontFamily: _fontFamily);
  static const IconData recycle_thin = IconData(0xed24, fontFamily: _fontFamily);
  static const IconData reddit_logo_thin = IconData(0xed25, fontFamily: _fontFamily);
  static const IconData repeat_once_thin = IconData(0xed26, fontFamily: _fontFamily);
  static const IconData repeat_thin = IconData(0xed27, fontFamily: _fontFamily);
  static const IconData rewind_circle_thin = IconData(0xed28, fontFamily: _fontFamily);
  static const IconData rewind_thin = IconData(0xed29, fontFamily: _fontFamily);
  static const IconData robot_thin = IconData(0xed2a, fontFamily: _fontFamily);
  static const IconData rocket_launch_thin = IconData(0xed2b, fontFamily: _fontFamily);
  static const IconData rocket_thin = IconData(0xed2c, fontFamily: _fontFamily);
  static const IconData rows_thin = IconData(0xed2d, fontFamily: _fontFamily);
  static const IconData rss_simple_thin = IconData(0xed2e, fontFamily: _fontFamily);
  static const IconData rss_thin = IconData(0xed2f, fontFamily: _fontFamily);
  static const IconData rug_thin = IconData(0xed30, fontFamily: _fontFamily);
  static const IconData ruler_thin = IconData(0xed31, fontFamily: _fontFamily);
  static const IconData scales_thin = IconData(0xed32, fontFamily: _fontFamily);
  static const IconData scan_thin = IconData(0xed33, fontFamily: _fontFamily);
  static const IconData scissors_thin = IconData(0xed34, fontFamily: _fontFamily);
  static const IconData screencast_thin = IconData(0xed35, fontFamily: _fontFamily);
  static const IconData scribble_loop_thin = IconData(0xed36, fontFamily: _fontFamily);
  static const IconData scroll_thin = IconData(0xed37, fontFamily: _fontFamily);
  static const IconData selection_all_thin = IconData(0xed38, fontFamily: _fontFamily);
  static const IconData selection_background_thin = IconData(0xed39, fontFamily: _fontFamily);
  static const IconData selection_foreground_thin = IconData(0xed3a, fontFamily: _fontFamily);
  static const IconData selection_inverse_thin = IconData(0xed3b, fontFamily: _fontFamily);
  static const IconData selection_plus_thin = IconData(0xed3c, fontFamily: _fontFamily);
  static const IconData selection_slash_thin = IconData(0xed3d, fontFamily: _fontFamily);
  static const IconData selection_thin = IconData(0xed3e, fontFamily: _fontFamily);
  static const IconData share_network_thin = IconData(0xed3f, fontFamily: _fontFamily);
  static const IconData share_thin = IconData(0xed40, fontFamily: _fontFamily);
  static const IconData shield_check_thin = IconData(0xed41, fontFamily: _fontFamily);
  static const IconData shield_checkered_thin = IconData(0xed42, fontFamily: _fontFamily);
  static const IconData shield_chevron_thin = IconData(0xed43, fontFamily: _fontFamily);
  static const IconData shield_plus_thin = IconData(0xed44, fontFamily: _fontFamily);
  static const IconData shield_slash_thin = IconData(0xed45, fontFamily: _fontFamily);
  static const IconData shield_star_thin = IconData(0xed46, fontFamily: _fontFamily);
  static const IconData shield_thin = IconData(0xed47, fontFamily: _fontFamily);
  static const IconData shield_warning_thin = IconData(0xed48, fontFamily: _fontFamily);
  static const IconData shopping_bag_open_thin = IconData(0xed49, fontFamily: _fontFamily);
  static const IconData shopping_bag_thin = IconData(0xed4a, fontFamily: _fontFamily);
  static const IconData shopping_cart_simple_thin = IconData(0xed4b, fontFamily: _fontFamily);
  static const IconData shopping_cart_thin = IconData(0xed4c, fontFamily: _fontFamily);
  static const IconData shower_thin = IconData(0xed4d, fontFamily: _fontFamily);
  static const IconData shuffle_angular_thin = IconData(0xed4e, fontFamily: _fontFamily);
  static const IconData shuffle_simple_thin = IconData(0xed4f, fontFamily: _fontFamily);
  static const IconData shuffle_thin = IconData(0xed50, fontFamily: _fontFamily);
  static const IconData sidebar_simple_thin = IconData(0xed51, fontFamily: _fontFamily);
  static const IconData sidebar_thin = IconData(0xed52, fontFamily: _fontFamily);
  static const IconData sign_in_thin = IconData(0xed53, fontFamily: _fontFamily);
  static const IconData sign_out_thin = IconData(0xed54, fontFamily: _fontFamily);
  static const IconData signpost_thin = IconData(0xed55, fontFamily: _fontFamily);
  static const IconData sim_card_thin = IconData(0xed56, fontFamily: _fontFamily);
  static const IconData sketch_logo_thin = IconData(0xed57, fontFamily: _fontFamily);
  static const IconData skip_back_circle_thin = IconData(0xed58, fontFamily: _fontFamily);
  static const IconData skip_back_thin = IconData(0xed59, fontFamily: _fontFamily);
  static const IconData skip_forward_circle_thin = IconData(0xed5a, fontFamily: _fontFamily);
  static const IconData skip_forward_thin = IconData(0xed5b, fontFamily: _fontFamily);
  static const IconData skull_thin = IconData(0xed5c, fontFamily: _fontFamily);
  static const IconData slack_logo_thin = IconData(0xed5d, fontFamily: _fontFamily);
  static const IconData sliders_horizontal_thin = IconData(0xed5e, fontFamily: _fontFamily);
  static const IconData sliders_thin = IconData(0xed5f, fontFamily: _fontFamily);
  static const IconData smiley_blank_thin = IconData(0xed60, fontFamily: _fontFamily);
  static const IconData smiley_meh_thin = IconData(0xed61, fontFamily: _fontFamily);
  static const IconData smiley_nervous_thin = IconData(0xed62, fontFamily: _fontFamily);
  static const IconData smiley_sad_thin = IconData(0xed63, fontFamily: _fontFamily);
  static const IconData smiley_sticker_thin = IconData(0xed64, fontFamily: _fontFamily);
  static const IconData smiley_thin = IconData(0xed65, fontFamily: _fontFamily);
  static const IconData smiley_wink_thin = IconData(0xed66, fontFamily: _fontFamily);
  static const IconData smiley_x_eyes_thin = IconData(0xed67, fontFamily: _fontFamily);
  static const IconData snapchat_logo_thin = IconData(0xed68, fontFamily: _fontFamily);
  static const IconData snowflake_thin = IconData(0xed69, fontFamily: _fontFamily);
  static const IconData soccer_ball_thin = IconData(0xed6a, fontFamily: _fontFamily);
  static const IconData sort_ascending_thin = IconData(0xed6b, fontFamily: _fontFamily);
  static const IconData sort_descending_thin = IconData(0xed6c, fontFamily: _fontFamily);
  static const IconData spade_thin = IconData(0xed6d, fontFamily: _fontFamily);
  static const IconData sparkle_thin = IconData(0xed6e, fontFamily: _fontFamily);
  static const IconData speaker_high_thin = IconData(0xed6f, fontFamily: _fontFamily);
  static const IconData speaker_low_thin = IconData(0xed70, fontFamily: _fontFamily);
  static const IconData speaker_none_thin = IconData(0xed71, fontFamily: _fontFamily);
  static const IconData speaker_simple_high_thin = IconData(0xed72, fontFamily: _fontFamily);
  static const IconData speaker_simple_low_thin = IconData(0xed73, fontFamily: _fontFamily);
  static const IconData speaker_simple_none_thin = IconData(0xed74, fontFamily: _fontFamily);
  static const IconData speaker_simple_slash_thin = IconData(0xed75, fontFamily: _fontFamily);
  static const IconData speaker_simple_x_thin = IconData(0xed76, fontFamily: _fontFamily);
  static const IconData speaker_slash_thin = IconData(0xed77, fontFamily: _fontFamily);
  static const IconData speaker_x_thin = IconData(0xed78, fontFamily: _fontFamily);
  static const IconData spinner_gap_thin = IconData(0xed79, fontFamily: _fontFamily);
  static const IconData spinner_thin = IconData(0xed7a, fontFamily: _fontFamily);
  static const IconData spiral_thin = IconData(0xed7b, fontFamily: _fontFamily);
  static const IconData spotify_logo_thin = IconData(0xed7c, fontFamily: _fontFamily);
  static const IconData square_half_bottom_thin = IconData(0xed7d, fontFamily: _fontFamily);
  static const IconData square_half_thin = IconData(0xed7e, fontFamily: _fontFamily);
  static const IconData square_logo_thin = IconData(0xed7f, fontFamily: _fontFamily);
  static const IconData square_thin = IconData(0xed80, fontFamily: _fontFamily);
  static const IconData squares_four_thin = IconData(0xed81, fontFamily: _fontFamily);
  static const IconData stack_overflow_logo_thin = IconData(0xed82, fontFamily: _fontFamily);
  static const IconData stack_simple_thin = IconData(0xed83, fontFamily: _fontFamily);
  static const IconData stack_thin = IconData(0xed84, fontFamily: _fontFamily);
  static const IconData stamp_thin = IconData(0xed85, fontFamily: _fontFamily);
  static const IconData star_four_thin = IconData(0xed86, fontFamily: _fontFamily);
  static const IconData star_half_thin = IconData(0xed87, fontFamily: _fontFamily);
  static const IconData star_thin = IconData(0xed88, fontFamily: _fontFamily);
  static const IconData sticker_thin = IconData(0xed89, fontFamily: _fontFamily);
  static const IconData stop_circle_thin = IconData(0xed8a, fontFamily: _fontFamily);
  static const IconData stop_thin = IconData(0xed8b, fontFamily: _fontFamily);
  static const IconData storefront_thin = IconData(0xed8c, fontFamily: _fontFamily);
  static const IconData strategy_thin = IconData(0xed8d, fontFamily: _fontFamily);
  static const IconData stripe_logo_thin = IconData(0xed8e, fontFamily: _fontFamily);
  static const IconData student_thin = IconData(0xed8f, fontFamily: _fontFamily);
  static const IconData suitcase_simple_thin = IconData(0xed90, fontFamily: _fontFamily);
  static const IconData suitcase_thin = IconData(0xed91, fontFamily: _fontFamily);
  static const IconData sun_dim_thin = IconData(0xed92, fontFamily: _fontFamily);
  static const IconData sun_horizon_thin = IconData(0xed93, fontFamily: _fontFamily);
  static const IconData sun_thin = IconData(0xed94, fontFamily: _fontFamily);
  static const IconData sunglasses_thin = IconData(0xed95, fontFamily: _fontFamily);
  static const IconData swap_thin = IconData(0xed96, fontFamily: _fontFamily);
  static const IconData swatches_thin = IconData(0xed97, fontFamily: _fontFamily);
  static const IconData sword_thin = IconData(0xed98, fontFamily: _fontFamily);
  static const IconData syringe_thin = IconData(0xed99, fontFamily: _fontFamily);
  static const IconData t_shirt_thin = IconData(0xed9a, fontFamily: _fontFamily);
  static const IconData table_thin = IconData(0xed9b, fontFamily: _fontFamily);
  static const IconData tabs_thin = IconData(0xed9c, fontFamily: _fontFamily);
  static const IconData tag_chevron_thin = IconData(0xed9d, fontFamily: _fontFamily);
  static const IconData tag_simple_thin = IconData(0xed9e, fontFamily: _fontFamily);
  static const IconData tag_thin = IconData(0xed9f, fontFamily: _fontFamily);
  static const IconData target_thin = IconData(0xeda0, fontFamily: _fontFamily);
  static const IconData taxi_thin = IconData(0xeda1, fontFamily: _fontFamily);
  static const IconData telegram_logo_thin = IconData(0xeda2, fontFamily: _fontFamily);
  static const IconData television_simple_thin = IconData(0xeda3, fontFamily: _fontFamily);
  static const IconData television_thin = IconData(0xeda4, fontFamily: _fontFamily);
  static const IconData tennis_ball_thin = IconData(0xeda5, fontFamily: _fontFamily);
  static const IconData terminal_thin = IconData(0xeda6, fontFamily: _fontFamily);
  static const IconData terminal_window_thin = IconData(0xeda7, fontFamily: _fontFamily);
  static const IconData test_tube_thin = IconData(0xeda8, fontFamily: _fontFamily);
  static const IconData text_aa_thin = IconData(0xeda9, fontFamily: _fontFamily);
  static const IconData text_align_center_thin = IconData(0xedaa, fontFamily: _fontFamily);
  static const IconData text_align_justify_thin = IconData(0xedab, fontFamily: _fontFamily);
  static const IconData text_align_left_thin = IconData(0xedac, fontFamily: _fontFamily);
  static const IconData text_align_right_thin = IconData(0xedad, fontFamily: _fontFamily);
  static const IconData text_bolder_thin = IconData(0xedae, fontFamily: _fontFamily);
  static const IconData text_h_five_thin = IconData(0xedaf, fontFamily: _fontFamily);
  static const IconData text_h_four_thin = IconData(0xedb0, fontFamily: _fontFamily);
  static const IconData text_h_one_thin = IconData(0xedb1, fontFamily: _fontFamily);
  static const IconData text_h_six_thin = IconData(0xedb2, fontFamily: _fontFamily);
  static const IconData text_h_thin = IconData(0xedb3, fontFamily: _fontFamily);
  static const IconData text_h_three_thin = IconData(0xedb4, fontFamily: _fontFamily);
  static const IconData text_h_two_thin = IconData(0xedb5, fontFamily: _fontFamily);
  static const IconData text_indent_thin = IconData(0xedb6, fontFamily: _fontFamily);
  static const IconData text_italic_thin = IconData(0xedb7, fontFamily: _fontFamily);
  static const IconData text_outdent_thin = IconData(0xedb8, fontFamily: _fontFamily);
  static const IconData text_strikethrough_thin = IconData(0xedb9, fontFamily: _fontFamily);
  static const IconData text_t_thin = IconData(0xedba, fontFamily: _fontFamily);
  static const IconData text_underline_thin = IconData(0xedbb, fontFamily: _fontFamily);
  static const IconData textbox_thin = IconData(0xedbc, fontFamily: _fontFamily);
  static const IconData thermometer_cold_thin = IconData(0xedbd, fontFamily: _fontFamily);
  static const IconData thermometer_hot_thin = IconData(0xedbe, fontFamily: _fontFamily);
  static const IconData thermometer_simple_thin = IconData(0xedbf, fontFamily: _fontFamily);
  static const IconData thermometer_thin = IconData(0xedc0, fontFamily: _fontFamily);
  static const IconData thumbs_down_thin = IconData(0xedc1, fontFamily: _fontFamily);
  static const IconData thumbs_up_thin = IconData(0xedc2, fontFamily: _fontFamily);
  static const IconData ticket_thin = IconData(0xedc3, fontFamily: _fontFamily);
  static const IconData tiktok_logo_thin = IconData(0xedc4, fontFamily: _fontFamily);
  static const IconData timer_thin = IconData(0xedc5, fontFamily: _fontFamily);
  static const IconData toggle_left_thin = IconData(0xedc6, fontFamily: _fontFamily);
  static const IconData toggle_right_thin = IconData(0xedc7, fontFamily: _fontFamily);
  static const IconData toilet_paper_thin = IconData(0xedc8, fontFamily: _fontFamily);
  static const IconData toilet_thin = IconData(0xedc9, fontFamily: _fontFamily);
  static const IconData tote_simple_thin = IconData(0xedca, fontFamily: _fontFamily);
  static const IconData tote_thin = IconData(0xedcb, fontFamily: _fontFamily);
  static const IconData trademark_registered_thin = IconData(0xedcc, fontFamily: _fontFamily);
  static const IconData traffic_cone_thin = IconData(0xedcd, fontFamily: _fontFamily);
  static const IconData traffic_sign_thin = IconData(0xedce, fontFamily: _fontFamily);
  static const IconData traffic_signal_thin = IconData(0xedcf, fontFamily: _fontFamily);
  static const IconData train_regional_thin = IconData(0xedd0, fontFamily: _fontFamily);
  static const IconData train_simple_thin = IconData(0xedd1, fontFamily: _fontFamily);
  static const IconData train_thin = IconData(0xedd2, fontFamily: _fontFamily);
  static const IconData translate_thin = IconData(0xedd3, fontFamily: _fontFamily);
  static const IconData trash_simple_thin = IconData(0xedd4, fontFamily: _fontFamily);
  static const IconData trash_thin = IconData(0xedd5, fontFamily: _fontFamily);
  static const IconData tray_thin = IconData(0xedd6, fontFamily: _fontFamily);
  static const IconData tree_evergreen_thin = IconData(0xedd7, fontFamily: _fontFamily);
  static const IconData tree_structure_thin = IconData(0xedd8, fontFamily: _fontFamily);
  static const IconData tree_thin = IconData(0xedd9, fontFamily: _fontFamily);
  static const IconData trend_down_thin = IconData(0xedda, fontFamily: _fontFamily);
  static const IconData trend_up_thin = IconData(0xeddb, fontFamily: _fontFamily);
  static const IconData triangle_thin = IconData(0xeddc, fontFamily: _fontFamily);
  static const IconData trophy_thin = IconData(0xeddd, fontFamily: _fontFamily);
  static const IconData truck_thin = IconData(0xedde, fontFamily: _fontFamily);
  static const IconData twitch_logo_thin = IconData(0xeddf, fontFamily: _fontFamily);
  static const IconData twitter_logo_thin = IconData(0xede0, fontFamily: _fontFamily);
  static const IconData umbrella_simple_thin = IconData(0xede1, fontFamily: _fontFamily);
  static const IconData umbrella_thin = IconData(0xede2, fontFamily: _fontFamily);
  static const IconData upload_simple_thin = IconData(0xede3, fontFamily: _fontFamily);
  static const IconData upload_thin = IconData(0xede4, fontFamily: _fontFamily);
  static const IconData user_circle_gear_thin = IconData(0xede5, fontFamily: _fontFamily);
  static const IconData user_circle_minus_thin = IconData(0xede6, fontFamily: _fontFamily);
  static const IconData user_circle_plus_thin = IconData(0xede7, fontFamily: _fontFamily);
  static const IconData user_circle_thin = IconData(0xede8, fontFamily: _fontFamily);
  static const IconData user_focus_thin = IconData(0xede9, fontFamily: _fontFamily);
  static const IconData user_gear_thin = IconData(0xedea, fontFamily: _fontFamily);
  static const IconData user_list_thin = IconData(0xedeb, fontFamily: _fontFamily);
  static const IconData user_minus_thin = IconData(0xedec, fontFamily: _fontFamily);
  static const IconData user_plus_thin = IconData(0xeded, fontFamily: _fontFamily);
  static const IconData user_rectangle_thin = IconData(0xedee, fontFamily: _fontFamily);
  static const IconData user_square_thin = IconData(0xedef, fontFamily: _fontFamily);
  static const IconData user_switch_thin = IconData(0xedf0, fontFamily: _fontFamily);
  static const IconData user_thin = IconData(0xedf1, fontFamily: _fontFamily);
  static const IconData users_four_thin = IconData(0xedf2, fontFamily: _fontFamily);
  static const IconData users_thin = IconData(0xedf3, fontFamily: _fontFamily);
  static const IconData users_three_thin = IconData(0xedf4, fontFamily: _fontFamily);
  static const IconData vault_thin = IconData(0xedf5, fontFamily: _fontFamily);
  static const IconData vibrate_thin = IconData(0xedf6, fontFamily: _fontFamily);
  static const IconData video_camera_slash_thin = IconData(0xedf7, fontFamily: _fontFamily);
  static const IconData video_camera_thin = IconData(0xedf8, fontFamily: _fontFamily);
  static const IconData vignette_thin = IconData(0xedf9, fontFamily: _fontFamily);
  static const IconData voicemail_thin = IconData(0xedfa, fontFamily: _fontFamily);
  static const IconData volleyball_thin = IconData(0xedfb, fontFamily: _fontFamily);
  static const IconData wall_thin = IconData(0xedfc, fontFamily: _fontFamily);
  static const IconData wallet_thin = IconData(0xedfd, fontFamily: _fontFamily);
  static const IconData warning_circle_thin = IconData(0xedfe, fontFamily: _fontFamily);
  static const IconData warning_octagon_thin = IconData(0xedff, fontFamily: _fontFamily);
  static const IconData warning_thin = IconData(0xee00, fontFamily: _fontFamily);
  static const IconData watch_thin = IconData(0xee01, fontFamily: _fontFamily);
  static const IconData wave_sawtooth_thin = IconData(0xee02, fontFamily: _fontFamily);
  static const IconData wave_sine_thin = IconData(0xee03, fontFamily: _fontFamily);
  static const IconData wave_square_thin = IconData(0xee04, fontFamily: _fontFamily);
  static const IconData wave_triangle_thin = IconData(0xee05, fontFamily: _fontFamily);
  static const IconData waves_thin = IconData(0xee06, fontFamily: _fontFamily);
  static const IconData webcam_thin = IconData(0xee07, fontFamily: _fontFamily);
  static const IconData whatsapp_logo_thin = IconData(0xee08, fontFamily: _fontFamily);
  static const IconData wheelchair_thin = IconData(0xee09, fontFamily: _fontFamily);
  static const IconData wifi_high_thin = IconData(0xee0a, fontFamily: _fontFamily);
  static const IconData wifi_low_thin = IconData(0xee0b, fontFamily: _fontFamily);
  static const IconData wifi_medium_thin = IconData(0xee0c, fontFamily: _fontFamily);
  static const IconData wifi_none_thin = IconData(0xee0d, fontFamily: _fontFamily);
  static const IconData wifi_slash_thin = IconData(0xee0e, fontFamily: _fontFamily);
  static const IconData wifi_x_thin = IconData(0xee0f, fontFamily: _fontFamily);
  static const IconData wind_thin = IconData(0xee10, fontFamily: _fontFamily);
  static const IconData windows_logo_thin = IconData(0xee11, fontFamily: _fontFamily);
  static const IconData wine_thin = IconData(0xee12, fontFamily: _fontFamily);
  static const IconData wrench_thin = IconData(0xee13, fontFamily: _fontFamily);
  static const IconData x_circle_thin = IconData(0xee14, fontFamily: _fontFamily);
  static const IconData x_square_thin = IconData(0xee15, fontFamily: _fontFamily);
  static const IconData x_thin = IconData(0xee16, fontFamily: _fontFamily);
  static const IconData yin_yang_thin = IconData(0xee17, fontFamily: _fontFamily);
  static const IconData youtube_logo_thin = IconData(0xee18, fontFamily: _fontFamily);
  static const IconData activity_light = IconData(0xee19, fontFamily: _fontFamily);
  static const IconData address_book_light = IconData(0xee1a, fontFamily: _fontFamily);
  static const IconData airplane_in_flight_light = IconData(0xee1b, fontFamily: _fontFamily);
  static const IconData airplane_landing_light = IconData(0xee1c, fontFamily: _fontFamily);
  static const IconData airplane_light = IconData(0xee1d, fontFamily: _fontFamily);
  static const IconData airplane_takeoff_light = IconData(0xee1e, fontFamily: _fontFamily);
  static const IconData airplane_tilt_light = IconData(0xee1f, fontFamily: _fontFamily);
  static const IconData airplay_light = IconData(0xee20, fontFamily: _fontFamily);
  static const IconData alarm_light = IconData(0xee21, fontFamily: _fontFamily);
  static const IconData alien_light = IconData(0xee22, fontFamily: _fontFamily);
  static const IconData align_bottom_light = IconData(0xee23, fontFamily: _fontFamily);
  static const IconData align_bottom_simple_light = IconData(0xee24, fontFamily: _fontFamily);
  static const IconData align_center_horizontal_light = IconData(0xee25, fontFamily: _fontFamily);
  static const IconData align_center_horizontal_simple_light = IconData(0xee26, fontFamily: _fontFamily);
  static const IconData align_center_vertical_light = IconData(0xee27, fontFamily: _fontFamily);
  static const IconData align_center_vertical_simple_light = IconData(0xee28, fontFamily: _fontFamily);
  static const IconData align_left_light = IconData(0xee29, fontFamily: _fontFamily);
  static const IconData align_left_simple_light = IconData(0xee2a, fontFamily: _fontFamily);
  static const IconData align_right_light = IconData(0xee2b, fontFamily: _fontFamily);
  static const IconData align_right_simple_light = IconData(0xee2c, fontFamily: _fontFamily);
  static const IconData align_top_light = IconData(0xee2d, fontFamily: _fontFamily);
  static const IconData align_top_simple_light = IconData(0xee2e, fontFamily: _fontFamily);
  static const IconData anchor_light = IconData(0xee2f, fontFamily: _fontFamily);
  static const IconData anchor_simple_light = IconData(0xee30, fontFamily: _fontFamily);
  static const IconData android_logo_light = IconData(0xee31, fontFamily: _fontFamily);
  static const IconData angular_logo_light = IconData(0xee32, fontFamily: _fontFamily);
  static const IconData aperture_light = IconData(0xee33, fontFamily: _fontFamily);
  static const IconData app_store_logo_light = IconData(0xee34, fontFamily: _fontFamily);
  static const IconData app_window_light = IconData(0xee35, fontFamily: _fontFamily);
  static const IconData apple_logo_light = IconData(0xee36, fontFamily: _fontFamily);
  static const IconData apple_podcasts_logo_light = IconData(0xee37, fontFamily: _fontFamily);
  static const IconData archive_box_light = IconData(0xee38, fontFamily: _fontFamily);
  static const IconData archive_light = IconData(0xee39, fontFamily: _fontFamily);
  static const IconData archive_tray_light = IconData(0xee3a, fontFamily: _fontFamily);
  static const IconData armchair_light = IconData(0xee3b, fontFamily: _fontFamily);
  static const IconData arrow_arc_left_light = IconData(0xee3c, fontFamily: _fontFamily);
  static const IconData arrow_arc_right_light = IconData(0xee3d, fontFamily: _fontFamily);
  static const IconData arrow_bend_double_up_left_light = IconData(0xee3e, fontFamily: _fontFamily);
  static const IconData arrow_bend_double_up_right_light = IconData(0xee3f, fontFamily: _fontFamily);
  static const IconData arrow_bend_down_left_light = IconData(0xee40, fontFamily: _fontFamily);
  static const IconData arrow_bend_down_right_light = IconData(0xee41, fontFamily: _fontFamily);
  static const IconData arrow_bend_left_down_light = IconData(0xee42, fontFamily: _fontFamily);
  static const IconData arrow_bend_left_up_light = IconData(0xee43, fontFamily: _fontFamily);
  static const IconData arrow_bend_right_down_light = IconData(0xee44, fontFamily: _fontFamily);
  static const IconData arrow_bend_right_up_light = IconData(0xee45, fontFamily: _fontFamily);
  static const IconData arrow_bend_up_left_light = IconData(0xee46, fontFamily: _fontFamily);
  static const IconData arrow_bend_up_right_light = IconData(0xee47, fontFamily: _fontFamily);
  static const IconData arrow_circle_down_left_light = IconData(0xee48, fontFamily: _fontFamily);
  static const IconData arrow_circle_down_light = IconData(0xee49, fontFamily: _fontFamily);
  static const IconData arrow_circle_down_right_light = IconData(0xee4a, fontFamily: _fontFamily);
  static const IconData arrow_circle_left_light = IconData(0xee4b, fontFamily: _fontFamily);
  static const IconData arrow_circle_right_light = IconData(0xee4c, fontFamily: _fontFamily);
  static const IconData arrow_circle_up_left_light = IconData(0xee4d, fontFamily: _fontFamily);
  static const IconData arrow_circle_up_light = IconData(0xee4e, fontFamily: _fontFamily);
  static const IconData arrow_circle_up_right_light = IconData(0xee4f, fontFamily: _fontFamily);
  static const IconData arrow_clockwise_light = IconData(0xee50, fontFamily: _fontFamily);
  static const IconData arrow_counter_clockwise_light = IconData(0xee51, fontFamily: _fontFamily);
  static const IconData arrow_down_left_light = IconData(0xee52, fontFamily: _fontFamily);
  static const IconData arrow_down_light = IconData(0xee53, fontFamily: _fontFamily);
  static const IconData arrow_down_right_light = IconData(0xee54, fontFamily: _fontFamily);
  static const IconData arrow_elbow_down_left_light = IconData(0xee55, fontFamily: _fontFamily);
  static const IconData arrow_elbow_down_right_light = IconData(0xee56, fontFamily: _fontFamily);
  static const IconData arrow_elbow_left_down_light = IconData(0xee57, fontFamily: _fontFamily);
  static const IconData arrow_elbow_left_light = IconData(0xee58, fontFamily: _fontFamily);
  static const IconData arrow_elbow_left_up_light = IconData(0xee59, fontFamily: _fontFamily);
  static const IconData arrow_elbow_right_down_light = IconData(0xee5a, fontFamily: _fontFamily);
  static const IconData arrow_elbow_right_light = IconData(0xee5b, fontFamily: _fontFamily);
  static const IconData arrow_elbow_right_up_light = IconData(0xee5c, fontFamily: _fontFamily);
  static const IconData arrow_elbow_up_left_light = IconData(0xee5d, fontFamily: _fontFamily);
  static const IconData arrow_elbow_up_right_light = IconData(0xee5e, fontFamily: _fontFamily);
  static const IconData arrow_fat_down_light = IconData(0xee5f, fontFamily: _fontFamily);
  static const IconData arrow_fat_left_light = IconData(0xee60, fontFamily: _fontFamily);
  static const IconData arrow_fat_line_down_light = IconData(0xee61, fontFamily: _fontFamily);
  static const IconData arrow_fat_line_left_light = IconData(0xee62, fontFamily: _fontFamily);
  static const IconData arrow_fat_line_right_light = IconData(0xee63, fontFamily: _fontFamily);
  static const IconData arrow_fat_line_up_light = IconData(0xee64, fontFamily: _fontFamily);
  static const IconData arrow_fat_lines_down_light = IconData(0xee65, fontFamily: _fontFamily);
  static const IconData arrow_fat_lines_left_light = IconData(0xee66, fontFamily: _fontFamily);
  static const IconData arrow_fat_lines_right_light = IconData(0xee67, fontFamily: _fontFamily);
  static const IconData arrow_fat_lines_up_light = IconData(0xee68, fontFamily: _fontFamily);
  static const IconData arrow_fat_right_light = IconData(0xee69, fontFamily: _fontFamily);
  static const IconData arrow_fat_up_light = IconData(0xee6a, fontFamily: _fontFamily);
  static const IconData arrow_left_light = IconData(0xee6b, fontFamily: _fontFamily);
  static const IconData arrow_line_down_left_light = IconData(0xee6c, fontFamily: _fontFamily);
  static const IconData arrow_line_down_light = IconData(0xee6d, fontFamily: _fontFamily);
  static const IconData arrow_line_down_right_light = IconData(0xee6e, fontFamily: _fontFamily);
  static const IconData arrow_line_left_light = IconData(0xee6f, fontFamily: _fontFamily);
  static const IconData arrow_line_right_light = IconData(0xee70, fontFamily: _fontFamily);
  static const IconData arrow_line_up_left_light = IconData(0xee71, fontFamily: _fontFamily);
  static const IconData arrow_line_up_light = IconData(0xee72, fontFamily: _fontFamily);
  static const IconData arrow_line_up_right_light = IconData(0xee73, fontFamily: _fontFamily);
  static const IconData arrow_right_light = IconData(0xee74, fontFamily: _fontFamily);
  static const IconData arrow_square_down_left_light = IconData(0xee75, fontFamily: _fontFamily);
  static const IconData arrow_square_down_light = IconData(0xee76, fontFamily: _fontFamily);
  static const IconData arrow_square_down_right_light = IconData(0xee77, fontFamily: _fontFamily);
  static const IconData arrow_square_in_light = IconData(0xee78, fontFamily: _fontFamily);
  static const IconData arrow_square_left_light = IconData(0xee79, fontFamily: _fontFamily);
  static const IconData arrow_square_out_light = IconData(0xee7a, fontFamily: _fontFamily);
  static const IconData arrow_square_right_light = IconData(0xee7b, fontFamily: _fontFamily);
  static const IconData arrow_square_up_left_light = IconData(0xee7c, fontFamily: _fontFamily);
  static const IconData arrow_square_up_light = IconData(0xee7d, fontFamily: _fontFamily);
  static const IconData arrow_square_up_right_light = IconData(0xee7e, fontFamily: _fontFamily);
  static const IconData arrow_u_down_left_light = IconData(0xee7f, fontFamily: _fontFamily);
  static const IconData arrow_u_down_right_light = IconData(0xee80, fontFamily: _fontFamily);
  static const IconData arrow_u_left_down_light = IconData(0xee81, fontFamily: _fontFamily);
  static const IconData arrow_u_left_up_light = IconData(0xee82, fontFamily: _fontFamily);
  static const IconData arrow_u_right_down_light = IconData(0xee83, fontFamily: _fontFamily);
  static const IconData arrow_u_right_up_light = IconData(0xee84, fontFamily: _fontFamily);
  static const IconData arrow_u_up_left_light = IconData(0xee85, fontFamily: _fontFamily);
  static const IconData arrow_u_up_right_light = IconData(0xee86, fontFamily: _fontFamily);
  static const IconData arrow_up_left_light = IconData(0xee87, fontFamily: _fontFamily);
  static const IconData arrow_up_light = IconData(0xee88, fontFamily: _fontFamily);
  static const IconData arrow_up_right_light = IconData(0xee89, fontFamily: _fontFamily);
  static const IconData arrows_clockwise_light = IconData(0xee8a, fontFamily: _fontFamily);
  static const IconData arrows_counter_clockwise_light = IconData(0xee8b, fontFamily: _fontFamily);
  static const IconData arrows_down_up_light = IconData(0xee8c, fontFamily: _fontFamily);
  static const IconData arrows_horizontal_light = IconData(0xee8d, fontFamily: _fontFamily);
  static const IconData arrows_in_cardinal_light = IconData(0xee8e, fontFamily: _fontFamily);
  static const IconData arrows_in_light = IconData(0xee8f, fontFamily: _fontFamily);
  static const IconData arrows_in_line_horizontal_light = IconData(0xee90, fontFamily: _fontFamily);
  static const IconData arrows_in_line_vertical_light = IconData(0xee91, fontFamily: _fontFamily);
  static const IconData arrows_in_simple_light = IconData(0xee92, fontFamily: _fontFamily);
  static const IconData arrows_left_right_light = IconData(0xee93, fontFamily: _fontFamily);
  static const IconData arrows_out_cardinal_light = IconData(0xee94, fontFamily: _fontFamily);
  static const IconData arrows_out_light = IconData(0xee95, fontFamily: _fontFamily);
  static const IconData arrows_out_line_horizontal_light = IconData(0xee96, fontFamily: _fontFamily);
  static const IconData arrows_out_line_vertical_light = IconData(0xee97, fontFamily: _fontFamily);
  static const IconData arrows_out_simple_light = IconData(0xee98, fontFamily: _fontFamily);
  static const IconData arrows_vertical_light = IconData(0xee99, fontFamily: _fontFamily);
  static const IconData article_light = IconData(0xee9a, fontFamily: _fontFamily);
  static const IconData article_medium_light = IconData(0xee9b, fontFamily: _fontFamily);
  static const IconData article_ny_times_light = IconData(0xee9c, fontFamily: _fontFamily);
  static const IconData asterisk_light = IconData(0xee9d, fontFamily: _fontFamily);
  static const IconData asterisk_simple_light = IconData(0xee9e, fontFamily: _fontFamily);
  static const IconData at_light = IconData(0xee9f, fontFamily: _fontFamily);
  static const IconData atom_light = IconData(0xeea0, fontFamily: _fontFamily);
  static const IconData baby_light = IconData(0xeea1, fontFamily: _fontFamily);
  static const IconData backpack_light = IconData(0xeea2, fontFamily: _fontFamily);
  static const IconData backspace_light = IconData(0xeea3, fontFamily: _fontFamily);
  static const IconData bag_light = IconData(0xeea4, fontFamily: _fontFamily);
  static const IconData bag_simple_light = IconData(0xeea5, fontFamily: _fontFamily);
  static const IconData balloon_light = IconData(0xeea6, fontFamily: _fontFamily);
  static const IconData bandaids_light = IconData(0xeea7, fontFamily: _fontFamily);
  static const IconData bank_light = IconData(0xeea8, fontFamily: _fontFamily);
  static const IconData barbell_light = IconData(0xeea9, fontFamily: _fontFamily);
  static const IconData barcode_light = IconData(0xeeaa, fontFamily: _fontFamily);
  static const IconData barricade_light = IconData(0xeeab, fontFamily: _fontFamily);
  static const IconData baseball_light = IconData(0xeeac, fontFamily: _fontFamily);
  static const IconData basketball_light = IconData(0xeead, fontFamily: _fontFamily);
  static const IconData bathtub_light = IconData(0xeeae, fontFamily: _fontFamily);
  static const IconData battery_charging_light = IconData(0xeeaf, fontFamily: _fontFamily);
  static const IconData battery_charging_vertical_light = IconData(0xeeb0, fontFamily: _fontFamily);
  static const IconData battery_empty_light = IconData(0xeeb1, fontFamily: _fontFamily);
  static const IconData battery_full_light = IconData(0xeeb2, fontFamily: _fontFamily);
  static const IconData battery_high_light = IconData(0xeeb3, fontFamily: _fontFamily);
  static const IconData battery_low_light = IconData(0xeeb4, fontFamily: _fontFamily);
  static const IconData battery_medium_light = IconData(0xeeb5, fontFamily: _fontFamily);
  static const IconData battery_plus_light = IconData(0xeeb6, fontFamily: _fontFamily);
  static const IconData battery_warning_light = IconData(0xeeb7, fontFamily: _fontFamily);
  static const IconData battery_warning_vertical_light = IconData(0xeeb8, fontFamily: _fontFamily);
  static const IconData bed_light = IconData(0xeeb9, fontFamily: _fontFamily);
  static const IconData beer_bottle_light = IconData(0xeeba, fontFamily: _fontFamily);
  static const IconData behance_logo_light = IconData(0xeebb, fontFamily: _fontFamily);
  static const IconData bell_light = IconData(0xeebc, fontFamily: _fontFamily);
  static const IconData bell_ringing_light = IconData(0xeebd, fontFamily: _fontFamily);
  static const IconData bell_simple_light = IconData(0xeebe, fontFamily: _fontFamily);
  static const IconData bell_simple_ringing_light = IconData(0xeebf, fontFamily: _fontFamily);
  static const IconData bell_simple_slash_light = IconData(0xeec0, fontFamily: _fontFamily);
  static const IconData bell_simple_z_light = IconData(0xeec1, fontFamily: _fontFamily);
  static const IconData bell_slash_light = IconData(0xeec2, fontFamily: _fontFamily);
  static const IconData bell_z_light = IconData(0xeec3, fontFamily: _fontFamily);
  static const IconData bezier_curve_light = IconData(0xeec4, fontFamily: _fontFamily);
  static const IconData bicycle_light = IconData(0xeec5, fontFamily: _fontFamily);
  static const IconData binoculars_light = IconData(0xeec6, fontFamily: _fontFamily);
  static const IconData bird_light = IconData(0xeec7, fontFamily: _fontFamily);
  static const IconData bluetooth_connected_light = IconData(0xeec8, fontFamily: _fontFamily);
  static const IconData bluetooth_light = IconData(0xeec9, fontFamily: _fontFamily);
  static const IconData bluetooth_slash_light = IconData(0xeeca, fontFamily: _fontFamily);
  static const IconData bluetooth_x_light = IconData(0xeecb, fontFamily: _fontFamily);
  static const IconData boat_light = IconData(0xeecc, fontFamily: _fontFamily);
  static const IconData book_bookmark_light = IconData(0xeecd, fontFamily: _fontFamily);
  static const IconData book_light = IconData(0xeece, fontFamily: _fontFamily);
  static const IconData book_open_light = IconData(0xeecf, fontFamily: _fontFamily);
  static const IconData bookmark_light = IconData(0xeed0, fontFamily: _fontFamily);
  static const IconData bookmark_simple_light = IconData(0xeed1, fontFamily: _fontFamily);
  static const IconData bookmarks_light = IconData(0xeed2, fontFamily: _fontFamily);
  static const IconData bookmarks_simple_light = IconData(0xeed3, fontFamily: _fontFamily);
  static const IconData books_light = IconData(0xeed4, fontFamily: _fontFamily);
  static const IconData bounding_box_light = IconData(0xeed5, fontFamily: _fontFamily);
  static const IconData brackets_angle_light = IconData(0xeed6, fontFamily: _fontFamily);
  static const IconData brackets_curly_light = IconData(0xeed7, fontFamily: _fontFamily);
  static const IconData brackets_round_light = IconData(0xeed8, fontFamily: _fontFamily);
  static const IconData brackets_square_light = IconData(0xeed9, fontFamily: _fontFamily);
  static const IconData brain_light = IconData(0xeeda, fontFamily: _fontFamily);
  static const IconData brandy_light = IconData(0xeedb, fontFamily: _fontFamily);
  static const IconData briefcase_light = IconData(0xeedc, fontFamily: _fontFamily);
  static const IconData briefcase_metal_light = IconData(0xeedd, fontFamily: _fontFamily);
  static const IconData broadcast_light = IconData(0xeede, fontFamily: _fontFamily);
  static const IconData browser_light = IconData(0xeedf, fontFamily: _fontFamily);
  static const IconData browsers_light = IconData(0xeee0, fontFamily: _fontFamily);
  static const IconData bug_beetle_light = IconData(0xeee1, fontFamily: _fontFamily);
  static const IconData bug_droid_light = IconData(0xeee2, fontFamily: _fontFamily);
  static const IconData bug_light = IconData(0xeee3, fontFamily: _fontFamily);
  static const IconData buildings_light = IconData(0xeee4, fontFamily: _fontFamily);
  static const IconData bus_light = IconData(0xeee5, fontFamily: _fontFamily);
  static const IconData butterfly_light = IconData(0xeee6, fontFamily: _fontFamily);
  static const IconData cactus_light = IconData(0xeee7, fontFamily: _fontFamily);
  static const IconData cake_light = IconData(0xeee8, fontFamily: _fontFamily);
  static const IconData calculator_light = IconData(0xeee9, fontFamily: _fontFamily);
  static const IconData calendar_blank_light = IconData(0xeeea, fontFamily: _fontFamily);
  static const IconData calendar_check_light = IconData(0xeeeb, fontFamily: _fontFamily);
  static const IconData calendar_light = IconData(0xeeec, fontFamily: _fontFamily);
  static const IconData calendar_plus_light = IconData(0xeeed, fontFamily: _fontFamily);
  static const IconData calendar_x_light = IconData(0xeeee, fontFamily: _fontFamily);
  static const IconData camera_light = IconData(0xeeef, fontFamily: _fontFamily);
  static const IconData camera_rotate_light = IconData(0xeef0, fontFamily: _fontFamily);
  static const IconData camera_slash_light = IconData(0xeef1, fontFamily: _fontFamily);
  static const IconData campfire_light = IconData(0xeef2, fontFamily: _fontFamily);
  static const IconData car_light = IconData(0xeef3, fontFamily: _fontFamily);
  static const IconData car_simple_light = IconData(0xeef4, fontFamily: _fontFamily);
  static const IconData cardholder_light = IconData(0xeef5, fontFamily: _fontFamily);
  static const IconData cards_light = IconData(0xeef6, fontFamily: _fontFamily);
  static const IconData caret_circle_double_down_light = IconData(0xeef7, fontFamily: _fontFamily);
  static const IconData caret_circle_double_left_light = IconData(0xeef8, fontFamily: _fontFamily);
  static const IconData caret_circle_double_right_light = IconData(0xeef9, fontFamily: _fontFamily);
  static const IconData caret_circle_double_up_light = IconData(0xeefa, fontFamily: _fontFamily);
  static const IconData caret_circle_down_light = IconData(0xeefb, fontFamily: _fontFamily);
  static const IconData caret_circle_left_light = IconData(0xeefc, fontFamily: _fontFamily);
  static const IconData caret_circle_right_light = IconData(0xeefd, fontFamily: _fontFamily);
  static const IconData caret_circle_up_light = IconData(0xeefe, fontFamily: _fontFamily);
  static const IconData caret_double_down_light = IconData(0xeeff, fontFamily: _fontFamily);
  static const IconData caret_double_left_light = IconData(0xef00, fontFamily: _fontFamily);
  static const IconData caret_double_right_light = IconData(0xef01, fontFamily: _fontFamily);
  static const IconData caret_double_up_light = IconData(0xef02, fontFamily: _fontFamily);
  static const IconData caret_down_light = IconData(0xef03, fontFamily: _fontFamily);
  static const IconData caret_left_light = IconData(0xef04, fontFamily: _fontFamily);
  static const IconData caret_right_light = IconData(0xef05, fontFamily: _fontFamily);
  static const IconData caret_up_light = IconData(0xef06, fontFamily: _fontFamily);
  static const IconData cat_light = IconData(0xef07, fontFamily: _fontFamily);
  static const IconData cell_signal_full_light = IconData(0xef08, fontFamily: _fontFamily);
  static const IconData cell_signal_high_light = IconData(0xef09, fontFamily: _fontFamily);
  static const IconData cell_signal_low_light = IconData(0xef0a, fontFamily: _fontFamily);
  static const IconData cell_signal_medium_light = IconData(0xef0b, fontFamily: _fontFamily);
  static const IconData cell_signal_none_light = IconData(0xef0c, fontFamily: _fontFamily);
  static const IconData cell_signal_slash_light = IconData(0xef0d, fontFamily: _fontFamily);
  static const IconData cell_signal_x_light = IconData(0xef0e, fontFamily: _fontFamily);
  static const IconData chalkboard_light = IconData(0xef0f, fontFamily: _fontFamily);
  static const IconData chalkboard_simple_light = IconData(0xef10, fontFamily: _fontFamily);
  static const IconData chalkboard_teacher_light = IconData(0xef11, fontFamily: _fontFamily);
  static const IconData chart_bar_horizontal_light = IconData(0xef12, fontFamily: _fontFamily);
  static const IconData chart_bar_light = IconData(0xef13, fontFamily: _fontFamily);
  static const IconData chart_line_light = IconData(0xef14, fontFamily: _fontFamily);
  static const IconData chart_line_up_light = IconData(0xef15, fontFamily: _fontFamily);
  static const IconData chart_pie_light = IconData(0xef16, fontFamily: _fontFamily);
  static const IconData chart_pie_slice_light = IconData(0xef17, fontFamily: _fontFamily);
  static const IconData chat_centered_dots_light = IconData(0xef18, fontFamily: _fontFamily);
  static const IconData chat_centered_light = IconData(0xef19, fontFamily: _fontFamily);
  static const IconData chat_centered_text_light = IconData(0xef1a, fontFamily: _fontFamily);
  static const IconData chat_circle_dots_light = IconData(0xef1b, fontFamily: _fontFamily);
  static const IconData chat_circle_light = IconData(0xef1c, fontFamily: _fontFamily);
  static const IconData chat_circle_text_light = IconData(0xef1d, fontFamily: _fontFamily);
  static const IconData chat_dots_light = IconData(0xef1e, fontFamily: _fontFamily);
  static const IconData chat_light = IconData(0xef1f, fontFamily: _fontFamily);
  static const IconData chat_teardrop_dots_light = IconData(0xef20, fontFamily: _fontFamily);
  static const IconData chat_teardrop_light = IconData(0xef21, fontFamily: _fontFamily);
  static const IconData chat_teardrop_text_light = IconData(0xef22, fontFamily: _fontFamily);
  static const IconData chat_text_light = IconData(0xef23, fontFamily: _fontFamily);
  static const IconData chats_circle_light = IconData(0xef24, fontFamily: _fontFamily);
  static const IconData chats_light = IconData(0xef25, fontFamily: _fontFamily);
  static const IconData chats_teardrop_light = IconData(0xef26, fontFamily: _fontFamily);
  static const IconData check_circle_light = IconData(0xef27, fontFamily: _fontFamily);
  static const IconData check_light = IconData(0xef28, fontFamily: _fontFamily);
  static const IconData check_square_light = IconData(0xef29, fontFamily: _fontFamily);
  static const IconData check_square_offset_light = IconData(0xef2a, fontFamily: _fontFamily);
  static const IconData checks_light = IconData(0xef2b, fontFamily: _fontFamily);
  static const IconData circle_dashed_light = IconData(0xef2c, fontFamily: _fontFamily);
  static const IconData circle_half_light = IconData(0xef2d, fontFamily: _fontFamily);
  static const IconData circle_half_tilt_light = IconData(0xef2e, fontFamily: _fontFamily);
  static const IconData circle_light = IconData(0xef2f, fontFamily: _fontFamily);
  static const IconData circle_notch_light = IconData(0xef30, fontFamily: _fontFamily);
  static const IconData circle_wavy_check_light = IconData(0xef31, fontFamily: _fontFamily);
  static const IconData circle_wavy_light = IconData(0xef32, fontFamily: _fontFamily);
  static const IconData circle_wavy_question_light = IconData(0xef33, fontFamily: _fontFamily);
  static const IconData circle_wavy_warning_light = IconData(0xef34, fontFamily: _fontFamily);
  static const IconData circles_four_light = IconData(0xef35, fontFamily: _fontFamily);
  static const IconData circles_three_light = IconData(0xef36, fontFamily: _fontFamily);
  static const IconData circles_three_plus_light = IconData(0xef37, fontFamily: _fontFamily);
  static const IconData clipboard_light = IconData(0xef38, fontFamily: _fontFamily);
  static const IconData clipboard_text_light = IconData(0xef39, fontFamily: _fontFamily);
  static const IconData clock_afternoon_light = IconData(0xef3a, fontFamily: _fontFamily);
  static const IconData clock_clockwise_light = IconData(0xef3b, fontFamily: _fontFamily);
  static const IconData clock_counter_clockwise_light = IconData(0xef3c, fontFamily: _fontFamily);
  static const IconData clock_light = IconData(0xef3d, fontFamily: _fontFamily);
  static const IconData closed_captioning_light = IconData(0xef3e, fontFamily: _fontFamily);
  static const IconData cloud_arrow_down_light = IconData(0xef3f, fontFamily: _fontFamily);
  static const IconData cloud_arrow_up_light = IconData(0xef40, fontFamily: _fontFamily);
  static const IconData cloud_check_light = IconData(0xef41, fontFamily: _fontFamily);
  static const IconData cloud_fog_light = IconData(0xef42, fontFamily: _fontFamily);
  static const IconData cloud_light = IconData(0xef43, fontFamily: _fontFamily);
  static const IconData cloud_lightning_light = IconData(0xef44, fontFamily: _fontFamily);
  static const IconData cloud_moon_light = IconData(0xef45, fontFamily: _fontFamily);
  static const IconData cloud_rain_light = IconData(0xef46, fontFamily: _fontFamily);
  static const IconData cloud_slash_light = IconData(0xef47, fontFamily: _fontFamily);
  static const IconData cloud_snow_light = IconData(0xef48, fontFamily: _fontFamily);
  static const IconData cloud_sun_light = IconData(0xef49, fontFamily: _fontFamily);
  static const IconData club_light = IconData(0xef4a, fontFamily: _fontFamily);
  static const IconData coat_hanger_light = IconData(0xef4b, fontFamily: _fontFamily);
  static const IconData code_light = IconData(0xef4c, fontFamily: _fontFamily);
  static const IconData code_simple_light = IconData(0xef4d, fontFamily: _fontFamily);
  static const IconData codepen_logo_light = IconData(0xef4e, fontFamily: _fontFamily);
  static const IconData codesandbox_logo_light = IconData(0xef4f, fontFamily: _fontFamily);
  static const IconData coffee_light = IconData(0xef50, fontFamily: _fontFamily);
  static const IconData coin_light = IconData(0xef51, fontFamily: _fontFamily);
  static const IconData coin_vertical_light = IconData(0xef52, fontFamily: _fontFamily);
  static const IconData coins_light = IconData(0xef53, fontFamily: _fontFamily);
  static const IconData columns_light = IconData(0xef54, fontFamily: _fontFamily);
  static const IconData command_light = IconData(0xef55, fontFamily: _fontFamily);
  static const IconData compass_light = IconData(0xef56, fontFamily: _fontFamily);
  static const IconData computer_tower_light = IconData(0xef57, fontFamily: _fontFamily);
  static const IconData confetti_light = IconData(0xef58, fontFamily: _fontFamily);
  static const IconData cookie_light = IconData(0xef59, fontFamily: _fontFamily);
  static const IconData cooking_pot_light = IconData(0xef5a, fontFamily: _fontFamily);
  static const IconData copy_light = IconData(0xef5b, fontFamily: _fontFamily);
  static const IconData copy_simple_light = IconData(0xef5c, fontFamily: _fontFamily);
  static const IconData copyleft_light = IconData(0xef5d, fontFamily: _fontFamily);
  static const IconData copyright_light = IconData(0xef5e, fontFamily: _fontFamily);
  static const IconData corners_in_light = IconData(0xef5f, fontFamily: _fontFamily);
  static const IconData corners_out_light = IconData(0xef60, fontFamily: _fontFamily);
  static const IconData cpu_light = IconData(0xef61, fontFamily: _fontFamily);
  static const IconData credit_card_light = IconData(0xef62, fontFamily: _fontFamily);
  static const IconData crop_light = IconData(0xef63, fontFamily: _fontFamily);
  static const IconData crosshair_light = IconData(0xef64, fontFamily: _fontFamily);
  static const IconData crosshair_simple_light = IconData(0xef65, fontFamily: _fontFamily);
  static const IconData crown_light = IconData(0xef66, fontFamily: _fontFamily);
  static const IconData crown_simple_light = IconData(0xef67, fontFamily: _fontFamily);
  static const IconData cube_light = IconData(0xef68, fontFamily: _fontFamily);
  static const IconData currency_btc_light = IconData(0xef69, fontFamily: _fontFamily);
  static const IconData currency_circle_dollar_light = IconData(0xef6a, fontFamily: _fontFamily);
  static const IconData currency_cny_light = IconData(0xef6b, fontFamily: _fontFamily);
  static const IconData currency_dollar_light = IconData(0xef6c, fontFamily: _fontFamily);
  static const IconData currency_dollar_simple_light = IconData(0xef6d, fontFamily: _fontFamily);
  static const IconData currency_eth_light = IconData(0xef6e, fontFamily: _fontFamily);
  static const IconData currency_eur_light = IconData(0xef6f, fontFamily: _fontFamily);
  static const IconData currency_gbp_light = IconData(0xef70, fontFamily: _fontFamily);
  static const IconData currency_inr_light = IconData(0xef71, fontFamily: _fontFamily);
  static const IconData currency_jpy_light = IconData(0xef72, fontFamily: _fontFamily);
  static const IconData currency_krw_light = IconData(0xef73, fontFamily: _fontFamily);
  static const IconData currency_kzt_light = IconData(0xef74, fontFamily: _fontFamily);
  static const IconData currency_ngn_light = IconData(0xef75, fontFamily: _fontFamily);
  static const IconData currency_rub_light = IconData(0xef76, fontFamily: _fontFamily);
  static const IconData cursor_light = IconData(0xef77, fontFamily: _fontFamily);
  static const IconData cursor_text_light = IconData(0xef78, fontFamily: _fontFamily);
  static const IconData cylinder_light = IconData(0xef79, fontFamily: _fontFamily);
  static const IconData database_light = IconData(0xef7a, fontFamily: _fontFamily);
  static const IconData desktop_light = IconData(0xef7b, fontFamily: _fontFamily);
  static const IconData desktop_tower_light = IconData(0xef7c, fontFamily: _fontFamily);
  static const IconData detective_light = IconData(0xef7d, fontFamily: _fontFamily);
  static const IconData device_mobile_camera_light = IconData(0xef7e, fontFamily: _fontFamily);
  static const IconData device_mobile_light = IconData(0xef7f, fontFamily: _fontFamily);
  static const IconData device_mobile_speaker_light = IconData(0xef80, fontFamily: _fontFamily);
  static const IconData device_tablet_camera_light = IconData(0xef81, fontFamily: _fontFamily);
  static const IconData device_tablet_light = IconData(0xef82, fontFamily: _fontFamily);
  static const IconData device_tablet_speaker_light = IconData(0xef83, fontFamily: _fontFamily);
  static const IconData diamond_light = IconData(0xef84, fontFamily: _fontFamily);
  static const IconData diamonds_four_light = IconData(0xef85, fontFamily: _fontFamily);
  static const IconData dice_five_light = IconData(0xef86, fontFamily: _fontFamily);
  static const IconData dice_four_light = IconData(0xef87, fontFamily: _fontFamily);
  static const IconData dice_one_light = IconData(0xef88, fontFamily: _fontFamily);
  static const IconData dice_six_light = IconData(0xef89, fontFamily: _fontFamily);
  static const IconData dice_three_light = IconData(0xef8a, fontFamily: _fontFamily);
  static const IconData dice_two_light = IconData(0xef8b, fontFamily: _fontFamily);
  static const IconData disc_light = IconData(0xef8c, fontFamily: _fontFamily);
  static const IconData discord_logo_light = IconData(0xef8d, fontFamily: _fontFamily);
  static const IconData divide_light = IconData(0xef8e, fontFamily: _fontFamily);
  static const IconData dog_light = IconData(0xef8f, fontFamily: _fontFamily);
  static const IconData door_light = IconData(0xef90, fontFamily: _fontFamily);
  static const IconData dots_nine_light = IconData(0xef91, fontFamily: _fontFamily);
  static const IconData dots_six_light = IconData(0xef92, fontFamily: _fontFamily);
  static const IconData dots_six_vertical_light = IconData(0xef93, fontFamily: _fontFamily);
  static const IconData dots_three_circle_light = IconData(0xef94, fontFamily: _fontFamily);
  static const IconData dots_three_circle_vertical_light = IconData(0xef95, fontFamily: _fontFamily);
  static const IconData dots_three_light = IconData(0xef96, fontFamily: _fontFamily);
  static const IconData dots_three_outline_light = IconData(0xef97, fontFamily: _fontFamily);
  static const IconData dots_three_outline_vertical_light = IconData(0xef98, fontFamily: _fontFamily);
  static const IconData dots_three_vertical_light = IconData(0xef99, fontFamily: _fontFamily);
  static const IconData download_light = IconData(0xef9a, fontFamily: _fontFamily);
  static const IconData download_simple_light = IconData(0xef9b, fontFamily: _fontFamily);
  static const IconData dribbble_logo_light = IconData(0xef9c, fontFamily: _fontFamily);
  static const IconData drop_half_bottom_light = IconData(0xef9d, fontFamily: _fontFamily);
  static const IconData drop_half_light = IconData(0xef9e, fontFamily: _fontFamily);
  static const IconData drop_light = IconData(0xef9f, fontFamily: _fontFamily);
  static const IconData ear_light = IconData(0xefa0, fontFamily: _fontFamily);
  static const IconData ear_slash_light = IconData(0xefa1, fontFamily: _fontFamily);
  static const IconData egg_crack_light = IconData(0xefa2, fontFamily: _fontFamily);
  static const IconData egg_light = IconData(0xefa3, fontFamily: _fontFamily);
  static const IconData eject_light = IconData(0xefa4, fontFamily: _fontFamily);
  static const IconData eject_simple_light = IconData(0xefa5, fontFamily: _fontFamily);
  static const IconData envelope_light = IconData(0xefa6, fontFamily: _fontFamily);
  static const IconData envelope_open_light = IconData(0xefa7, fontFamily: _fontFamily);
  static const IconData envelope_simple_light = IconData(0xefa8, fontFamily: _fontFamily);
  static const IconData envelope_simple_open_light = IconData(0xefa9, fontFamily: _fontFamily);
  static const IconData equalizer_light = IconData(0xefaa, fontFamily: _fontFamily);
  static const IconData equals_light = IconData(0xefab, fontFamily: _fontFamily);
  static const IconData eraser_light = IconData(0xefac, fontFamily: _fontFamily);
  static const IconData exam_light = IconData(0xefad, fontFamily: _fontFamily);
  static const IconData export_light = IconData(0xefae, fontFamily: _fontFamily);
  static const IconData eye_closed_light = IconData(0xefaf, fontFamily: _fontFamily);
  static const IconData eye_light = IconData(0xefb0, fontFamily: _fontFamily);
  static const IconData eye_slash_light = IconData(0xefb1, fontFamily: _fontFamily);
  static const IconData eyedropper_light = IconData(0xefb2, fontFamily: _fontFamily);
  static const IconData eyedropper_sample_light = IconData(0xefb3, fontFamily: _fontFamily);
  static const IconData eyeglasses_light = IconData(0xefb4, fontFamily: _fontFamily);
  static const IconData face_mask_light = IconData(0xefb5, fontFamily: _fontFamily);
  static const IconData facebook_logo_light = IconData(0xefb6, fontFamily: _fontFamily);
  static const IconData factory_light = IconData(0xefb7, fontFamily: _fontFamily);
  static const IconData faders_horizontal_light = IconData(0xefb8, fontFamily: _fontFamily);
  static const IconData faders_light = IconData(0xefb9, fontFamily: _fontFamily);
  static const IconData fast_forward_circle_light = IconData(0xefba, fontFamily: _fontFamily);
  static const IconData fast_forward_light = IconData(0xefbb, fontFamily: _fontFamily);
  static const IconData figma_logo_light = IconData(0xefbc, fontFamily: _fontFamily);
  static const IconData file_arrow_down_light = IconData(0xefbd, fontFamily: _fontFamily);
  static const IconData file_arrow_up_light = IconData(0xefbe, fontFamily: _fontFamily);
  static const IconData file_audio_light = IconData(0xefbf, fontFamily: _fontFamily);
  static const IconData file_cloud_light = IconData(0xefc0, fontFamily: _fontFamily);
  static const IconData file_code_light = IconData(0xefc1, fontFamily: _fontFamily);
  static const IconData file_css_light = IconData(0xefc2, fontFamily: _fontFamily);
  static const IconData file_csv_light = IconData(0xefc3, fontFamily: _fontFamily);
  static const IconData file_doc_light = IconData(0xefc4, fontFamily: _fontFamily);
  static const IconData file_dotted_light = IconData(0xefc5, fontFamily: _fontFamily);
  static const IconData file_html_light = IconData(0xefc6, fontFamily: _fontFamily);
  static const IconData file_image_light = IconData(0xefc7, fontFamily: _fontFamily);
  static const IconData file_jpg_light = IconData(0xefc8, fontFamily: _fontFamily);
  static const IconData file_js_light = IconData(0xefc9, fontFamily: _fontFamily);
  static const IconData file_jsx_light = IconData(0xefca, fontFamily: _fontFamily);
  static const IconData file_light = IconData(0xefcb, fontFamily: _fontFamily);
  static const IconData file_lock_light = IconData(0xefcc, fontFamily: _fontFamily);
  static const IconData file_minus_light = IconData(0xefcd, fontFamily: _fontFamily);
  static const IconData file_pdf_light = IconData(0xefce, fontFamily: _fontFamily);
  static const IconData file_plus_light = IconData(0xefcf, fontFamily: _fontFamily);
  static const IconData file_png_light = IconData(0xefd0, fontFamily: _fontFamily);
  static const IconData file_ppt_light = IconData(0xefd1, fontFamily: _fontFamily);
  static const IconData file_rs_light = IconData(0xefd2, fontFamily: _fontFamily);
  static const IconData file_search_light = IconData(0xefd3, fontFamily: _fontFamily);
  static const IconData file_text_light = IconData(0xefd4, fontFamily: _fontFamily);
  static const IconData file_ts_light = IconData(0xefd5, fontFamily: _fontFamily);
  static const IconData file_tsx_light = IconData(0xefd6, fontFamily: _fontFamily);
  static const IconData file_video_light = IconData(0xefd7, fontFamily: _fontFamily);
  static const IconData file_vue_light = IconData(0xefd8, fontFamily: _fontFamily);
  static const IconData file_x_light = IconData(0xefd9, fontFamily: _fontFamily);
  static const IconData file_xls_light = IconData(0xefda, fontFamily: _fontFamily);
  static const IconData file_zip_light = IconData(0xefdb, fontFamily: _fontFamily);
  static const IconData files_light = IconData(0xefdc, fontFamily: _fontFamily);
  static const IconData film_script_light = IconData(0xefdd, fontFamily: _fontFamily);
  static const IconData film_slate_light = IconData(0xefde, fontFamily: _fontFamily);
  static const IconData film_strip_light = IconData(0xefdf, fontFamily: _fontFamily);
  static const IconData fingerprint_light = IconData(0xefe0, fontFamily: _fontFamily);
  static const IconData fingerprint_simple_light = IconData(0xefe1, fontFamily: _fontFamily);
  static const IconData finn_the_human_light = IconData(0xefe2, fontFamily: _fontFamily);
  static const IconData fire_light = IconData(0xefe3, fontFamily: _fontFamily);
  static const IconData fire_simple_light = IconData(0xefe4, fontFamily: _fontFamily);
  static const IconData first_aid_kit_light = IconData(0xefe5, fontFamily: _fontFamily);
  static const IconData first_aid_light = IconData(0xefe6, fontFamily: _fontFamily);
  static const IconData fish_light = IconData(0xefe7, fontFamily: _fontFamily);
  static const IconData fish_simple_light = IconData(0xefe8, fontFamily: _fontFamily);
  static const IconData flag_banner_light = IconData(0xefe9, fontFamily: _fontFamily);
  static const IconData flag_checkered_light = IconData(0xefea, fontFamily: _fontFamily);
  static const IconData flag_light = IconData(0xefeb, fontFamily: _fontFamily);
  static const IconData flame_light = IconData(0xefec, fontFamily: _fontFamily);
  static const IconData flashlight_light = IconData(0xefed, fontFamily: _fontFamily);
  static const IconData flask_light = IconData(0xefee, fontFamily: _fontFamily);
  static const IconData floppy_disk_back_light = IconData(0xefef, fontFamily: _fontFamily);
  static const IconData floppy_disk_light = IconData(0xeff0, fontFamily: _fontFamily);
  static const IconData flow_arrow_light = IconData(0xeff1, fontFamily: _fontFamily);
  static const IconData flower_light = IconData(0xeff2, fontFamily: _fontFamily);
  static const IconData flower_lotus_light = IconData(0xeff3, fontFamily: _fontFamily);
  static const IconData flying_saucer_light = IconData(0xeff4, fontFamily: _fontFamily);
  static const IconData folder_dotted_light = IconData(0xeff5, fontFamily: _fontFamily);
  static const IconData folder_light = IconData(0xeff6, fontFamily: _fontFamily);
  static const IconData folder_lock_light = IconData(0xeff7, fontFamily: _fontFamily);
  static const IconData folder_minus_light = IconData(0xeff8, fontFamily: _fontFamily);
  static const IconData folder_notch_light = IconData(0xeff9, fontFamily: _fontFamily);
  static const IconData folder_notch_minus_light = IconData(0xeffa, fontFamily: _fontFamily);
  static const IconData folder_notch_open_light = IconData(0xeffb, fontFamily: _fontFamily);
  static const IconData folder_notch_plus_light = IconData(0xeffc, fontFamily: _fontFamily);
  static const IconData folder_open_light = IconData(0xeffd, fontFamily: _fontFamily);
  static const IconData folder_plus_light = IconData(0xeffe, fontFamily: _fontFamily);
  static const IconData folder_simple_dotted_light = IconData(0xefff, fontFamily: _fontFamily);
  static const IconData folder_simple_light = IconData(0xf000, fontFamily: _fontFamily);
  static const IconData folder_simple_lock_light = IconData(0xf001, fontFamily: _fontFamily);
  static const IconData folder_simple_minus_light = IconData(0xf002, fontFamily: _fontFamily);
  static const IconData folder_simple_plus_light = IconData(0xf003, fontFamily: _fontFamily);
  static const IconData folder_simple_star_light = IconData(0xf004, fontFamily: _fontFamily);
  static const IconData folder_simple_user_light = IconData(0xf005, fontFamily: _fontFamily);
  static const IconData folder_star_light = IconData(0xf006, fontFamily: _fontFamily);
  static const IconData folder_user_light = IconData(0xf007, fontFamily: _fontFamily);
  static const IconData folders_light = IconData(0xf008, fontFamily: _fontFamily);
  static const IconData football_light = IconData(0xf009, fontFamily: _fontFamily);
  static const IconData fork_knife_light = IconData(0xf00a, fontFamily: _fontFamily);
  static const IconData frame_corners_light = IconData(0xf00b, fontFamily: _fontFamily);
  static const IconData framer_logo_light = IconData(0xf00c, fontFamily: _fontFamily);
  static const IconData function_light = IconData(0xf00d, fontFamily: _fontFamily);
  static const IconData funnel_light = IconData(0xf00e, fontFamily: _fontFamily);
  static const IconData funnel_simple_light = IconData(0xf00f, fontFamily: _fontFamily);
  static const IconData game_controller_light = IconData(0xf010, fontFamily: _fontFamily);
  static const IconData gas_pump_light = IconData(0xf011, fontFamily: _fontFamily);
  static const IconData gauge_light = IconData(0xf012, fontFamily: _fontFamily);
  static const IconData gear_light = IconData(0xf013, fontFamily: _fontFamily);
  static const IconData gear_six_light = IconData(0xf014, fontFamily: _fontFamily);
  static const IconData gender_female_light = IconData(0xf015, fontFamily: _fontFamily);
  static const IconData gender_intersex_light = IconData(0xf016, fontFamily: _fontFamily);
  static const IconData gender_male_light = IconData(0xf017, fontFamily: _fontFamily);
  static const IconData gender_neuter_light = IconData(0xf018, fontFamily: _fontFamily);
  static const IconData gender_nonbinary_light = IconData(0xf019, fontFamily: _fontFamily);
  static const IconData gender_transgender_light = IconData(0xf01a, fontFamily: _fontFamily);
  static const IconData ghost_light = IconData(0xf01b, fontFamily: _fontFamily);
  static const IconData gif_light = IconData(0xf01c, fontFamily: _fontFamily);
  static const IconData gift_light = IconData(0xf01d, fontFamily: _fontFamily);
  static const IconData git_branch_light = IconData(0xf01e, fontFamily: _fontFamily);
  static const IconData git_commit_light = IconData(0xf01f, fontFamily: _fontFamily);
  static const IconData git_diff_light = IconData(0xf020, fontFamily: _fontFamily);
  static const IconData git_fork_light = IconData(0xf021, fontFamily: _fontFamily);
  static const IconData git_merge_light = IconData(0xf022, fontFamily: _fontFamily);
  static const IconData git_pull_request_light = IconData(0xf023, fontFamily: _fontFamily);
  static const IconData github_logo_light = IconData(0xf024, fontFamily: _fontFamily);
  static const IconData gitlab_logo_light = IconData(0xf025, fontFamily: _fontFamily);
  static const IconData gitlab_logo_simple_light = IconData(0xf026, fontFamily: _fontFamily);
  static const IconData globe_hemisphere_east_light = IconData(0xf027, fontFamily: _fontFamily);
  static const IconData globe_hemisphere_west_light = IconData(0xf028, fontFamily: _fontFamily);
  static const IconData globe_light = IconData(0xf029, fontFamily: _fontFamily);
  static const IconData globe_simple_light = IconData(0xf02a, fontFamily: _fontFamily);
  static const IconData globe_stand_light = IconData(0xf02b, fontFamily: _fontFamily);
  static const IconData google_chrome_logo_light = IconData(0xf02c, fontFamily: _fontFamily);
  static const IconData google_logo_light = IconData(0xf02d, fontFamily: _fontFamily);
  static const IconData google_photos_logo_light = IconData(0xf02e, fontFamily: _fontFamily);
  static const IconData google_play_logo_light = IconData(0xf02f, fontFamily: _fontFamily);
  static const IconData google_podcasts_logo_light = IconData(0xf030, fontFamily: _fontFamily);
  static const IconData gradient_light = IconData(0xf031, fontFamily: _fontFamily);
  static const IconData graduation_cap_light = IconData(0xf032, fontFamily: _fontFamily);
  static const IconData graph_light = IconData(0xf033, fontFamily: _fontFamily);
  static const IconData grid_four_light = IconData(0xf034, fontFamily: _fontFamily);
  static const IconData hamburger_light = IconData(0xf035, fontFamily: _fontFamily);
  static const IconData hand_eye_light = IconData(0xf036, fontFamily: _fontFamily);
  static const IconData hand_fist_light = IconData(0xf037, fontFamily: _fontFamily);
  static const IconData hand_grabbing_light = IconData(0xf038, fontFamily: _fontFamily);
  static const IconData hand_light = IconData(0xf039, fontFamily: _fontFamily);
  static const IconData hand_palm_light = IconData(0xf03a, fontFamily: _fontFamily);
  static const IconData hand_pointing_light = IconData(0xf03b, fontFamily: _fontFamily);
  static const IconData hand_soap_light = IconData(0xf03c, fontFamily: _fontFamily);
  static const IconData hand_waving_light = IconData(0xf03d, fontFamily: _fontFamily);
  static const IconData handbag_light = IconData(0xf03e, fontFamily: _fontFamily);
  static const IconData handbag_simple_light = IconData(0xf03f, fontFamily: _fontFamily);
  static const IconData hands_clapping_light = IconData(0xf040, fontFamily: _fontFamily);
  static const IconData handshake_light = IconData(0xf041, fontFamily: _fontFamily);
  static const IconData hard_drive_light = IconData(0xf042, fontFamily: _fontFamily);
  static const IconData hard_drives_light = IconData(0xf043, fontFamily: _fontFamily);
  static const IconData hash_light = IconData(0xf044, fontFamily: _fontFamily);
  static const IconData hash_straight_light = IconData(0xf045, fontFamily: _fontFamily);
  static const IconData headlights_light = IconData(0xf046, fontFamily: _fontFamily);
  static const IconData headphones_light = IconData(0xf047, fontFamily: _fontFamily);
  static const IconData headset_light = IconData(0xf048, fontFamily: _fontFamily);
  static const IconData heart_break_light = IconData(0xf049, fontFamily: _fontFamily);
  static const IconData heart_light = IconData(0xf04a, fontFamily: _fontFamily);
  static const IconData heart_straight_break_light = IconData(0xf04b, fontFamily: _fontFamily);
  static const IconData heart_straight_light = IconData(0xf04c, fontFamily: _fontFamily);
  static const IconData heartbeat_light = IconData(0xf04d, fontFamily: _fontFamily);
  static const IconData hexagon_light = IconData(0xf04e, fontFamily: _fontFamily);
  static const IconData highlighter_circle_light = IconData(0xf04f, fontFamily: _fontFamily);
  static const IconData horse_light = IconData(0xf050, fontFamily: _fontFamily);
  static const IconData hourglass_high_light = IconData(0xf051, fontFamily: _fontFamily);
  static const IconData hourglass_light = IconData(0xf052, fontFamily: _fontFamily);
  static const IconData hourglass_low_light = IconData(0xf053, fontFamily: _fontFamily);
  static const IconData hourglass_medium_light = IconData(0xf054, fontFamily: _fontFamily);
  static const IconData hourglass_simple_high_light = IconData(0xf055, fontFamily: _fontFamily);
  static const IconData hourglass_simple_light = IconData(0xf056, fontFamily: _fontFamily);
  static const IconData hourglass_simple_low_light = IconData(0xf057, fontFamily: _fontFamily);
  static const IconData hourglass_simple_medium_light = IconData(0xf058, fontFamily: _fontFamily);
  static const IconData house_light = IconData(0xf059, fontFamily: _fontFamily);
  static const IconData house_line_light = IconData(0xf05a, fontFamily: _fontFamily);
  static const IconData house_simple_light = IconData(0xf05b, fontFamily: _fontFamily);
  static const IconData identification_badge_light = IconData(0xf05c, fontFamily: _fontFamily);
  static const IconData identification_card_light = IconData(0xf05d, fontFamily: _fontFamily);
  static const IconData image_light = IconData(0xf05e, fontFamily: _fontFamily);
  static const IconData image_square_light = IconData(0xf05f, fontFamily: _fontFamily);
  static const IconData infinity_light = IconData(0xf060, fontFamily: _fontFamily);
  static const IconData info_light = IconData(0xf061, fontFamily: _fontFamily);
  static const IconData instagram_logo_light = IconData(0xf062, fontFamily: _fontFamily);
  static const IconData intersect_light = IconData(0xf063, fontFamily: _fontFamily);
  static const IconData jeep_light = IconData(0xf064, fontFamily: _fontFamily);
  static const IconData kanban_light = IconData(0xf065, fontFamily: _fontFamily);
  static const IconData key_light = IconData(0xf066, fontFamily: _fontFamily);
  static const IconData key_return_light = IconData(0xf067, fontFamily: _fontFamily);
  static const IconData keyboard_light = IconData(0xf068, fontFamily: _fontFamily);
  static const IconData keyhole_light = IconData(0xf069, fontFamily: _fontFamily);
  static const IconData knife_light = IconData(0xf06a, fontFamily: _fontFamily);
  static const IconData ladder_light = IconData(0xf06b, fontFamily: _fontFamily);
  static const IconData ladder_simple_light = IconData(0xf06c, fontFamily: _fontFamily);
  static const IconData lamp_light = IconData(0xf06d, fontFamily: _fontFamily);
  static const IconData laptop_light = IconData(0xf06e, fontFamily: _fontFamily);
  static const IconData layout_light = IconData(0xf06f, fontFamily: _fontFamily);
  static const IconData leaf_light = IconData(0xf070, fontFamily: _fontFamily);
  static const IconData lifebuoy_light = IconData(0xf071, fontFamily: _fontFamily);
  static const IconData lightbulb_filament_light = IconData(0xf072, fontFamily: _fontFamily);
  static const IconData lightbulb_light = IconData(0xf073, fontFamily: _fontFamily);
  static const IconData lightning_light = IconData(0xf074, fontFamily: _fontFamily);
  static const IconData lightning_slash_light = IconData(0xf075, fontFamily: _fontFamily);
  static const IconData line_segment_light = IconData(0xf076, fontFamily: _fontFamily);
  static const IconData line_segments_light = IconData(0xf077, fontFamily: _fontFamily);
  static const IconData link_break_light = IconData(0xf078, fontFamily: _fontFamily);
  static const IconData link_light = IconData(0xf079, fontFamily: _fontFamily);
  static const IconData link_simple_break_light = IconData(0xf07a, fontFamily: _fontFamily);
  static const IconData link_simple_horizontal_break_light = IconData(0xf07b, fontFamily: _fontFamily);
  static const IconData link_simple_horizontal_light = IconData(0xf07c, fontFamily: _fontFamily);
  static const IconData link_simple_light = IconData(0xf07d, fontFamily: _fontFamily);
  static const IconData linkedin_logo_light = IconData(0xf07e, fontFamily: _fontFamily);
  static const IconData linux_logo_light = IconData(0xf07f, fontFamily: _fontFamily);
  static const IconData list_bullets_light = IconData(0xf080, fontFamily: _fontFamily);
  static const IconData list_checks_light = IconData(0xf081, fontFamily: _fontFamily);
  static const IconData list_dashes_light = IconData(0xf082, fontFamily: _fontFamily);
  static const IconData list_light = IconData(0xf083, fontFamily: _fontFamily);
  static const IconData list_numbers_light = IconData(0xf084, fontFamily: _fontFamily);
  static const IconData list_plus_light = IconData(0xf085, fontFamily: _fontFamily);
  static const IconData lock_key_light = IconData(0xf086, fontFamily: _fontFamily);
  static const IconData lock_key_open_light = IconData(0xf087, fontFamily: _fontFamily);
  static const IconData lock_laminated_light = IconData(0xf088, fontFamily: _fontFamily);
  static const IconData lock_laminated_open_light = IconData(0xf089, fontFamily: _fontFamily);
  static const IconData lock_light = IconData(0xf08a, fontFamily: _fontFamily);
  static const IconData lock_open_light = IconData(0xf08b, fontFamily: _fontFamily);
  static const IconData lock_simple_light = IconData(0xf08c, fontFamily: _fontFamily);
  static const IconData lock_simple_open_light = IconData(0xf08d, fontFamily: _fontFamily);
  static const IconData magic_wand_light = IconData(0xf08e, fontFamily: _fontFamily);
  static const IconData magnet_light = IconData(0xf08f, fontFamily: _fontFamily);
  static const IconData magnet_straight_light = IconData(0xf090, fontFamily: _fontFamily);
  static const IconData magnifying_glass_light = IconData(0xf091, fontFamily: _fontFamily);
  static const IconData magnifying_glass_minus_light = IconData(0xf092, fontFamily: _fontFamily);
  static const IconData magnifying_glass_plus_light = IconData(0xf093, fontFamily: _fontFamily);
  static const IconData map_pin_light = IconData(0xf094, fontFamily: _fontFamily);
  static const IconData map_pin_line_light = IconData(0xf095, fontFamily: _fontFamily);
  static const IconData map_trifold_light = IconData(0xf096, fontFamily: _fontFamily);
  static const IconData marker_circle_light = IconData(0xf097, fontFamily: _fontFamily);
  static const IconData martini_light = IconData(0xf098, fontFamily: _fontFamily);
  static const IconData mask_happy_light = IconData(0xf099, fontFamily: _fontFamily);
  static const IconData mask_sad_light = IconData(0xf09a, fontFamily: _fontFamily);
  static const IconData math_operations_light = IconData(0xf09b, fontFamily: _fontFamily);
  static const IconData medal_light = IconData(0xf09c, fontFamily: _fontFamily);
  static const IconData medium_logo_light = IconData(0xf09d, fontFamily: _fontFamily);
  static const IconData megaphone_light = IconData(0xf09e, fontFamily: _fontFamily);
  static const IconData megaphone_simple_light = IconData(0xf09f, fontFamily: _fontFamily);
  static const IconData messenger_logo_light = IconData(0xf0a0, fontFamily: _fontFamily);
  static const IconData microphone_light = IconData(0xf0a1, fontFamily: _fontFamily);
  static const IconData microphone_slash_light = IconData(0xf0a2, fontFamily: _fontFamily);
  static const IconData microphone_stage_light = IconData(0xf0a3, fontFamily: _fontFamily);
  static const IconData microsoft_excel_logo_light = IconData(0xf0a4, fontFamily: _fontFamily);
  static const IconData microsoft_powerpoint_logo_light = IconData(0xf0a5, fontFamily: _fontFamily);
  static const IconData microsoft_teams_logo_light = IconData(0xf0a6, fontFamily: _fontFamily);
  static const IconData microsoft_word_logo_light = IconData(0xf0a7, fontFamily: _fontFamily);
  static const IconData minus_circle_light = IconData(0xf0a8, fontFamily: _fontFamily);
  static const IconData minus_light = IconData(0xf0a9, fontFamily: _fontFamily);
  static const IconData money_light = IconData(0xf0aa, fontFamily: _fontFamily);
  static const IconData monitor_light = IconData(0xf0ab, fontFamily: _fontFamily);
  static const IconData monitor_play_light = IconData(0xf0ac, fontFamily: _fontFamily);
  static const IconData moon_light = IconData(0xf0ad, fontFamily: _fontFamily);
  static const IconData moon_stars_light = IconData(0xf0ae, fontFamily: _fontFamily);
  static const IconData mountains_light = IconData(0xf0af, fontFamily: _fontFamily);
  static const IconData mouse_light = IconData(0xf0b0, fontFamily: _fontFamily);
  static const IconData mouse_simple_light = IconData(0xf0b1, fontFamily: _fontFamily);
  static const IconData music_note_light = IconData(0xf0b2, fontFamily: _fontFamily);
  static const IconData music_note_simple_light = IconData(0xf0b3, fontFamily: _fontFamily);
  static const IconData music_notes_light = IconData(0xf0b4, fontFamily: _fontFamily);
  static const IconData music_notes_plus_light = IconData(0xf0b5, fontFamily: _fontFamily);
  static const IconData music_notes_simple_light = IconData(0xf0b6, fontFamily: _fontFamily);
  static const IconData navigation_arrow_light = IconData(0xf0b7, fontFamily: _fontFamily);
  static const IconData needle_light = IconData(0xf0b8, fontFamily: _fontFamily);
  static const IconData newspaper_clipping_light = IconData(0xf0b9, fontFamily: _fontFamily);
  static const IconData newspaper_light = IconData(0xf0ba, fontFamily: _fontFamily);
  static const IconData note_blank_light = IconData(0xf0bb, fontFamily: _fontFamily);
  static const IconData note_light = IconData(0xf0bc, fontFamily: _fontFamily);
  static const IconData note_pencil_light = IconData(0xf0bd, fontFamily: _fontFamily);
  static const IconData notebook_light = IconData(0xf0be, fontFamily: _fontFamily);
  static const IconData notepad_light = IconData(0xf0bf, fontFamily: _fontFamily);
  static const IconData notification_light = IconData(0xf0c0, fontFamily: _fontFamily);
  static const IconData number_circle_eight_light = IconData(0xf0c1, fontFamily: _fontFamily);
  static const IconData number_circle_five_light = IconData(0xf0c2, fontFamily: _fontFamily);
  static const IconData number_circle_four_light = IconData(0xf0c3, fontFamily: _fontFamily);
  static const IconData number_circle_nine_light = IconData(0xf0c4, fontFamily: _fontFamily);
  static const IconData number_circle_one_light = IconData(0xf0c5, fontFamily: _fontFamily);
  static const IconData number_circle_seven_light = IconData(0xf0c6, fontFamily: _fontFamily);
  static const IconData number_circle_six_light = IconData(0xf0c7, fontFamily: _fontFamily);
  static const IconData number_circle_three_light = IconData(0xf0c8, fontFamily: _fontFamily);
  static const IconData number_circle_two_light = IconData(0xf0c9, fontFamily: _fontFamily);
  static const IconData number_circle_zero_light = IconData(0xf0ca, fontFamily: _fontFamily);
  static const IconData number_eight_light = IconData(0xf0cb, fontFamily: _fontFamily);
  static const IconData number_five_light = IconData(0xf0cc, fontFamily: _fontFamily);
  static const IconData number_four_light = IconData(0xf0cd, fontFamily: _fontFamily);
  static const IconData number_nine_light = IconData(0xf0ce, fontFamily: _fontFamily);
  static const IconData number_one_light = IconData(0xf0cf, fontFamily: _fontFamily);
  static const IconData number_seven_light = IconData(0xf0d0, fontFamily: _fontFamily);
  static const IconData number_six_light = IconData(0xf0d1, fontFamily: _fontFamily);
  static const IconData number_square_eight_light = IconData(0xf0d2, fontFamily: _fontFamily);
  static const IconData number_square_five_light = IconData(0xf0d3, fontFamily: _fontFamily);
  static const IconData number_square_four_light = IconData(0xf0d4, fontFamily: _fontFamily);
  static const IconData number_square_nine_light = IconData(0xf0d5, fontFamily: _fontFamily);
  static const IconData number_square_one_light = IconData(0xf0d6, fontFamily: _fontFamily);
  static const IconData number_square_seven_light = IconData(0xf0d7, fontFamily: _fontFamily);
  static const IconData number_square_six_light = IconData(0xf0d8, fontFamily: _fontFamily);
  static const IconData number_square_three_light = IconData(0xf0d9, fontFamily: _fontFamily);
  static const IconData number_square_two_light = IconData(0xf0da, fontFamily: _fontFamily);
  static const IconData number_square_zero_light = IconData(0xf0db, fontFamily: _fontFamily);
  static const IconData number_three_light = IconData(0xf0dc, fontFamily: _fontFamily);
  static const IconData number_two_light = IconData(0xf0dd, fontFamily: _fontFamily);
  static const IconData number_zero_light = IconData(0xf0de, fontFamily: _fontFamily);
  static const IconData nut_light = IconData(0xf0df, fontFamily: _fontFamily);
  static const IconData ny_times_logo_light = IconData(0xf0e0, fontFamily: _fontFamily);
  static const IconData octagon_light = IconData(0xf0e1, fontFamily: _fontFamily);
  static const IconData option_light = IconData(0xf0e2, fontFamily: _fontFamily);
  static const IconData package_light = IconData(0xf0e3, fontFamily: _fontFamily);
  static const IconData paint_brush_broad_light = IconData(0xf0e4, fontFamily: _fontFamily);
  static const IconData paint_brush_household_light = IconData(0xf0e5, fontFamily: _fontFamily);
  static const IconData paint_brush_light = IconData(0xf0e6, fontFamily: _fontFamily);
  static const IconData paint_bucket_light = IconData(0xf0e7, fontFamily: _fontFamily);
  static const IconData paint_roller_light = IconData(0xf0e8, fontFamily: _fontFamily);
  static const IconData palette_light = IconData(0xf0e9, fontFamily: _fontFamily);
  static const IconData paper_plane_light = IconData(0xf0ea, fontFamily: _fontFamily);
  static const IconData paper_plane_right_light = IconData(0xf0eb, fontFamily: _fontFamily);
  static const IconData paper_plane_tilt_light = IconData(0xf0ec, fontFamily: _fontFamily);
  static const IconData paperclip_horizontal_light = IconData(0xf0ed, fontFamily: _fontFamily);
  static const IconData paperclip_light = IconData(0xf0ee, fontFamily: _fontFamily);
  static const IconData parachute_light = IconData(0xf0ef, fontFamily: _fontFamily);
  static const IconData password_light = IconData(0xf0f0, fontFamily: _fontFamily);
  static const IconData path_light = IconData(0xf0f1, fontFamily: _fontFamily);
  static const IconData pause_circle_light = IconData(0xf0f2, fontFamily: _fontFamily);
  static const IconData pause_light = IconData(0xf0f3, fontFamily: _fontFamily);
  static const IconData paw_print_light = IconData(0xf0f4, fontFamily: _fontFamily);
  static const IconData peace_light = IconData(0xf0f5, fontFamily: _fontFamily);
  static const IconData pen_light = IconData(0xf0f6, fontFamily: _fontFamily);
  static const IconData pen_nib_light = IconData(0xf0f7, fontFamily: _fontFamily);
  static const IconData pen_nib_straight_light = IconData(0xf0f8, fontFamily: _fontFamily);
  static const IconData pencil_circle_light = IconData(0xf0f9, fontFamily: _fontFamily);
  static const IconData pencil_light = IconData(0xf0fa, fontFamily: _fontFamily);
  static const IconData pencil_line_light = IconData(0xf0fb, fontFamily: _fontFamily);
  static const IconData pencil_simple_light = IconData(0xf0fc, fontFamily: _fontFamily);
  static const IconData pencil_simple_line_light = IconData(0xf0fd, fontFamily: _fontFamily);
  static const IconData percent_light = IconData(0xf0fe, fontFamily: _fontFamily);
  static const IconData person_light = IconData(0xf0ff, fontFamily: _fontFamily);
  static const IconData person_simple_light = IconData(0xf100, fontFamily: _fontFamily);
  static const IconData person_simple_run_light = IconData(0xf101, fontFamily: _fontFamily);
  static const IconData person_simple_walk_light = IconData(0xf102, fontFamily: _fontFamily);
  static const IconData perspective_light = IconData(0xf103, fontFamily: _fontFamily);
  static const IconData phone_call_light = IconData(0xf104, fontFamily: _fontFamily);
  static const IconData phone_disconnect_light = IconData(0xf105, fontFamily: _fontFamily);
  static const IconData phone_incoming_light = IconData(0xf106, fontFamily: _fontFamily);
  static const IconData phone_light = IconData(0xf107, fontFamily: _fontFamily);
  static const IconData phone_outgoing_light = IconData(0xf108, fontFamily: _fontFamily);
  static const IconData phone_slash_light = IconData(0xf109, fontFamily: _fontFamily);
  static const IconData phone_x_light = IconData(0xf10a, fontFamily: _fontFamily);
  static const IconData phosphor_logo_light = IconData(0xf10b, fontFamily: _fontFamily);
  static const IconData piano_keys_light = IconData(0xf10c, fontFamily: _fontFamily);
  static const IconData picture_in_picture_light = IconData(0xf10d, fontFamily: _fontFamily);
  static const IconData pill_light = IconData(0xf10e, fontFamily: _fontFamily);
  static const IconData pinterest_logo_light = IconData(0xf10f, fontFamily: _fontFamily);
  static const IconData pinwheel_light = IconData(0xf110, fontFamily: _fontFamily);
  static const IconData pizza_light = IconData(0xf111, fontFamily: _fontFamily);
  static const IconData placeholder_light = IconData(0xf112, fontFamily: _fontFamily);
  static const IconData planet_light = IconData(0xf113, fontFamily: _fontFamily);
  static const IconData play_circle_light = IconData(0xf114, fontFamily: _fontFamily);
  static const IconData play_light = IconData(0xf115, fontFamily: _fontFamily);
  static const IconData playlist_light = IconData(0xf116, fontFamily: _fontFamily);
  static const IconData plug_light = IconData(0xf117, fontFamily: _fontFamily);
  static const IconData plugs_connected_light = IconData(0xf118, fontFamily: _fontFamily);
  static const IconData plugs_light = IconData(0xf119, fontFamily: _fontFamily);
  static const IconData plus_circle_light = IconData(0xf11a, fontFamily: _fontFamily);
  static const IconData plus_light = IconData(0xf11b, fontFamily: _fontFamily);
  static const IconData plus_minus_light = IconData(0xf11c, fontFamily: _fontFamily);
  static const IconData poker_chip_light = IconData(0xf11d, fontFamily: _fontFamily);
  static const IconData police_car_light = IconData(0xf11e, fontFamily: _fontFamily);
  static const IconData polygon_light = IconData(0xf11f, fontFamily: _fontFamily);
  static const IconData popcorn_light = IconData(0xf120, fontFamily: _fontFamily);
  static const IconData power_light = IconData(0xf121, fontFamily: _fontFamily);
  static const IconData prescription_light = IconData(0xf122, fontFamily: _fontFamily);
  static const IconData presentation_chart_light = IconData(0xf123, fontFamily: _fontFamily);
  static const IconData presentation_light = IconData(0xf124, fontFamily: _fontFamily);
  static const IconData printer_light = IconData(0xf125, fontFamily: _fontFamily);
  static const IconData prohibit_inset_light = IconData(0xf126, fontFamily: _fontFamily);
  static const IconData prohibit_light = IconData(0xf127, fontFamily: _fontFamily);
  static const IconData projector_screen_chart_light = IconData(0xf128, fontFamily: _fontFamily);
  static const IconData projector_screen_light = IconData(0xf129, fontFamily: _fontFamily);
  static const IconData push_pin_light = IconData(0xf12a, fontFamily: _fontFamily);
  static const IconData push_pin_simple_light = IconData(0xf12b, fontFamily: _fontFamily);
  static const IconData push_pin_simple_slash_light = IconData(0xf12c, fontFamily: _fontFamily);
  static const IconData push_pin_slash_light = IconData(0xf12d, fontFamily: _fontFamily);
  static const IconData puzzle_piece_light = IconData(0xf12e, fontFamily: _fontFamily);
  static const IconData qr_code_light = IconData(0xf12f, fontFamily: _fontFamily);
  static const IconData question_light = IconData(0xf130, fontFamily: _fontFamily);
  static const IconData queue_light = IconData(0xf131, fontFamily: _fontFamily);
  static const IconData quotes_light = IconData(0xf132, fontFamily: _fontFamily);
  static const IconData radical_light = IconData(0xf133, fontFamily: _fontFamily);
  static const IconData radio_button_light = IconData(0xf134, fontFamily: _fontFamily);
  static const IconData radio_light = IconData(0xf135, fontFamily: _fontFamily);
  static const IconData rainbow_cloud_light = IconData(0xf136, fontFamily: _fontFamily);
  static const IconData rainbow_light = IconData(0xf137, fontFamily: _fontFamily);
  static const IconData receipt_light = IconData(0xf138, fontFamily: _fontFamily);
  static const IconData record_light = IconData(0xf139, fontFamily: _fontFamily);
  static const IconData rectangle_light = IconData(0xf13a, fontFamily: _fontFamily);
  static const IconData recycle_light = IconData(0xf13b, fontFamily: _fontFamily);
  static const IconData reddit_logo_light = IconData(0xf13c, fontFamily: _fontFamily);
  static const IconData repeat_light = IconData(0xf13d, fontFamily: _fontFamily);
  static const IconData repeat_once_light = IconData(0xf13e, fontFamily: _fontFamily);
  static const IconData rewind_circle_light = IconData(0xf13f, fontFamily: _fontFamily);
  static const IconData rewind_light = IconData(0xf140, fontFamily: _fontFamily);
  static const IconData robot_light = IconData(0xf141, fontFamily: _fontFamily);
  static const IconData rocket_launch_light = IconData(0xf142, fontFamily: _fontFamily);
  static const IconData rocket_light = IconData(0xf143, fontFamily: _fontFamily);
  static const IconData rows_light = IconData(0xf144, fontFamily: _fontFamily);
  static const IconData rss_light = IconData(0xf145, fontFamily: _fontFamily);
  static const IconData rss_simple_light = IconData(0xf146, fontFamily: _fontFamily);
  static const IconData rug_light = IconData(0xf147, fontFamily: _fontFamily);
  static const IconData ruler_light = IconData(0xf148, fontFamily: _fontFamily);
  static const IconData scales_light = IconData(0xf149, fontFamily: _fontFamily);
  static const IconData scan_light = IconData(0xf14a, fontFamily: _fontFamily);
  static const IconData scissors_light = IconData(0xf14b, fontFamily: _fontFamily);
  static const IconData screencast_light = IconData(0xf14c, fontFamily: _fontFamily);
  static const IconData scribble_loop_light = IconData(0xf14d, fontFamily: _fontFamily);
  static const IconData scroll_light = IconData(0xf14e, fontFamily: _fontFamily);
  static const IconData selection_all_light = IconData(0xf14f, fontFamily: _fontFamily);
  static const IconData selection_background_light = IconData(0xf150, fontFamily: _fontFamily);
  static const IconData selection_foreground_light = IconData(0xf151, fontFamily: _fontFamily);
  static const IconData selection_inverse_light = IconData(0xf152, fontFamily: _fontFamily);
  static const IconData selection_light = IconData(0xf153, fontFamily: _fontFamily);
  static const IconData selection_plus_light = IconData(0xf154, fontFamily: _fontFamily);
  static const IconData selection_slash_light = IconData(0xf155, fontFamily: _fontFamily);
  static const IconData share_light = IconData(0xf156, fontFamily: _fontFamily);
  static const IconData share_network_light = IconData(0xf157, fontFamily: _fontFamily);
  static const IconData shield_check_light = IconData(0xf158, fontFamily: _fontFamily);
  static const IconData shield_checkered_light = IconData(0xf159, fontFamily: _fontFamily);
  static const IconData shield_chevron_light = IconData(0xf15a, fontFamily: _fontFamily);
  static const IconData shield_light = IconData(0xf15b, fontFamily: _fontFamily);
  static const IconData shield_plus_light = IconData(0xf15c, fontFamily: _fontFamily);
  static const IconData shield_slash_light = IconData(0xf15d, fontFamily: _fontFamily);
  static const IconData shield_star_light = IconData(0xf15e, fontFamily: _fontFamily);
  static const IconData shield_warning_light = IconData(0xf15f, fontFamily: _fontFamily);
  static const IconData shopping_bag_light = IconData(0xf160, fontFamily: _fontFamily);
  static const IconData shopping_bag_open_light = IconData(0xf161, fontFamily: _fontFamily);
  static const IconData shopping_cart_light = IconData(0xf162, fontFamily: _fontFamily);
  static const IconData shopping_cart_simple_light = IconData(0xf163, fontFamily: _fontFamily);
  static const IconData shower_light = IconData(0xf164, fontFamily: _fontFamily);
  static const IconData shuffle_angular_light = IconData(0xf165, fontFamily: _fontFamily);
  static const IconData shuffle_light = IconData(0xf166, fontFamily: _fontFamily);
  static const IconData shuffle_simple_light = IconData(0xf167, fontFamily: _fontFamily);
  static const IconData sidebar_light = IconData(0xf168, fontFamily: _fontFamily);
  static const IconData sidebar_simple_light = IconData(0xf169, fontFamily: _fontFamily);
  static const IconData sign_in_light = IconData(0xf16a, fontFamily: _fontFamily);
  static const IconData sign_out_light = IconData(0xf16b, fontFamily: _fontFamily);
  static const IconData signpost_light = IconData(0xf16c, fontFamily: _fontFamily);
  static const IconData sim_card_light = IconData(0xf16d, fontFamily: _fontFamily);
  static const IconData sketch_logo_light = IconData(0xf16e, fontFamily: _fontFamily);
  static const IconData skip_back_circle_light = IconData(0xf16f, fontFamily: _fontFamily);
  static const IconData skip_back_light = IconData(0xf170, fontFamily: _fontFamily);
  static const IconData skip_forward_circle_light = IconData(0xf171, fontFamily: _fontFamily);
  static const IconData skip_forward_light = IconData(0xf172, fontFamily: _fontFamily);
  static const IconData skull_light = IconData(0xf173, fontFamily: _fontFamily);
  static const IconData slack_logo_light = IconData(0xf174, fontFamily: _fontFamily);
  static const IconData sliders_horizontal_light = IconData(0xf175, fontFamily: _fontFamily);
  static const IconData sliders_light = IconData(0xf176, fontFamily: _fontFamily);
  static const IconData smiley_blank_light = IconData(0xf177, fontFamily: _fontFamily);
  static const IconData smiley_light = IconData(0xf178, fontFamily: _fontFamily);
  static const IconData smiley_meh_light = IconData(0xf179, fontFamily: _fontFamily);
  static const IconData smiley_nervous_light = IconData(0xf17a, fontFamily: _fontFamily);
  static const IconData smiley_sad_light = IconData(0xf17b, fontFamily: _fontFamily);
  static const IconData smiley_sticker_light = IconData(0xf17c, fontFamily: _fontFamily);
  static const IconData smiley_wink_light = IconData(0xf17d, fontFamily: _fontFamily);
  static const IconData smiley_x_eyes_light = IconData(0xf17e, fontFamily: _fontFamily);
  static const IconData snapchat_logo_light = IconData(0xf17f, fontFamily: _fontFamily);
  static const IconData snowflake_light = IconData(0xf180, fontFamily: _fontFamily);
  static const IconData soccer_ball_light = IconData(0xf181, fontFamily: _fontFamily);
  static const IconData sort_ascending_light = IconData(0xf182, fontFamily: _fontFamily);
  static const IconData sort_descending_light = IconData(0xf183, fontFamily: _fontFamily);
  static const IconData spade_light = IconData(0xf184, fontFamily: _fontFamily);
  static const IconData sparkle_light = IconData(0xf185, fontFamily: _fontFamily);
  static const IconData speaker_high_light = IconData(0xf186, fontFamily: _fontFamily);
  static const IconData speaker_low_light = IconData(0xf187, fontFamily: _fontFamily);
  static const IconData speaker_none_light = IconData(0xf188, fontFamily: _fontFamily);
  static const IconData speaker_simple_high_light = IconData(0xf189, fontFamily: _fontFamily);
  static const IconData speaker_simple_low_light = IconData(0xf18a, fontFamily: _fontFamily);
  static const IconData speaker_simple_none_light = IconData(0xf18b, fontFamily: _fontFamily);
  static const IconData speaker_simple_slash_light = IconData(0xf18c, fontFamily: _fontFamily);
  static const IconData speaker_simple_x_light = IconData(0xf18d, fontFamily: _fontFamily);
  static const IconData speaker_slash_light = IconData(0xf18e, fontFamily: _fontFamily);
  static const IconData speaker_x_light = IconData(0xf18f, fontFamily: _fontFamily);
  static const IconData spinner_gap_light = IconData(0xf190, fontFamily: _fontFamily);
  static const IconData spinner_light = IconData(0xf191, fontFamily: _fontFamily);
  static const IconData spiral_light = IconData(0xf192, fontFamily: _fontFamily);
  static const IconData spotify_logo_light = IconData(0xf193, fontFamily: _fontFamily);
  static const IconData square_half_bottom_light = IconData(0xf194, fontFamily: _fontFamily);
  static const IconData square_half_light = IconData(0xf195, fontFamily: _fontFamily);
  static const IconData square_light = IconData(0xf196, fontFamily: _fontFamily);
  static const IconData square_logo_light = IconData(0xf197, fontFamily: _fontFamily);
  static const IconData squares_four_light = IconData(0xf198, fontFamily: _fontFamily);
  static const IconData stack_light = IconData(0xf199, fontFamily: _fontFamily);
  static const IconData stack_overflow_logo_light = IconData(0xf19a, fontFamily: _fontFamily);
  static const IconData stack_simple_light = IconData(0xf19b, fontFamily: _fontFamily);
  static const IconData stamp_light = IconData(0xf19c, fontFamily: _fontFamily);
  static const IconData star_four_light = IconData(0xf19d, fontFamily: _fontFamily);
  static const IconData star_half_light = IconData(0xf19e, fontFamily: _fontFamily);
  static const IconData star_light = IconData(0xf19f, fontFamily: _fontFamily);
  static const IconData sticker_light = IconData(0xf1a0, fontFamily: _fontFamily);
  static const IconData stop_circle_light = IconData(0xf1a1, fontFamily: _fontFamily);
  static const IconData stop_light = IconData(0xf1a2, fontFamily: _fontFamily);
  static const IconData storefront_light = IconData(0xf1a3, fontFamily: _fontFamily);
  static const IconData strategy_light = IconData(0xf1a4, fontFamily: _fontFamily);
  static const IconData stripe_logo_light = IconData(0xf1a5, fontFamily: _fontFamily);
  static const IconData student_light = IconData(0xf1a6, fontFamily: _fontFamily);
  static const IconData suitcase_light = IconData(0xf1a7, fontFamily: _fontFamily);
  static const IconData suitcase_simple_light = IconData(0xf1a8, fontFamily: _fontFamily);
  static const IconData sun_dim_light = IconData(0xf1a9, fontFamily: _fontFamily);
  static const IconData sun_horizon_light = IconData(0xf1aa, fontFamily: _fontFamily);
  static const IconData sun_light = IconData(0xf1ab, fontFamily: _fontFamily);
  static const IconData sunglasses_light = IconData(0xf1ac, fontFamily: _fontFamily);
  static const IconData swap_light = IconData(0xf1ad, fontFamily: _fontFamily);
  static const IconData swatches_light = IconData(0xf1ae, fontFamily: _fontFamily);
  static const IconData sword_light = IconData(0xf1af, fontFamily: _fontFamily);
  static const IconData syringe_light = IconData(0xf1b0, fontFamily: _fontFamily);
  static const IconData t_shirt_light = IconData(0xf1b1, fontFamily: _fontFamily);
  static const IconData table_light = IconData(0xf1b2, fontFamily: _fontFamily);
  static const IconData tabs_light = IconData(0xf1b3, fontFamily: _fontFamily);
  static const IconData tag_chevron_light = IconData(0xf1b4, fontFamily: _fontFamily);
  static const IconData tag_light = IconData(0xf1b5, fontFamily: _fontFamily);
  static const IconData tag_simple_light = IconData(0xf1b6, fontFamily: _fontFamily);
  static const IconData target_light = IconData(0xf1b7, fontFamily: _fontFamily);
  static const IconData taxi_light = IconData(0xf1b8, fontFamily: _fontFamily);
  static const IconData telegram_logo_light = IconData(0xf1b9, fontFamily: _fontFamily);
  static const IconData television_light = IconData(0xf1ba, fontFamily: _fontFamily);
  static const IconData television_simple_light = IconData(0xf1bb, fontFamily: _fontFamily);
  static const IconData tennis_ball_light = IconData(0xf1bc, fontFamily: _fontFamily);
  static const IconData terminal_light = IconData(0xf1bd, fontFamily: _fontFamily);
  static const IconData terminal_window_light = IconData(0xf1be, fontFamily: _fontFamily);
  static const IconData test_tube_light = IconData(0xf1bf, fontFamily: _fontFamily);
  static const IconData text_aa_light = IconData(0xf1c0, fontFamily: _fontFamily);
  static const IconData text_align_center_light = IconData(0xf1c1, fontFamily: _fontFamily);
  static const IconData text_align_justify_light = IconData(0xf1c2, fontFamily: _fontFamily);
  static const IconData text_align_left_light = IconData(0xf1c3, fontFamily: _fontFamily);
  static const IconData text_align_right_light = IconData(0xf1c4, fontFamily: _fontFamily);
  static const IconData text_bolder_light = IconData(0xf1c5, fontFamily: _fontFamily);
  static const IconData text_h_five_light = IconData(0xf1c6, fontFamily: _fontFamily);
  static const IconData text_h_four_light = IconData(0xf1c7, fontFamily: _fontFamily);
  static const IconData text_h_light = IconData(0xf1c8, fontFamily: _fontFamily);
  static const IconData text_h_one_light = IconData(0xf1c9, fontFamily: _fontFamily);
  static const IconData text_h_six_light = IconData(0xf1ca, fontFamily: _fontFamily);
  static const IconData text_h_three_light = IconData(0xf1cb, fontFamily: _fontFamily);
  static const IconData text_h_two_light = IconData(0xf1cc, fontFamily: _fontFamily);
  static const IconData text_indent_light = IconData(0xf1cd, fontFamily: _fontFamily);
  static const IconData text_italic_light = IconData(0xf1ce, fontFamily: _fontFamily);
  static const IconData text_outdent_light = IconData(0xf1cf, fontFamily: _fontFamily);
  static const IconData text_strikethrough_light = IconData(0xf1d0, fontFamily: _fontFamily);
  static const IconData text_t_light = IconData(0xf1d1, fontFamily: _fontFamily);
  static const IconData text_underline_light = IconData(0xf1d2, fontFamily: _fontFamily);
  static const IconData textbox_light = IconData(0xf1d3, fontFamily: _fontFamily);
  static const IconData thermometer_cold_light = IconData(0xf1d4, fontFamily: _fontFamily);
  static const IconData thermometer_hot_light = IconData(0xf1d5, fontFamily: _fontFamily);
  static const IconData thermometer_light = IconData(0xf1d6, fontFamily: _fontFamily);
  static const IconData thermometer_simple_light = IconData(0xf1d7, fontFamily: _fontFamily);
  static const IconData thumbs_down_light = IconData(0xf1d8, fontFamily: _fontFamily);
  static const IconData thumbs_up_light = IconData(0xf1d9, fontFamily: _fontFamily);
  static const IconData ticket_light = IconData(0xf1da, fontFamily: _fontFamily);
  static const IconData tiktok_logo_light = IconData(0xf1db, fontFamily: _fontFamily);
  static const IconData timer_light = IconData(0xf1dc, fontFamily: _fontFamily);
  static const IconData toggle_left_light = IconData(0xf1dd, fontFamily: _fontFamily);
  static const IconData toggle_right_light = IconData(0xf1de, fontFamily: _fontFamily);
  static const IconData toilet_light = IconData(0xf1df, fontFamily: _fontFamily);
  static const IconData toilet_paper_light = IconData(0xf1e0, fontFamily: _fontFamily);
  static const IconData tote_light = IconData(0xf1e1, fontFamily: _fontFamily);
  static const IconData tote_simple_light = IconData(0xf1e2, fontFamily: _fontFamily);
  static const IconData trademark_registered_light = IconData(0xf1e3, fontFamily: _fontFamily);
  static const IconData traffic_cone_light = IconData(0xf1e4, fontFamily: _fontFamily);
  static const IconData traffic_sign_light = IconData(0xf1e5, fontFamily: _fontFamily);
  static const IconData traffic_signal_light = IconData(0xf1e6, fontFamily: _fontFamily);
  static const IconData train_light = IconData(0xf1e7, fontFamily: _fontFamily);
  static const IconData train_regional_light = IconData(0xf1e8, fontFamily: _fontFamily);
  static const IconData train_simple_light = IconData(0xf1e9, fontFamily: _fontFamily);
  static const IconData translate_light = IconData(0xf1ea, fontFamily: _fontFamily);
  static const IconData trash_light = IconData(0xf1eb, fontFamily: _fontFamily);
  static const IconData trash_simple_light = IconData(0xf1ec, fontFamily: _fontFamily);
  static const IconData tray_light = IconData(0xf1ed, fontFamily: _fontFamily);
  static const IconData tree_evergreen_light = IconData(0xf1ee, fontFamily: _fontFamily);
  static const IconData tree_light = IconData(0xf1ef, fontFamily: _fontFamily);
  static const IconData tree_structure_light = IconData(0xf1f0, fontFamily: _fontFamily);
  static const IconData trend_down_light = IconData(0xf1f1, fontFamily: _fontFamily);
  static const IconData trend_up_light = IconData(0xf1f2, fontFamily: _fontFamily);
  static const IconData triangle_light = IconData(0xf1f3, fontFamily: _fontFamily);
  static const IconData trophy_light = IconData(0xf1f4, fontFamily: _fontFamily);
  static const IconData truck_light = IconData(0xf1f5, fontFamily: _fontFamily);
  static const IconData twitch_logo_light = IconData(0xf1f6, fontFamily: _fontFamily);
  static const IconData twitter_logo_light = IconData(0xf1f7, fontFamily: _fontFamily);
  static const IconData umbrella_light = IconData(0xf1f8, fontFamily: _fontFamily);
  static const IconData umbrella_simple_light = IconData(0xf1f9, fontFamily: _fontFamily);
  static const IconData upload_light = IconData(0xf1fa, fontFamily: _fontFamily);
  static const IconData upload_simple_light = IconData(0xf1fb, fontFamily: _fontFamily);
  static const IconData user_circle_gear_light = IconData(0xf1fc, fontFamily: _fontFamily);
  static const IconData user_circle_light = IconData(0xf1fd, fontFamily: _fontFamily);
  static const IconData user_circle_minus_light = IconData(0xf1fe, fontFamily: _fontFamily);
  static const IconData user_circle_plus_light = IconData(0xf1ff, fontFamily: _fontFamily);
  static const IconData user_focus_light = IconData(0xf200, fontFamily: _fontFamily);
  static const IconData user_gear_light = IconData(0xf201, fontFamily: _fontFamily);
  static const IconData user_light = IconData(0xf202, fontFamily: _fontFamily);
  static const IconData user_list_light = IconData(0xf203, fontFamily: _fontFamily);
  static const IconData user_minus_light = IconData(0xf204, fontFamily: _fontFamily);
  static const IconData user_plus_light = IconData(0xf205, fontFamily: _fontFamily);
  static const IconData user_rectangle_light = IconData(0xf206, fontFamily: _fontFamily);
  static const IconData user_square_light = IconData(0xf207, fontFamily: _fontFamily);
  static const IconData user_switch_light = IconData(0xf208, fontFamily: _fontFamily);
  static const IconData users_four_light = IconData(0xf209, fontFamily: _fontFamily);
  static const IconData users_light = IconData(0xf20a, fontFamily: _fontFamily);
  static const IconData users_three_light = IconData(0xf20b, fontFamily: _fontFamily);
  static const IconData vault_light = IconData(0xf20c, fontFamily: _fontFamily);
  static const IconData vibrate_light = IconData(0xf20d, fontFamily: _fontFamily);
  static const IconData video_camera_light = IconData(0xf20e, fontFamily: _fontFamily);
  static const IconData video_camera_slash_light = IconData(0xf20f, fontFamily: _fontFamily);
  static const IconData vignette_light = IconData(0xf210, fontFamily: _fontFamily);
  static const IconData voicemail_light = IconData(0xf211, fontFamily: _fontFamily);
  static const IconData volleyball_light = IconData(0xf212, fontFamily: _fontFamily);
  static const IconData wall_light = IconData(0xf213, fontFamily: _fontFamily);
  static const IconData wallet_light = IconData(0xf214, fontFamily: _fontFamily);
  static const IconData warning_circle_light = IconData(0xf215, fontFamily: _fontFamily);
  static const IconData warning_light = IconData(0xf216, fontFamily: _fontFamily);
  static const IconData warning_octagon_light = IconData(0xf217, fontFamily: _fontFamily);
  static const IconData watch_light = IconData(0xf218, fontFamily: _fontFamily);
  static const IconData wave_sawtooth_light = IconData(0xf219, fontFamily: _fontFamily);
  static const IconData wave_sine_light = IconData(0xf21a, fontFamily: _fontFamily);
  static const IconData wave_square_light = IconData(0xf21b, fontFamily: _fontFamily);
  static const IconData wave_triangle_light = IconData(0xf21c, fontFamily: _fontFamily);
  static const IconData waves_light = IconData(0xf21d, fontFamily: _fontFamily);
  static const IconData webcam_light = IconData(0xf21e, fontFamily: _fontFamily);
  static const IconData whatsapp_logo_light = IconData(0xf21f, fontFamily: _fontFamily);
  static const IconData wheelchair_light = IconData(0xf220, fontFamily: _fontFamily);
  static const IconData wifi_high_light = IconData(0xf221, fontFamily: _fontFamily);
  static const IconData wifi_low_light = IconData(0xf222, fontFamily: _fontFamily);
  static const IconData wifi_medium_light = IconData(0xf223, fontFamily: _fontFamily);
  static const IconData wifi_none_light = IconData(0xf224, fontFamily: _fontFamily);
  static const IconData wifi_slash_light = IconData(0xf225, fontFamily: _fontFamily);
  static const IconData wifi_x_light = IconData(0xf226, fontFamily: _fontFamily);
  static const IconData wind_light = IconData(0xf227, fontFamily: _fontFamily);
  static const IconData windows_logo_light = IconData(0xf228, fontFamily: _fontFamily);
  static const IconData wine_light = IconData(0xf229, fontFamily: _fontFamily);
  static const IconData wrench_light = IconData(0xf22a, fontFamily: _fontFamily);
  static const IconData x_circle_light = IconData(0xf22b, fontFamily: _fontFamily);
  static const IconData x_light = IconData(0xf22c, fontFamily: _fontFamily);
  static const IconData x_square_light = IconData(0xf22d, fontFamily: _fontFamily);
  static const IconData yin_yang_light = IconData(0xf22e, fontFamily: _fontFamily);
  static const IconData youtube_logo_light = IconData(0xf22f, fontFamily: _fontFamily);
  static const IconData activity = IconData(0xf230, fontFamily: _fontFamily);
  static const IconData address_book = IconData(0xf231, fontFamily: _fontFamily);
  static const IconData airplane = IconData(0xf232, fontFamily: _fontFamily);
  static const IconData airplane_in_flight = IconData(0xf233, fontFamily: _fontFamily);
  static const IconData airplane_landing = IconData(0xf234, fontFamily: _fontFamily);
  static const IconData airplane_takeoff = IconData(0xf235, fontFamily: _fontFamily);
  static const IconData airplane_tilt = IconData(0xf236, fontFamily: _fontFamily);
  static const IconData airplay = IconData(0xf237, fontFamily: _fontFamily);
  static const IconData alarm = IconData(0xf238, fontFamily: _fontFamily);
  static const IconData alien = IconData(0xf239, fontFamily: _fontFamily);
  static const IconData align_bottom = IconData(0xf23a, fontFamily: _fontFamily);
  static const IconData align_bottom_simple = IconData(0xf23b, fontFamily: _fontFamily);
  static const IconData align_center_horizontal = IconData(0xf23c, fontFamily: _fontFamily);
  static const IconData align_center_horizontal_simple = IconData(0xf23d, fontFamily: _fontFamily);
  static const IconData align_center_vertical = IconData(0xf23e, fontFamily: _fontFamily);
  static const IconData align_center_vertical_simple = IconData(0xf23f, fontFamily: _fontFamily);
  static const IconData align_left = IconData(0xf240, fontFamily: _fontFamily);
  static const IconData align_left_simple = IconData(0xf241, fontFamily: _fontFamily);
  static const IconData align_right = IconData(0xf242, fontFamily: _fontFamily);
  static const IconData align_right_simple = IconData(0xf243, fontFamily: _fontFamily);
  static const IconData align_top = IconData(0xf244, fontFamily: _fontFamily);
  static const IconData align_top_simple = IconData(0xf245, fontFamily: _fontFamily);
  static const IconData anchor = IconData(0xf246, fontFamily: _fontFamily);
  static const IconData anchor_simple = IconData(0xf247, fontFamily: _fontFamily);
  static const IconData android_logo = IconData(0xf248, fontFamily: _fontFamily);
  static const IconData angular_logo = IconData(0xf249, fontFamily: _fontFamily);
  static const IconData aperture = IconData(0xf24a, fontFamily: _fontFamily);
  static const IconData app_store_logo = IconData(0xf24b, fontFamily: _fontFamily);
  static const IconData app_window = IconData(0xf24c, fontFamily: _fontFamily);
  static const IconData apple_logo = IconData(0xf24d, fontFamily: _fontFamily);
  static const IconData apple_podcasts_logo = IconData(0xf24e, fontFamily: _fontFamily);
  static const IconData archive = IconData(0xf24f, fontFamily: _fontFamily);
  static const IconData archive_box = IconData(0xf250, fontFamily: _fontFamily);
  static const IconData archive_tray = IconData(0xf251, fontFamily: _fontFamily);
  static const IconData armchair = IconData(0xf252, fontFamily: _fontFamily);
  static const IconData arrow_arc_left = IconData(0xf253, fontFamily: _fontFamily);
  static const IconData arrow_arc_right = IconData(0xf254, fontFamily: _fontFamily);
  static const IconData arrow_bend_double_up_left = IconData(0xf255, fontFamily: _fontFamily);
  static const IconData arrow_bend_double_up_right = IconData(0xf256, fontFamily: _fontFamily);
  static const IconData arrow_bend_down_left = IconData(0xf257, fontFamily: _fontFamily);
  static const IconData arrow_bend_down_right = IconData(0xf258, fontFamily: _fontFamily);
  static const IconData arrow_bend_left_down = IconData(0xf259, fontFamily: _fontFamily);
  static const IconData arrow_bend_left_up = IconData(0xf25a, fontFamily: _fontFamily);
  static const IconData arrow_bend_right_down = IconData(0xf25b, fontFamily: _fontFamily);
  static const IconData arrow_bend_right_up = IconData(0xf25c, fontFamily: _fontFamily);
  static const IconData arrow_bend_up_left = IconData(0xf25d, fontFamily: _fontFamily);
  static const IconData arrow_bend_up_right = IconData(0xf25e, fontFamily: _fontFamily);
  static const IconData arrow_circle_down = IconData(0xf25f, fontFamily: _fontFamily);
  static const IconData arrow_circle_down_left = IconData(0xf260, fontFamily: _fontFamily);
  static const IconData arrow_circle_down_right = IconData(0xf261, fontFamily: _fontFamily);
  static const IconData arrow_circle_left = IconData(0xf262, fontFamily: _fontFamily);
  static const IconData arrow_circle_right = IconData(0xf263, fontFamily: _fontFamily);
  static const IconData arrow_circle_up = IconData(0xf264, fontFamily: _fontFamily);
  static const IconData arrow_circle_up_left = IconData(0xf265, fontFamily: _fontFamily);
  static const IconData arrow_circle_up_right = IconData(0xf266, fontFamily: _fontFamily);
  static const IconData arrow_clockwise = IconData(0xf267, fontFamily: _fontFamily);
  static const IconData arrow_counter_clockwise = IconData(0xf268, fontFamily: _fontFamily);
  static const IconData arrow_down = IconData(0xf269, fontFamily: _fontFamily);
  static const IconData arrow_down_left = IconData(0xf26a, fontFamily: _fontFamily);
  static const IconData arrow_down_right = IconData(0xf26b, fontFamily: _fontFamily);
  static const IconData arrow_elbow_down_left = IconData(0xf26c, fontFamily: _fontFamily);
  static const IconData arrow_elbow_down_right = IconData(0xf26d, fontFamily: _fontFamily);
  static const IconData arrow_elbow_left = IconData(0xf26e, fontFamily: _fontFamily);
  static const IconData arrow_elbow_left_down = IconData(0xf26f, fontFamily: _fontFamily);
  static const IconData arrow_elbow_left_up = IconData(0xf270, fontFamily: _fontFamily);
  static const IconData arrow_elbow_right = IconData(0xf271, fontFamily: _fontFamily);
  static const IconData arrow_elbow_right_down = IconData(0xf272, fontFamily: _fontFamily);
  static const IconData arrow_elbow_right_up = IconData(0xf273, fontFamily: _fontFamily);
  static const IconData arrow_elbow_up_left = IconData(0xf274, fontFamily: _fontFamily);
  static const IconData arrow_elbow_up_right = IconData(0xf275, fontFamily: _fontFamily);
  static const IconData arrow_fat_down = IconData(0xf276, fontFamily: _fontFamily);
  static const IconData arrow_fat_left = IconData(0xf277, fontFamily: _fontFamily);
  static const IconData arrow_fat_line_down = IconData(0xf278, fontFamily: _fontFamily);
  static const IconData arrow_fat_line_left = IconData(0xf279, fontFamily: _fontFamily);
  static const IconData arrow_fat_line_right = IconData(0xf27a, fontFamily: _fontFamily);
  static const IconData arrow_fat_line_up = IconData(0xf27b, fontFamily: _fontFamily);
  static const IconData arrow_fat_lines_down = IconData(0xf27c, fontFamily: _fontFamily);
  static const IconData arrow_fat_lines_left = IconData(0xf27d, fontFamily: _fontFamily);
  static const IconData arrow_fat_lines_right = IconData(0xf27e, fontFamily: _fontFamily);
  static const IconData arrow_fat_lines_up = IconData(0xf27f, fontFamily: _fontFamily);
  static const IconData arrow_fat_right = IconData(0xf280, fontFamily: _fontFamily);
  static const IconData arrow_fat_up = IconData(0xf281, fontFamily: _fontFamily);
  static const IconData arrow_left = IconData(0xf282, fontFamily: _fontFamily);
  static const IconData arrow_line_down = IconData(0xf283, fontFamily: _fontFamily);
  static const IconData arrow_line_down_left = IconData(0xf284, fontFamily: _fontFamily);
  static const IconData arrow_line_down_right = IconData(0xf285, fontFamily: _fontFamily);
  static const IconData arrow_line_left = IconData(0xf286, fontFamily: _fontFamily);
  static const IconData arrow_line_right = IconData(0xf287, fontFamily: _fontFamily);
  static const IconData arrow_line_up = IconData(0xf288, fontFamily: _fontFamily);
  static const IconData arrow_line_up_left = IconData(0xf289, fontFamily: _fontFamily);
  static const IconData arrow_line_up_right = IconData(0xf28a, fontFamily: _fontFamily);
  static const IconData arrow_right = IconData(0xf28b, fontFamily: _fontFamily);
  static const IconData arrow_square_down = IconData(0xf28c, fontFamily: _fontFamily);
  static const IconData arrow_square_down_left = IconData(0xf28d, fontFamily: _fontFamily);
  static const IconData arrow_square_down_right = IconData(0xf28e, fontFamily: _fontFamily);
  static const IconData arrow_square_in = IconData(0xf28f, fontFamily: _fontFamily);
  static const IconData arrow_square_left = IconData(0xf290, fontFamily: _fontFamily);
  static const IconData arrow_square_out = IconData(0xf291, fontFamily: _fontFamily);
  static const IconData arrow_square_right = IconData(0xf292, fontFamily: _fontFamily);
  static const IconData arrow_square_up = IconData(0xf293, fontFamily: _fontFamily);
  static const IconData arrow_square_up_left = IconData(0xf294, fontFamily: _fontFamily);
  static const IconData arrow_square_up_right = IconData(0xf295, fontFamily: _fontFamily);
  static const IconData arrow_u_down_left = IconData(0xf296, fontFamily: _fontFamily);
  static const IconData arrow_u_down_right = IconData(0xf297, fontFamily: _fontFamily);
  static const IconData arrow_u_left_down = IconData(0xf298, fontFamily: _fontFamily);
  static const IconData arrow_u_left_up = IconData(0xf299, fontFamily: _fontFamily);
  static const IconData arrow_u_right_down = IconData(0xf29a, fontFamily: _fontFamily);
  static const IconData arrow_u_right_up = IconData(0xf29b, fontFamily: _fontFamily);
  static const IconData arrow_u_up_left = IconData(0xf29c, fontFamily: _fontFamily);
  static const IconData arrow_u_up_right = IconData(0xf29d, fontFamily: _fontFamily);
  static const IconData arrow_up = IconData(0xf29e, fontFamily: _fontFamily);
  static const IconData arrow_up_left = IconData(0xf29f, fontFamily: _fontFamily);
  static const IconData arrow_up_right = IconData(0xf2a0, fontFamily: _fontFamily);
  static const IconData arrows_clockwise = IconData(0xf2a1, fontFamily: _fontFamily);
  static const IconData arrows_counter_clockwise = IconData(0xf2a2, fontFamily: _fontFamily);
  static const IconData arrows_down_up = IconData(0xf2a3, fontFamily: _fontFamily);
  static const IconData arrows_horizontal = IconData(0xf2a4, fontFamily: _fontFamily);
  static const IconData arrows_in = IconData(0xf2a5, fontFamily: _fontFamily);
  static const IconData arrows_in_cardinal = IconData(0xf2a6, fontFamily: _fontFamily);
  static const IconData arrows_in_line_horizontal = IconData(0xf2a7, fontFamily: _fontFamily);
  static const IconData arrows_in_line_vertical = IconData(0xf2a8, fontFamily: _fontFamily);
  static const IconData arrows_in_simple = IconData(0xf2a9, fontFamily: _fontFamily);
  static const IconData arrows_left_right = IconData(0xf2aa, fontFamily: _fontFamily);
  static const IconData arrows_out = IconData(0xf2ab, fontFamily: _fontFamily);
  static const IconData arrows_out_cardinal = IconData(0xf2ac, fontFamily: _fontFamily);
  static const IconData arrows_out_line_horizontal = IconData(0xf2ad, fontFamily: _fontFamily);
  static const IconData arrows_out_line_vertical = IconData(0xf2ae, fontFamily: _fontFamily);
  static const IconData arrows_out_simple = IconData(0xf2af, fontFamily: _fontFamily);
  static const IconData arrows_vertical = IconData(0xf2b0, fontFamily: _fontFamily);
  static const IconData article = IconData(0xf2b1, fontFamily: _fontFamily);
  static const IconData article_medium = IconData(0xf2b2, fontFamily: _fontFamily);
  static const IconData article_ny_times = IconData(0xf2b3, fontFamily: _fontFamily);
  static const IconData asterisk = IconData(0xf2b4, fontFamily: _fontFamily);
  static const IconData asterisk_simple = IconData(0xf2b5, fontFamily: _fontFamily);
  static const IconData at = IconData(0xf2b6, fontFamily: _fontFamily);
  static const IconData atom = IconData(0xf2b7, fontFamily: _fontFamily);
  static const IconData baby = IconData(0xf2b8, fontFamily: _fontFamily);
  static const IconData backpack = IconData(0xf2b9, fontFamily: _fontFamily);
  static const IconData backspace = IconData(0xf2ba, fontFamily: _fontFamily);
  static const IconData bag = IconData(0xf2bb, fontFamily: _fontFamily);
  static const IconData bag_simple = IconData(0xf2bc, fontFamily: _fontFamily);
  static const IconData balloon = IconData(0xf2bd, fontFamily: _fontFamily);
  static const IconData bandaids = IconData(0xf2be, fontFamily: _fontFamily);
  static const IconData bank = IconData(0xf2bf, fontFamily: _fontFamily);
  static const IconData barbell = IconData(0xf2c0, fontFamily: _fontFamily);
  static const IconData barcode = IconData(0xf2c1, fontFamily: _fontFamily);
  static const IconData barricade = IconData(0xf2c2, fontFamily: _fontFamily);
  static const IconData baseball = IconData(0xf2c3, fontFamily: _fontFamily);
  static const IconData basketball = IconData(0xf2c4, fontFamily: _fontFamily);
  static const IconData bathtub = IconData(0xf2c5, fontFamily: _fontFamily);
  static const IconData battery_charging = IconData(0xf2c6, fontFamily: _fontFamily);
  static const IconData battery_charging_vertical = IconData(0xf2c7, fontFamily: _fontFamily);
  static const IconData battery_empty = IconData(0xf2c8, fontFamily: _fontFamily);
  static const IconData battery_full = IconData(0xf2c9, fontFamily: _fontFamily);
  static const IconData battery_high = IconData(0xf2ca, fontFamily: _fontFamily);
  static const IconData battery_low = IconData(0xf2cb, fontFamily: _fontFamily);
  static const IconData battery_medium = IconData(0xf2cc, fontFamily: _fontFamily);
  static const IconData battery_plus = IconData(0xf2cd, fontFamily: _fontFamily);
  static const IconData battery_warning = IconData(0xf2ce, fontFamily: _fontFamily);
  static const IconData battery_warning_vertical = IconData(0xf2cf, fontFamily: _fontFamily);
  static const IconData bed = IconData(0xf2d0, fontFamily: _fontFamily);
  static const IconData beer_bottle = IconData(0xf2d1, fontFamily: _fontFamily);
  static const IconData behance_logo = IconData(0xf2d2, fontFamily: _fontFamily);
  static const IconData bell = IconData(0xf2d3, fontFamily: _fontFamily);
  static const IconData bell_ringing = IconData(0xf2d4, fontFamily: _fontFamily);
  static const IconData bell_simple = IconData(0xf2d5, fontFamily: _fontFamily);
  static const IconData bell_simple_ringing = IconData(0xf2d6, fontFamily: _fontFamily);
  static const IconData bell_simple_slash = IconData(0xf2d7, fontFamily: _fontFamily);
  static const IconData bell_simple_z = IconData(0xf2d8, fontFamily: _fontFamily);
  static const IconData bell_slash = IconData(0xf2d9, fontFamily: _fontFamily);
  static const IconData bell_z = IconData(0xf2da, fontFamily: _fontFamily);
  static const IconData bezier_curve = IconData(0xf2db, fontFamily: _fontFamily);
  static const IconData bicycle = IconData(0xf2dc, fontFamily: _fontFamily);
  static const IconData binoculars = IconData(0xf2dd, fontFamily: _fontFamily);
  static const IconData bird = IconData(0xf2de, fontFamily: _fontFamily);
  static const IconData bluetooth = IconData(0xf2df, fontFamily: _fontFamily);
  static const IconData bluetooth_connected = IconData(0xf2e0, fontFamily: _fontFamily);
  static const IconData bluetooth_slash = IconData(0xf2e1, fontFamily: _fontFamily);
  static const IconData bluetooth_x = IconData(0xf2e2, fontFamily: _fontFamily);
  static const IconData boat = IconData(0xf2e3, fontFamily: _fontFamily);
  static const IconData book = IconData(0xf2e4, fontFamily: _fontFamily);
  static const IconData book_bookmark = IconData(0xf2e5, fontFamily: _fontFamily);
  static const IconData book_open = IconData(0xf2e6, fontFamily: _fontFamily);
  static const IconData bookmark = IconData(0xf2e7, fontFamily: _fontFamily);
  static const IconData bookmark_simple = IconData(0xf2e8, fontFamily: _fontFamily);
  static const IconData bookmarks = IconData(0xf2e9, fontFamily: _fontFamily);
  static const IconData bookmarks_simple = IconData(0xf2ea, fontFamily: _fontFamily);
  static const IconData books = IconData(0xf2eb, fontFamily: _fontFamily);
  static const IconData bounding_box = IconData(0xf2ec, fontFamily: _fontFamily);
  static const IconData brackets_angle = IconData(0xf2ed, fontFamily: _fontFamily);
  static const IconData brackets_curly = IconData(0xf2ee, fontFamily: _fontFamily);
  static const IconData brackets_round = IconData(0xf2ef, fontFamily: _fontFamily);
  static const IconData brackets_square = IconData(0xf2f0, fontFamily: _fontFamily);
  static const IconData brain = IconData(0xf2f1, fontFamily: _fontFamily);
  static const IconData brandy = IconData(0xf2f2, fontFamily: _fontFamily);
  static const IconData briefcase = IconData(0xf2f3, fontFamily: _fontFamily);
  static const IconData briefcase_metal = IconData(0xf2f4, fontFamily: _fontFamily);
  static const IconData broadcast = IconData(0xf2f5, fontFamily: _fontFamily);
  static const IconData browser = IconData(0xf2f6, fontFamily: _fontFamily);
  static const IconData browsers = IconData(0xf2f7, fontFamily: _fontFamily);
  static const IconData bug = IconData(0xf2f8, fontFamily: _fontFamily);
  static const IconData bug_beetle = IconData(0xf2f9, fontFamily: _fontFamily);
  static const IconData bug_droid = IconData(0xf2fa, fontFamily: _fontFamily);
  static const IconData buildings = IconData(0xf2fb, fontFamily: _fontFamily);
  static const IconData bus = IconData(0xf2fc, fontFamily: _fontFamily);
  static const IconData butterfly = IconData(0xf2fd, fontFamily: _fontFamily);
  static const IconData cactus = IconData(0xf2fe, fontFamily: _fontFamily);
  static const IconData cake = IconData(0xf2ff, fontFamily: _fontFamily);
  static const IconData calculator = IconData(0xf300, fontFamily: _fontFamily);
  static const IconData calendar = IconData(0xf301, fontFamily: _fontFamily);
  static const IconData calendar_blank = IconData(0xf302, fontFamily: _fontFamily);
  static const IconData calendar_check = IconData(0xf303, fontFamily: _fontFamily);
  static const IconData calendar_plus = IconData(0xf304, fontFamily: _fontFamily);
  static const IconData calendar_x = IconData(0xf305, fontFamily: _fontFamily);
  static const IconData camera = IconData(0xf306, fontFamily: _fontFamily);
  static const IconData camera_rotate = IconData(0xf307, fontFamily: _fontFamily);
  static const IconData camera_slash = IconData(0xf308, fontFamily: _fontFamily);
  static const IconData campfire = IconData(0xf309, fontFamily: _fontFamily);
  static const IconData car = IconData(0xf30a, fontFamily: _fontFamily);
  static const IconData car_simple = IconData(0xf30b, fontFamily: _fontFamily);
  static const IconData cardholder = IconData(0xf30c, fontFamily: _fontFamily);
  static const IconData cards = IconData(0xf30d, fontFamily: _fontFamily);
  static const IconData caret_circle_double_down = IconData(0xf30e, fontFamily: _fontFamily);
  static const IconData caret_circle_double_left = IconData(0xf30f, fontFamily: _fontFamily);
  static const IconData caret_circle_double_right = IconData(0xf310, fontFamily: _fontFamily);
  static const IconData caret_circle_double_up = IconData(0xf311, fontFamily: _fontFamily);
  static const IconData caret_circle_down = IconData(0xf312, fontFamily: _fontFamily);
  static const IconData caret_circle_left = IconData(0xf313, fontFamily: _fontFamily);
  static const IconData caret_circle_right = IconData(0xf314, fontFamily: _fontFamily);
  static const IconData caret_circle_up = IconData(0xf315, fontFamily: _fontFamily);
  static const IconData caret_double_down = IconData(0xf316, fontFamily: _fontFamily);
  static const IconData caret_double_left = IconData(0xf317, fontFamily: _fontFamily);
  static const IconData caret_double_right = IconData(0xf318, fontFamily: _fontFamily);
  static const IconData caret_double_up = IconData(0xf319, fontFamily: _fontFamily);
  static const IconData caret_down = IconData(0xf31a, fontFamily: _fontFamily);
  static const IconData caret_left = IconData(0xf31b, fontFamily: _fontFamily);
  static const IconData caret_right = IconData(0xf31c, fontFamily: _fontFamily);
  static const IconData caret_up = IconData(0xf31d, fontFamily: _fontFamily);
  static const IconData cat = IconData(0xf31e, fontFamily: _fontFamily);
  static const IconData cell_signal_full = IconData(0xf31f, fontFamily: _fontFamily);
  static const IconData cell_signal_high = IconData(0xf320, fontFamily: _fontFamily);
  static const IconData cell_signal_low = IconData(0xf321, fontFamily: _fontFamily);
  static const IconData cell_signal_medium = IconData(0xf322, fontFamily: _fontFamily);
  static const IconData cell_signal_none = IconData(0xf323, fontFamily: _fontFamily);
  static const IconData cell_signal_slash = IconData(0xf324, fontFamily: _fontFamily);
  static const IconData cell_signal_x = IconData(0xf325, fontFamily: _fontFamily);
  static const IconData chalkboard = IconData(0xf326, fontFamily: _fontFamily);
  static const IconData chalkboard_simple = IconData(0xf327, fontFamily: _fontFamily);
  static const IconData chalkboard_teacher = IconData(0xf328, fontFamily: _fontFamily);
  static const IconData chart_bar = IconData(0xf329, fontFamily: _fontFamily);
  static const IconData chart_bar_horizontal = IconData(0xf32a, fontFamily: _fontFamily);
  static const IconData chart_line = IconData(0xf32b, fontFamily: _fontFamily);
  static const IconData chart_line_up = IconData(0xf32c, fontFamily: _fontFamily);
  static const IconData chart_pie = IconData(0xf32d, fontFamily: _fontFamily);
  static const IconData chart_pie_slice = IconData(0xf32e, fontFamily: _fontFamily);
  static const IconData chat = IconData(0xf32f, fontFamily: _fontFamily);
  static const IconData chat_centered = IconData(0xf330, fontFamily: _fontFamily);
  static const IconData chat_centered_dots = IconData(0xf331, fontFamily: _fontFamily);
  static const IconData chat_centered_text = IconData(0xf332, fontFamily: _fontFamily);
  static const IconData chat_circle = IconData(0xf333, fontFamily: _fontFamily);
  static const IconData chat_circle_dots = IconData(0xf334, fontFamily: _fontFamily);
  static const IconData chat_circle_text = IconData(0xf335, fontFamily: _fontFamily);
  static const IconData chat_dots = IconData(0xf336, fontFamily: _fontFamily);
  static const IconData chat_teardrop = IconData(0xf337, fontFamily: _fontFamily);
  static const IconData chat_teardrop_dots = IconData(0xf338, fontFamily: _fontFamily);
  static const IconData chat_teardrop_text = IconData(0xf339, fontFamily: _fontFamily);
  static const IconData chat_text = IconData(0xf33a, fontFamily: _fontFamily);
  static const IconData chats = IconData(0xf33b, fontFamily: _fontFamily);
  static const IconData chats_circle = IconData(0xf33c, fontFamily: _fontFamily);
  static const IconData chats_teardrop = IconData(0xf33d, fontFamily: _fontFamily);
  static const IconData check = IconData(0xf33e, fontFamily: _fontFamily);
  static const IconData check_circle = IconData(0xf33f, fontFamily: _fontFamily);
  static const IconData check_square = IconData(0xf340, fontFamily: _fontFamily);
  static const IconData check_square_offset = IconData(0xf341, fontFamily: _fontFamily);
  static const IconData checks = IconData(0xf342, fontFamily: _fontFamily);
  static const IconData circle = IconData(0xf343, fontFamily: _fontFamily);
  static const IconData circle_dashed = IconData(0xf344, fontFamily: _fontFamily);
  static const IconData circle_half = IconData(0xf345, fontFamily: _fontFamily);
  static const IconData circle_half_tilt = IconData(0xf346, fontFamily: _fontFamily);
  static const IconData circle_notch = IconData(0xf347, fontFamily: _fontFamily);
  static const IconData circle_wavy = IconData(0xf348, fontFamily: _fontFamily);
  static const IconData circle_wavy_check = IconData(0xf349, fontFamily: _fontFamily);
  static const IconData circle_wavy_question = IconData(0xf34a, fontFamily: _fontFamily);
  static const IconData circle_wavy_warning = IconData(0xf34b, fontFamily: _fontFamily);
  static const IconData circles_four = IconData(0xf34c, fontFamily: _fontFamily);
  static const IconData circles_three = IconData(0xf34d, fontFamily: _fontFamily);
  static const IconData circles_three_plus = IconData(0xf34e, fontFamily: _fontFamily);
  static const IconData clipboard = IconData(0xf34f, fontFamily: _fontFamily);
  static const IconData clipboard_text = IconData(0xf350, fontFamily: _fontFamily);
  static const IconData clock = IconData(0xf351, fontFamily: _fontFamily);
  static const IconData clock_afternoon = IconData(0xf352, fontFamily: _fontFamily);
  static const IconData clock_clockwise = IconData(0xf353, fontFamily: _fontFamily);
  static const IconData clock_counter_clockwise = IconData(0xf354, fontFamily: _fontFamily);
  static const IconData closed_captioning = IconData(0xf355, fontFamily: _fontFamily);
  static const IconData cloud = IconData(0xf356, fontFamily: _fontFamily);
  static const IconData cloud_arrow_down = IconData(0xf357, fontFamily: _fontFamily);
  static const IconData cloud_arrow_up = IconData(0xf358, fontFamily: _fontFamily);
  static const IconData cloud_check = IconData(0xf359, fontFamily: _fontFamily);
  static const IconData cloud_fog = IconData(0xf35a, fontFamily: _fontFamily);
  static const IconData cloud_lightning = IconData(0xf35b, fontFamily: _fontFamily);
  static const IconData cloud_moon = IconData(0xf35c, fontFamily: _fontFamily);
  static const IconData cloud_rain = IconData(0xf35d, fontFamily: _fontFamily);
  static const IconData cloud_slash = IconData(0xf35e, fontFamily: _fontFamily);
  static const IconData cloud_snow = IconData(0xf35f, fontFamily: _fontFamily);
  static const IconData cloud_sun = IconData(0xf360, fontFamily: _fontFamily);
  static const IconData club = IconData(0xf361, fontFamily: _fontFamily);
  static const IconData coat_hanger = IconData(0xf362, fontFamily: _fontFamily);
  static const IconData code = IconData(0xf363, fontFamily: _fontFamily);
  static const IconData code_simple = IconData(0xf364, fontFamily: _fontFamily);
  static const IconData codepen_logo = IconData(0xf365, fontFamily: _fontFamily);
  static const IconData codesandbox_logo = IconData(0xf366, fontFamily: _fontFamily);
  static const IconData coffee = IconData(0xf367, fontFamily: _fontFamily);
  static const IconData coin = IconData(0xf368, fontFamily: _fontFamily);
  static const IconData coin_vertical = IconData(0xf369, fontFamily: _fontFamily);
  static const IconData coins = IconData(0xf36a, fontFamily: _fontFamily);
  static const IconData columns = IconData(0xf36b, fontFamily: _fontFamily);
  static const IconData command = IconData(0xf36c, fontFamily: _fontFamily);
  static const IconData compass = IconData(0xf36d, fontFamily: _fontFamily);
  static const IconData computer_tower = IconData(0xf36e, fontFamily: _fontFamily);
  static const IconData confetti = IconData(0xf36f, fontFamily: _fontFamily);
  static const IconData cookie = IconData(0xf370, fontFamily: _fontFamily);
  static const IconData cooking_pot = IconData(0xf371, fontFamily: _fontFamily);
  static const IconData copy = IconData(0xf372, fontFamily: _fontFamily);
  static const IconData copy_simple = IconData(0xf373, fontFamily: _fontFamily);
  static const IconData copyleft = IconData(0xf374, fontFamily: _fontFamily);
  static const IconData copyright = IconData(0xf375, fontFamily: _fontFamily);
  static const IconData corners_in = IconData(0xf376, fontFamily: _fontFamily);
  static const IconData corners_out = IconData(0xf377, fontFamily: _fontFamily);
  static const IconData cpu = IconData(0xf378, fontFamily: _fontFamily);
  static const IconData credit_card = IconData(0xf379, fontFamily: _fontFamily);
  static const IconData crop = IconData(0xf37a, fontFamily: _fontFamily);
  static const IconData crosshair = IconData(0xf37b, fontFamily: _fontFamily);
  static const IconData crosshair_simple = IconData(0xf37c, fontFamily: _fontFamily);
  static const IconData crown = IconData(0xf37d, fontFamily: _fontFamily);
  static const IconData crown_simple = IconData(0xf37e, fontFamily: _fontFamily);
  static const IconData cube = IconData(0xf37f, fontFamily: _fontFamily);
  static const IconData currency_btc = IconData(0xf380, fontFamily: _fontFamily);
  static const IconData currency_circle_dollar = IconData(0xf381, fontFamily: _fontFamily);
  static const IconData currency_cny = IconData(0xf382, fontFamily: _fontFamily);
  static const IconData currency_dollar = IconData(0xf383, fontFamily: _fontFamily);
  static const IconData currency_dollar_simple = IconData(0xf384, fontFamily: _fontFamily);
  static const IconData currency_eth = IconData(0xf385, fontFamily: _fontFamily);
  static const IconData currency_eur = IconData(0xf386, fontFamily: _fontFamily);
  static const IconData currency_gbp = IconData(0xf387, fontFamily: _fontFamily);
  static const IconData currency_inr = IconData(0xf388, fontFamily: _fontFamily);
  static const IconData currency_jpy = IconData(0xf389, fontFamily: _fontFamily);
  static const IconData currency_krw = IconData(0xf38a, fontFamily: _fontFamily);
  static const IconData currency_kzt = IconData(0xf38b, fontFamily: _fontFamily);
  static const IconData currency_ngn = IconData(0xf38c, fontFamily: _fontFamily);
  static const IconData currency_rub = IconData(0xf38d, fontFamily: _fontFamily);
  static const IconData cursor = IconData(0xf38e, fontFamily: _fontFamily);
  static const IconData cursor_text = IconData(0xf38f, fontFamily: _fontFamily);
  static const IconData cylinder = IconData(0xf390, fontFamily: _fontFamily);
  static const IconData database = IconData(0xf391, fontFamily: _fontFamily);
  static const IconData desktop = IconData(0xf392, fontFamily: _fontFamily);
  static const IconData desktop_tower = IconData(0xf393, fontFamily: _fontFamily);
  static const IconData detective = IconData(0xf394, fontFamily: _fontFamily);
  static const IconData device_mobile = IconData(0xf395, fontFamily: _fontFamily);
  static const IconData device_mobile_camera = IconData(0xf396, fontFamily: _fontFamily);
  static const IconData device_mobile_speaker = IconData(0xf397, fontFamily: _fontFamily);
  static const IconData device_tablet = IconData(0xf398, fontFamily: _fontFamily);
  static const IconData device_tablet_camera = IconData(0xf399, fontFamily: _fontFamily);
  static const IconData device_tablet_speaker = IconData(0xf39a, fontFamily: _fontFamily);
  static const IconData diamond = IconData(0xf39b, fontFamily: _fontFamily);
  static const IconData diamonds_four = IconData(0xf39c, fontFamily: _fontFamily);
  static const IconData dice_five = IconData(0xf39d, fontFamily: _fontFamily);
  static const IconData dice_four = IconData(0xf39e, fontFamily: _fontFamily);
  static const IconData dice_one = IconData(0xf39f, fontFamily: _fontFamily);
  static const IconData dice_six = IconData(0xf3a0, fontFamily: _fontFamily);
  static const IconData dice_three = IconData(0xf3a1, fontFamily: _fontFamily);
  static const IconData dice_two = IconData(0xf3a2, fontFamily: _fontFamily);
  static const IconData disc = IconData(0xf3a3, fontFamily: _fontFamily);
  static const IconData discord_logo = IconData(0xf3a4, fontFamily: _fontFamily);
  static const IconData divide = IconData(0xf3a5, fontFamily: _fontFamily);
  static const IconData dog = IconData(0xf3a6, fontFamily: _fontFamily);
  static const IconData door = IconData(0xf3a7, fontFamily: _fontFamily);
  static const IconData dots_nine = IconData(0xf3a8, fontFamily: _fontFamily);
  static const IconData dots_six = IconData(0xf3a9, fontFamily: _fontFamily);
  static const IconData dots_six_vertical = IconData(0xf3aa, fontFamily: _fontFamily);
  static const IconData dots_three = IconData(0xf3ab, fontFamily: _fontFamily);
  static const IconData dots_three_circle = IconData(0xf3ac, fontFamily: _fontFamily);
  static const IconData dots_three_circle_vertical = IconData(0xf3ad, fontFamily: _fontFamily);
  static const IconData dots_three_outline = IconData(0xf3ae, fontFamily: _fontFamily);
  static const IconData dots_three_outline_vertical = IconData(0xf3af, fontFamily: _fontFamily);
  static const IconData dots_three_vertical = IconData(0xf3b0, fontFamily: _fontFamily);
  static const IconData download = IconData(0xf3b1, fontFamily: _fontFamily);
  static const IconData download_simple = IconData(0xf3b2, fontFamily: _fontFamily);
  static const IconData dribbble_logo = IconData(0xf3b3, fontFamily: _fontFamily);
  static const IconData drop = IconData(0xf3b4, fontFamily: _fontFamily);
  static const IconData drop_half = IconData(0xf3b5, fontFamily: _fontFamily);
  static const IconData drop_half_bottom = IconData(0xf3b6, fontFamily: _fontFamily);
  static const IconData ear = IconData(0xf3b7, fontFamily: _fontFamily);
  static const IconData ear_slash = IconData(0xf3b8, fontFamily: _fontFamily);
  static const IconData egg = IconData(0xf3b9, fontFamily: _fontFamily);
  static const IconData egg_crack = IconData(0xf3ba, fontFamily: _fontFamily);
  static const IconData eject = IconData(0xf3bb, fontFamily: _fontFamily);
  static const IconData eject_simple = IconData(0xf3bc, fontFamily: _fontFamily);
  static const IconData envelope = IconData(0xf3bd, fontFamily: _fontFamily);
  static const IconData envelope_open = IconData(0xf3be, fontFamily: _fontFamily);
  static const IconData envelope_simple = IconData(0xf3bf, fontFamily: _fontFamily);
  static const IconData envelope_simple_open = IconData(0xf3c0, fontFamily: _fontFamily);
  static const IconData equalizer = IconData(0xf3c1, fontFamily: _fontFamily);
  static const IconData equals = IconData(0xf3c2, fontFamily: _fontFamily);
  static const IconData eraser = IconData(0xf3c3, fontFamily: _fontFamily);
  static const IconData exam = IconData(0xf3c4, fontFamily: _fontFamily);
  static const IconData export = IconData(0xf3c5, fontFamily: _fontFamily);
  static const IconData eye = IconData(0xf3c6, fontFamily: _fontFamily);
  static const IconData eye_closed = IconData(0xf3c7, fontFamily: _fontFamily);
  static const IconData eye_slash = IconData(0xf3c8, fontFamily: _fontFamily);
  static const IconData eyedropper = IconData(0xf3c9, fontFamily: _fontFamily);
  static const IconData eyedropper_sample = IconData(0xf3ca, fontFamily: _fontFamily);
  static const IconData eyeglasses = IconData(0xf3cb, fontFamily: _fontFamily);
  static const IconData face_mask = IconData(0xf3cc, fontFamily: _fontFamily);
  static const IconData facebook_logo = IconData(0xf3cd, fontFamily: _fontFamily);
  static const IconData factory = IconData(0xf3ce, fontFamily: _fontFamily);
  static const IconData faders = IconData(0xf3cf, fontFamily: _fontFamily);
  static const IconData faders_horizontal = IconData(0xf3d0, fontFamily: _fontFamily);
  static const IconData fast_forward = IconData(0xf3d1, fontFamily: _fontFamily);
  static const IconData fast_forward_circle = IconData(0xf3d2, fontFamily: _fontFamily);
  static const IconData figma_logo = IconData(0xf3d3, fontFamily: _fontFamily);
  static const IconData file = IconData(0xf3d4, fontFamily: _fontFamily);
  static const IconData file_arrow_down = IconData(0xf3d5, fontFamily: _fontFamily);
  static const IconData file_arrow_up = IconData(0xf3d6, fontFamily: _fontFamily);
  static const IconData file_audio = IconData(0xf3d7, fontFamily: _fontFamily);
  static const IconData file_cloud = IconData(0xf3d8, fontFamily: _fontFamily);
  static const IconData file_code = IconData(0xf3d9, fontFamily: _fontFamily);
  static const IconData file_css = IconData(0xf3da, fontFamily: _fontFamily);
  static const IconData file_csv = IconData(0xf3db, fontFamily: _fontFamily);
  static const IconData file_doc = IconData(0xf3dc, fontFamily: _fontFamily);
  static const IconData file_dotted = IconData(0xf3dd, fontFamily: _fontFamily);
  static const IconData file_html = IconData(0xf3de, fontFamily: _fontFamily);
  static const IconData file_image = IconData(0xf3df, fontFamily: _fontFamily);
  static const IconData file_jpg = IconData(0xf3e0, fontFamily: _fontFamily);
  static const IconData file_js = IconData(0xf3e1, fontFamily: _fontFamily);
  static const IconData file_jsx = IconData(0xf3e2, fontFamily: _fontFamily);
  static const IconData file_lock = IconData(0xf3e3, fontFamily: _fontFamily);
  static const IconData file_minus = IconData(0xf3e4, fontFamily: _fontFamily);
  static const IconData file_pdf = IconData(0xf3e5, fontFamily: _fontFamily);
  static const IconData file_plus = IconData(0xf3e6, fontFamily: _fontFamily);
  static const IconData file_png = IconData(0xf3e7, fontFamily: _fontFamily);
  static const IconData file_ppt = IconData(0xf3e8, fontFamily: _fontFamily);
  static const IconData file_rs = IconData(0xf3e9, fontFamily: _fontFamily);
  static const IconData file_search = IconData(0xf3ea, fontFamily: _fontFamily);
  static const IconData file_text = IconData(0xf3eb, fontFamily: _fontFamily);
  static const IconData file_ts = IconData(0xf3ec, fontFamily: _fontFamily);
  static const IconData file_tsx = IconData(0xf3ed, fontFamily: _fontFamily);
  static const IconData file_video = IconData(0xf3ee, fontFamily: _fontFamily);
  static const IconData file_vue = IconData(0xf3ef, fontFamily: _fontFamily);
  static const IconData file_x = IconData(0xf3f0, fontFamily: _fontFamily);
  static const IconData file_xls = IconData(0xf3f1, fontFamily: _fontFamily);
  static const IconData file_zip = IconData(0xf3f2, fontFamily: _fontFamily);
  static const IconData files = IconData(0xf3f3, fontFamily: _fontFamily);
  static const IconData film_script = IconData(0xf3f4, fontFamily: _fontFamily);
  static const IconData film_slate = IconData(0xf3f5, fontFamily: _fontFamily);
  static const IconData film_strip = IconData(0xf3f6, fontFamily: _fontFamily);
  static const IconData fingerprint = IconData(0xf3f7, fontFamily: _fontFamily);
  static const IconData fingerprint_simple = IconData(0xf3f8, fontFamily: _fontFamily);
  static const IconData finn_the_human = IconData(0xf3f9, fontFamily: _fontFamily);
  static const IconData fire = IconData(0xf3fa, fontFamily: _fontFamily);
  static const IconData fire_simple = IconData(0xf3fb, fontFamily: _fontFamily);
  static const IconData first_aid = IconData(0xf3fc, fontFamily: _fontFamily);
  static const IconData first_aid_kit = IconData(0xf3fd, fontFamily: _fontFamily);
  static const IconData fish = IconData(0xf3fe, fontFamily: _fontFamily);
  static const IconData fish_simple = IconData(0xf3ff, fontFamily: _fontFamily);
  static const IconData flag = IconData(0xf400, fontFamily: _fontFamily);
  static const IconData flag_banner = IconData(0xf401, fontFamily: _fontFamily);
  static const IconData flag_checkered = IconData(0xf402, fontFamily: _fontFamily);
  static const IconData flame = IconData(0xf403, fontFamily: _fontFamily);
  static const IconData flashlight = IconData(0xf404, fontFamily: _fontFamily);
  static const IconData flask = IconData(0xf405, fontFamily: _fontFamily);
  static const IconData floppy_disk = IconData(0xf406, fontFamily: _fontFamily);
  static const IconData floppy_disk_back = IconData(0xf407, fontFamily: _fontFamily);
  static const IconData flow_arrow = IconData(0xf408, fontFamily: _fontFamily);
  static const IconData flower = IconData(0xf409, fontFamily: _fontFamily);
  static const IconData flower_lotus = IconData(0xf40a, fontFamily: _fontFamily);
  static const IconData flying_saucer = IconData(0xf40b, fontFamily: _fontFamily);
  static const IconData folder = IconData(0xf40c, fontFamily: _fontFamily);
  static const IconData folder_dotted = IconData(0xf40d, fontFamily: _fontFamily);
  static const IconData folder_lock = IconData(0xf40e, fontFamily: _fontFamily);
  static const IconData folder_minus = IconData(0xf40f, fontFamily: _fontFamily);
  static const IconData folder_notch = IconData(0xf410, fontFamily: _fontFamily);
  static const IconData folder_notch_minus = IconData(0xf411, fontFamily: _fontFamily);
  static const IconData folder_notch_open = IconData(0xf412, fontFamily: _fontFamily);
  static const IconData folder_notch_plus = IconData(0xf413, fontFamily: _fontFamily);
  static const IconData folder_open = IconData(0xf414, fontFamily: _fontFamily);
  static const IconData folder_plus = IconData(0xf415, fontFamily: _fontFamily);
  static const IconData folder_simple = IconData(0xf416, fontFamily: _fontFamily);
  static const IconData folder_simple_dotted = IconData(0xf417, fontFamily: _fontFamily);
  static const IconData folder_simple_lock = IconData(0xf418, fontFamily: _fontFamily);
  static const IconData folder_simple_minus = IconData(0xf419, fontFamily: _fontFamily);
  static const IconData folder_simple_plus = IconData(0xf41a, fontFamily: _fontFamily);
  static const IconData folder_simple_star = IconData(0xf41b, fontFamily: _fontFamily);
  static const IconData folder_simple_user = IconData(0xf41c, fontFamily: _fontFamily);
  static const IconData folder_star = IconData(0xf41d, fontFamily: _fontFamily);
  static const IconData folder_user = IconData(0xf41e, fontFamily: _fontFamily);
  static const IconData folders = IconData(0xf41f, fontFamily: _fontFamily);
  static const IconData football = IconData(0xf420, fontFamily: _fontFamily);
  static const IconData fork_knife = IconData(0xf421, fontFamily: _fontFamily);
  static const IconData frame_corners = IconData(0xf422, fontFamily: _fontFamily);
  static const IconData framer_logo = IconData(0xf423, fontFamily: _fontFamily);
  static const IconData function = IconData(0xf424, fontFamily: _fontFamily);
  static const IconData funnel = IconData(0xf425, fontFamily: _fontFamily);
  static const IconData funnel_simple = IconData(0xf426, fontFamily: _fontFamily);
  static const IconData game_controller = IconData(0xf427, fontFamily: _fontFamily);
  static const IconData gas_pump = IconData(0xf428, fontFamily: _fontFamily);
  static const IconData gauge = IconData(0xf429, fontFamily: _fontFamily);
  static const IconData gear = IconData(0xf42a, fontFamily: _fontFamily);
  static const IconData gear_six = IconData(0xf42b, fontFamily: _fontFamily);
  static const IconData gender_female = IconData(0xf42c, fontFamily: _fontFamily);
  static const IconData gender_intersex = IconData(0xf42d, fontFamily: _fontFamily);
  static const IconData gender_male = IconData(0xf42e, fontFamily: _fontFamily);
  static const IconData gender_neuter = IconData(0xf42f, fontFamily: _fontFamily);
  static const IconData gender_nonbinary = IconData(0xf430, fontFamily: _fontFamily);
  static const IconData gender_transgender = IconData(0xf431, fontFamily: _fontFamily);
  static const IconData ghost = IconData(0xf432, fontFamily: _fontFamily);
  static const IconData gif = IconData(0xf433, fontFamily: _fontFamily);
  static const IconData gift = IconData(0xf434, fontFamily: _fontFamily);
  static const IconData git_branch = IconData(0xf435, fontFamily: _fontFamily);
  static const IconData git_commit = IconData(0xf436, fontFamily: _fontFamily);
  static const IconData git_diff = IconData(0xf437, fontFamily: _fontFamily);
  static const IconData git_fork = IconData(0xf438, fontFamily: _fontFamily);
  static const IconData git_merge = IconData(0xf439, fontFamily: _fontFamily);
  static const IconData git_pull_request = IconData(0xf43a, fontFamily: _fontFamily);
  static const IconData github_logo = IconData(0xf43b, fontFamily: _fontFamily);
  static const IconData gitlab_logo = IconData(0xf43c, fontFamily: _fontFamily);
  static const IconData gitlab_logo_simple = IconData(0xf43d, fontFamily: _fontFamily);
  static const IconData globe = IconData(0xf43e, fontFamily: _fontFamily);
  static const IconData globe_hemisphere_east = IconData(0xf43f, fontFamily: _fontFamily);
  static const IconData globe_hemisphere_west = IconData(0xf440, fontFamily: _fontFamily);
  static const IconData globe_simple = IconData(0xf441, fontFamily: _fontFamily);
  static const IconData globe_stand = IconData(0xf442, fontFamily: _fontFamily);
  static const IconData google_chrome_logo = IconData(0xf443, fontFamily: _fontFamily);
  static const IconData google_logo = IconData(0xf444, fontFamily: _fontFamily);
  static const IconData google_photos_logo = IconData(0xf445, fontFamily: _fontFamily);
  static const IconData google_play_logo = IconData(0xf446, fontFamily: _fontFamily);
  static const IconData google_podcasts_logo = IconData(0xf447, fontFamily: _fontFamily);
  static const IconData gradient = IconData(0xf448, fontFamily: _fontFamily);
  static const IconData graduation_cap = IconData(0xf449, fontFamily: _fontFamily);
  static const IconData graph = IconData(0xf44a, fontFamily: _fontFamily);
  static const IconData grid_four = IconData(0xf44b, fontFamily: _fontFamily);
  static const IconData hamburger = IconData(0xf44c, fontFamily: _fontFamily);
  static const IconData hand = IconData(0xf44d, fontFamily: _fontFamily);
  static const IconData hand_eye = IconData(0xf44e, fontFamily: _fontFamily);
  static const IconData hand_fist = IconData(0xf44f, fontFamily: _fontFamily);
  static const IconData hand_grabbing = IconData(0xf450, fontFamily: _fontFamily);
  static const IconData hand_palm = IconData(0xf451, fontFamily: _fontFamily);
  static const IconData hand_pointing = IconData(0xf452, fontFamily: _fontFamily);
  static const IconData hand_soap = IconData(0xf453, fontFamily: _fontFamily);
  static const IconData hand_waving = IconData(0xf454, fontFamily: _fontFamily);
  static const IconData handbag = IconData(0xf455, fontFamily: _fontFamily);
  static const IconData handbag_simple = IconData(0xf456, fontFamily: _fontFamily);
  static const IconData hands_clapping = IconData(0xf457, fontFamily: _fontFamily);
  static const IconData handshake = IconData(0xf458, fontFamily: _fontFamily);
  static const IconData hard_drive = IconData(0xf459, fontFamily: _fontFamily);
  static const IconData hard_drives = IconData(0xf45a, fontFamily: _fontFamily);
  static const IconData hash = IconData(0xf45b, fontFamily: _fontFamily);
  static const IconData hash_straight = IconData(0xf45c, fontFamily: _fontFamily);
  static const IconData headlights = IconData(0xf45d, fontFamily: _fontFamily);
  static const IconData headphones = IconData(0xf45e, fontFamily: _fontFamily);
  static const IconData headset = IconData(0xf45f, fontFamily: _fontFamily);
  static const IconData heart = IconData(0xf460, fontFamily: _fontFamily);
  static const IconData heart_break = IconData(0xf461, fontFamily: _fontFamily);
  static const IconData heart_straight = IconData(0xf462, fontFamily: _fontFamily);
  static const IconData heart_straight_break = IconData(0xf463, fontFamily: _fontFamily);
  static const IconData heartbeat = IconData(0xf464, fontFamily: _fontFamily);
  static const IconData hexagon = IconData(0xf465, fontFamily: _fontFamily);
  static const IconData highlighter_circle = IconData(0xf466, fontFamily: _fontFamily);
  static const IconData horse = IconData(0xf467, fontFamily: _fontFamily);
  static const IconData hourglass = IconData(0xf468, fontFamily: _fontFamily);
  static const IconData hourglass_high = IconData(0xf469, fontFamily: _fontFamily);
  static const IconData hourglass_low = IconData(0xf46a, fontFamily: _fontFamily);
  static const IconData hourglass_medium = IconData(0xf46b, fontFamily: _fontFamily);
  static const IconData hourglass_simple = IconData(0xf46c, fontFamily: _fontFamily);
  static const IconData hourglass_simple_high = IconData(0xf46d, fontFamily: _fontFamily);
  static const IconData hourglass_simple_low = IconData(0xf46e, fontFamily: _fontFamily);
  static const IconData hourglass_simple_medium = IconData(0xf46f, fontFamily: _fontFamily);
  static const IconData house = IconData(0xf470, fontFamily: _fontFamily);
  static const IconData house_line = IconData(0xf471, fontFamily: _fontFamily);
  static const IconData house_simple = IconData(0xf472, fontFamily: _fontFamily);
  static const IconData identification_badge = IconData(0xf473, fontFamily: _fontFamily);
  static const IconData identification_card = IconData(0xf474, fontFamily: _fontFamily);
  static const IconData image = IconData(0xf475, fontFamily: _fontFamily);
  static const IconData image_square = IconData(0xf476, fontFamily: _fontFamily);
  static const IconData infinity = IconData(0xf477, fontFamily: _fontFamily);
  static const IconData info = IconData(0xf478, fontFamily: _fontFamily);
  static const IconData instagram_logo = IconData(0xf479, fontFamily: _fontFamily);
  static const IconData intersect = IconData(0xf47a, fontFamily: _fontFamily);
  static const IconData jeep = IconData(0xf47b, fontFamily: _fontFamily);
  static const IconData kanban = IconData(0xf47c, fontFamily: _fontFamily);
  static const IconData key = IconData(0xf47d, fontFamily: _fontFamily);
  static const IconData key_return = IconData(0xf47e, fontFamily: _fontFamily);
  static const IconData keyboard = IconData(0xf47f, fontFamily: _fontFamily);
  static const IconData keyhole = IconData(0xf480, fontFamily: _fontFamily);
  static const IconData knife = IconData(0xf481, fontFamily: _fontFamily);
  static const IconData ladder = IconData(0xf482, fontFamily: _fontFamily);
  static const IconData ladder_simple = IconData(0xf483, fontFamily: _fontFamily);
  static const IconData lamp = IconData(0xf484, fontFamily: _fontFamily);
  static const IconData laptop = IconData(0xf485, fontFamily: _fontFamily);
  static const IconData layout = IconData(0xf486, fontFamily: _fontFamily);
  static const IconData leaf = IconData(0xf487, fontFamily: _fontFamily);
  static const IconData lifebuoy = IconData(0xf488, fontFamily: _fontFamily);
  static const IconData lightbulb = IconData(0xf489, fontFamily: _fontFamily);
  static const IconData lightbulb_filament = IconData(0xf48a, fontFamily: _fontFamily);
  static const IconData lightning = IconData(0xf48b, fontFamily: _fontFamily);
  static const IconData lightning_slash = IconData(0xf48c, fontFamily: _fontFamily);
  static const IconData line_segment = IconData(0xf48d, fontFamily: _fontFamily);
  static const IconData line_segments = IconData(0xf48e, fontFamily: _fontFamily);
  static const IconData link = IconData(0xf48f, fontFamily: _fontFamily);
  static const IconData link_break = IconData(0xf490, fontFamily: _fontFamily);
  static const IconData link_simple = IconData(0xf491, fontFamily: _fontFamily);
  static const IconData link_simple_break = IconData(0xf492, fontFamily: _fontFamily);
  static const IconData link_simple_horizontal = IconData(0xf493, fontFamily: _fontFamily);
  static const IconData link_simple_horizontal_break = IconData(0xf494, fontFamily: _fontFamily);
  static const IconData linkedin_logo = IconData(0xf495, fontFamily: _fontFamily);
  static const IconData linux_logo = IconData(0xf496, fontFamily: _fontFamily);
  static const IconData list = IconData(0xf497, fontFamily: _fontFamily);
  static const IconData list_bullets = IconData(0xf498, fontFamily: _fontFamily);
  static const IconData list_checks = IconData(0xf499, fontFamily: _fontFamily);
  static const IconData list_dashes = IconData(0xf49a, fontFamily: _fontFamily);
  static const IconData list_numbers = IconData(0xf49b, fontFamily: _fontFamily);
  static const IconData list_plus = IconData(0xf49c, fontFamily: _fontFamily);
  static const IconData lock = IconData(0xf49d, fontFamily: _fontFamily);
  static const IconData lock_key = IconData(0xf49e, fontFamily: _fontFamily);
  static const IconData lock_key_open = IconData(0xf49f, fontFamily: _fontFamily);
  static const IconData lock_laminated = IconData(0xf4a0, fontFamily: _fontFamily);
  static const IconData lock_laminated_open = IconData(0xf4a1, fontFamily: _fontFamily);
  static const IconData lock_open = IconData(0xf4a2, fontFamily: _fontFamily);
  static const IconData lock_simple = IconData(0xf4a3, fontFamily: _fontFamily);
  static const IconData lock_simple_open = IconData(0xf4a4, fontFamily: _fontFamily);
  static const IconData magic_wand = IconData(0xf4a5, fontFamily: _fontFamily);
  static const IconData magnet = IconData(0xf4a6, fontFamily: _fontFamily);
  static const IconData magnet_straight = IconData(0xf4a7, fontFamily: _fontFamily);
  static const IconData magnifying_glass = IconData(0xf4a8, fontFamily: _fontFamily);
  static const IconData magnifying_glass_minus = IconData(0xf4a9, fontFamily: _fontFamily);
  static const IconData magnifying_glass_plus = IconData(0xf4aa, fontFamily: _fontFamily);
  static const IconData map_pin = IconData(0xf4ab, fontFamily: _fontFamily);
  static const IconData map_pin_line = IconData(0xf4ac, fontFamily: _fontFamily);
  static const IconData map_trifold = IconData(0xf4ad, fontFamily: _fontFamily);
  static const IconData marker_circle = IconData(0xf4ae, fontFamily: _fontFamily);
  static const IconData martini = IconData(0xf4af, fontFamily: _fontFamily);
  static const IconData mask_happy = IconData(0xf4b0, fontFamily: _fontFamily);
  static const IconData mask_sad = IconData(0xf4b1, fontFamily: _fontFamily);
  static const IconData math_operations = IconData(0xf4b2, fontFamily: _fontFamily);
  static const IconData medal = IconData(0xf4b3, fontFamily: _fontFamily);
  static const IconData medium_logo = IconData(0xf4b4, fontFamily: _fontFamily);
  static const IconData megaphone = IconData(0xf4b5, fontFamily: _fontFamily);
  static const IconData megaphone_simple = IconData(0xf4b6, fontFamily: _fontFamily);
  static const IconData messenger_logo = IconData(0xf4b7, fontFamily: _fontFamily);
  static const IconData microphone = IconData(0xf4b8, fontFamily: _fontFamily);
  static const IconData microphone_slash = IconData(0xf4b9, fontFamily: _fontFamily);
  static const IconData microphone_stage = IconData(0xf4ba, fontFamily: _fontFamily);
  static const IconData microsoft_excel_logo = IconData(0xf4bb, fontFamily: _fontFamily);
  static const IconData microsoft_powerpoint_logo = IconData(0xf4bc, fontFamily: _fontFamily);
  static const IconData microsoft_teams_logo = IconData(0xf4bd, fontFamily: _fontFamily);
  static const IconData microsoft_word_logo = IconData(0xf4be, fontFamily: _fontFamily);
  static const IconData minus = IconData(0xf4bf, fontFamily: _fontFamily);
  static const IconData minus_circle = IconData(0xf4c0, fontFamily: _fontFamily);
  static const IconData money = IconData(0xf4c1, fontFamily: _fontFamily);
  static const IconData monitor = IconData(0xf4c2, fontFamily: _fontFamily);
  static const IconData monitor_play = IconData(0xf4c3, fontFamily: _fontFamily);
  static const IconData moon = IconData(0xf4c4, fontFamily: _fontFamily);
  static const IconData moon_stars = IconData(0xf4c5, fontFamily: _fontFamily);
  static const IconData mountains = IconData(0xf4c6, fontFamily: _fontFamily);
  static const IconData mouse = IconData(0xf4c7, fontFamily: _fontFamily);
  static const IconData mouse_simple = IconData(0xf4c8, fontFamily: _fontFamily);
  static const IconData music_note = IconData(0xf4c9, fontFamily: _fontFamily);
  static const IconData music_note_simple = IconData(0xf4ca, fontFamily: _fontFamily);
  static const IconData music_notes = IconData(0xf4cb, fontFamily: _fontFamily);
  static const IconData music_notes_plus = IconData(0xf4cc, fontFamily: _fontFamily);
  static const IconData music_notes_simple = IconData(0xf4cd, fontFamily: _fontFamily);
  static const IconData navigation_arrow = IconData(0xf4ce, fontFamily: _fontFamily);
  static const IconData needle = IconData(0xf4cf, fontFamily: _fontFamily);
  static const IconData newspaper = IconData(0xf4d0, fontFamily: _fontFamily);
  static const IconData newspaper_clipping = IconData(0xf4d1, fontFamily: _fontFamily);
  static const IconData note = IconData(0xf4d2, fontFamily: _fontFamily);
  static const IconData note_blank = IconData(0xf4d3, fontFamily: _fontFamily);
  static const IconData note_pencil = IconData(0xf4d4, fontFamily: _fontFamily);
  static const IconData notebook = IconData(0xf4d5, fontFamily: _fontFamily);
  static const IconData notepad = IconData(0xf4d6, fontFamily: _fontFamily);
  static const IconData notification = IconData(0xf4d7, fontFamily: _fontFamily);
  static const IconData number_circle_eight = IconData(0xf4d8, fontFamily: _fontFamily);
  static const IconData number_circle_five = IconData(0xf4d9, fontFamily: _fontFamily);
  static const IconData number_circle_four = IconData(0xf4da, fontFamily: _fontFamily);
  static const IconData number_circle_nine = IconData(0xf4db, fontFamily: _fontFamily);
  static const IconData number_circle_one = IconData(0xf4dc, fontFamily: _fontFamily);
  static const IconData number_circle_seven = IconData(0xf4dd, fontFamily: _fontFamily);
  static const IconData number_circle_six = IconData(0xf4de, fontFamily: _fontFamily);
  static const IconData number_circle_three = IconData(0xf4df, fontFamily: _fontFamily);
  static const IconData number_circle_two = IconData(0xf4e0, fontFamily: _fontFamily);
  static const IconData number_circle_zero = IconData(0xf4e1, fontFamily: _fontFamily);
  static const IconData number_eight = IconData(0xf4e2, fontFamily: _fontFamily);
  static const IconData number_five = IconData(0xf4e3, fontFamily: _fontFamily);
  static const IconData number_four = IconData(0xf4e4, fontFamily: _fontFamily);
  static const IconData number_nine = IconData(0xf4e5, fontFamily: _fontFamily);
  static const IconData number_one = IconData(0xf4e6, fontFamily: _fontFamily);
  static const IconData number_seven = IconData(0xf4e7, fontFamily: _fontFamily);
  static const IconData number_six = IconData(0xf4e8, fontFamily: _fontFamily);
  static const IconData number_square_eight = IconData(0xf4e9, fontFamily: _fontFamily);
  static const IconData number_square_five = IconData(0xf4ea, fontFamily: _fontFamily);
  static const IconData number_square_four = IconData(0xf4eb, fontFamily: _fontFamily);
  static const IconData number_square_nine = IconData(0xf4ec, fontFamily: _fontFamily);
  static const IconData number_square_one = IconData(0xf4ed, fontFamily: _fontFamily);
  static const IconData number_square_seven = IconData(0xf4ee, fontFamily: _fontFamily);
  static const IconData number_square_six = IconData(0xf4ef, fontFamily: _fontFamily);
  static const IconData number_square_three = IconData(0xf4f0, fontFamily: _fontFamily);
  static const IconData number_square_two = IconData(0xf4f1, fontFamily: _fontFamily);
  static const IconData number_square_zero = IconData(0xf4f2, fontFamily: _fontFamily);
  static const IconData number_three = IconData(0xf4f3, fontFamily: _fontFamily);
  static const IconData number_two = IconData(0xf4f4, fontFamily: _fontFamily);
  static const IconData number_zero = IconData(0xf4f5, fontFamily: _fontFamily);
  static const IconData nut = IconData(0xf4f6, fontFamily: _fontFamily);
  static const IconData ny_times_logo = IconData(0xf4f7, fontFamily: _fontFamily);
  static const IconData octagon = IconData(0xf4f8, fontFamily: _fontFamily);
  static const IconData option = IconData(0xf4f9, fontFamily: _fontFamily);
  static const IconData package = IconData(0xf4fa, fontFamily: _fontFamily);
  static const IconData paint_brush = IconData(0xf4fb, fontFamily: _fontFamily);
  static const IconData paint_brush_broad = IconData(0xf4fc, fontFamily: _fontFamily);
  static const IconData paint_brush_household = IconData(0xf4fd, fontFamily: _fontFamily);
  static const IconData paint_bucket = IconData(0xf4fe, fontFamily: _fontFamily);
  static const IconData paint_roller = IconData(0xf4ff, fontFamily: _fontFamily);
  static const IconData palette = IconData(0xf500, fontFamily: _fontFamily);
  static const IconData paper_plane = IconData(0xf501, fontFamily: _fontFamily);
  static const IconData paper_plane_right = IconData(0xf502, fontFamily: _fontFamily);
  static const IconData paper_plane_tilt = IconData(0xf503, fontFamily: _fontFamily);
  static const IconData paperclip = IconData(0xf504, fontFamily: _fontFamily);
  static const IconData paperclip_horizontal = IconData(0xf505, fontFamily: _fontFamily);
  static const IconData parachute = IconData(0xf506, fontFamily: _fontFamily);
  static const IconData password = IconData(0xf507, fontFamily: _fontFamily);
  static const IconData path = IconData(0xf508, fontFamily: _fontFamily);
  static const IconData pause = IconData(0xf509, fontFamily: _fontFamily);
  static const IconData pause_circle = IconData(0xf50a, fontFamily: _fontFamily);
  static const IconData paw_print = IconData(0xf50b, fontFamily: _fontFamily);
  static const IconData peace = IconData(0xf50c, fontFamily: _fontFamily);
  static const IconData pen = IconData(0xf50d, fontFamily: _fontFamily);
  static const IconData pen_nib = IconData(0xf50e, fontFamily: _fontFamily);
  static const IconData pen_nib_straight = IconData(0xf50f, fontFamily: _fontFamily);
  static const IconData pencil = IconData(0xf510, fontFamily: _fontFamily);
  static const IconData pencil_circle = IconData(0xf511, fontFamily: _fontFamily);
  static const IconData pencil_line = IconData(0xf512, fontFamily: _fontFamily);
  static const IconData pencil_simple = IconData(0xf513, fontFamily: _fontFamily);
  static const IconData pencil_simple_line = IconData(0xf514, fontFamily: _fontFamily);
  static const IconData percent = IconData(0xf515, fontFamily: _fontFamily);
  static const IconData person = IconData(0xf516, fontFamily: _fontFamily);
  static const IconData person_simple = IconData(0xf517, fontFamily: _fontFamily);
  static const IconData person_simple_run = IconData(0xf518, fontFamily: _fontFamily);
  static const IconData person_simple_walk = IconData(0xf519, fontFamily: _fontFamily);
  static const IconData perspective = IconData(0xf51a, fontFamily: _fontFamily);
  static const IconData phone = IconData(0xf51b, fontFamily: _fontFamily);
  static const IconData phone_call = IconData(0xf51c, fontFamily: _fontFamily);
  static const IconData phone_disconnect = IconData(0xf51d, fontFamily: _fontFamily);
  static const IconData phone_incoming = IconData(0xf51e, fontFamily: _fontFamily);
  static const IconData phone_outgoing = IconData(0xf51f, fontFamily: _fontFamily);
  static const IconData phone_slash = IconData(0xf520, fontFamily: _fontFamily);
  static const IconData phone_x = IconData(0xf521, fontFamily: _fontFamily);
  static const IconData phosphor_logo = IconData(0xf522, fontFamily: _fontFamily);
  static const IconData piano_keys = IconData(0xf523, fontFamily: _fontFamily);
  static const IconData picture_in_picture = IconData(0xf524, fontFamily: _fontFamily);
  static const IconData pill = IconData(0xf525, fontFamily: _fontFamily);
  static const IconData pinterest_logo = IconData(0xf526, fontFamily: _fontFamily);
  static const IconData pinwheel = IconData(0xf527, fontFamily: _fontFamily);
  static const IconData pizza = IconData(0xf528, fontFamily: _fontFamily);
  static const IconData placeholder = IconData(0xf529, fontFamily: _fontFamily);
  static const IconData planet = IconData(0xf52a, fontFamily: _fontFamily);
  static const IconData play = IconData(0xf52b, fontFamily: _fontFamily);
  static const IconData play_circle = IconData(0xf52c, fontFamily: _fontFamily);
  static const IconData playlist = IconData(0xf52d, fontFamily: _fontFamily);
  static const IconData plug = IconData(0xf52e, fontFamily: _fontFamily);
  static const IconData plugs = IconData(0xf52f, fontFamily: _fontFamily);
  static const IconData plugs_connected = IconData(0xf530, fontFamily: _fontFamily);
  static const IconData plus = IconData(0xf531, fontFamily: _fontFamily);
  static const IconData plus_circle = IconData(0xf532, fontFamily: _fontFamily);
  static const IconData plus_minus = IconData(0xf533, fontFamily: _fontFamily);
  static const IconData poker_chip = IconData(0xf534, fontFamily: _fontFamily);
  static const IconData police_car = IconData(0xf535, fontFamily: _fontFamily);
  static const IconData polygon = IconData(0xf536, fontFamily: _fontFamily);
  static const IconData popcorn = IconData(0xf537, fontFamily: _fontFamily);
  static const IconData power = IconData(0xf538, fontFamily: _fontFamily);
  static const IconData prescription = IconData(0xf539, fontFamily: _fontFamily);
  static const IconData presentation = IconData(0xf53a, fontFamily: _fontFamily);
  static const IconData presentation_chart = IconData(0xf53b, fontFamily: _fontFamily);
  static const IconData printer = IconData(0xf53c, fontFamily: _fontFamily);
  static const IconData prohibit = IconData(0xf53d, fontFamily: _fontFamily);
  static const IconData prohibit_inset = IconData(0xf53e, fontFamily: _fontFamily);
  static const IconData projector_screen = IconData(0xf53f, fontFamily: _fontFamily);
  static const IconData projector_screen_chart = IconData(0xf540, fontFamily: _fontFamily);
  static const IconData push_pin = IconData(0xf541, fontFamily: _fontFamily);
  static const IconData push_pin_simple = IconData(0xf542, fontFamily: _fontFamily);
  static const IconData push_pin_simple_slash = IconData(0xf543, fontFamily: _fontFamily);
  static const IconData push_pin_slash = IconData(0xf544, fontFamily: _fontFamily);
  static const IconData puzzle_piece = IconData(0xf545, fontFamily: _fontFamily);
  static const IconData qr_code = IconData(0xf546, fontFamily: _fontFamily);
  static const IconData question = IconData(0xf547, fontFamily: _fontFamily);
  static const IconData queue = IconData(0xf548, fontFamily: _fontFamily);
  static const IconData quotes = IconData(0xf549, fontFamily: _fontFamily);
  static const IconData radical = IconData(0xf54a, fontFamily: _fontFamily);
  static const IconData radio = IconData(0xf54b, fontFamily: _fontFamily);
  static const IconData radio_button = IconData(0xf54c, fontFamily: _fontFamily);
  static const IconData rainbow = IconData(0xf54d, fontFamily: _fontFamily);
  static const IconData rainbow_cloud = IconData(0xf54e, fontFamily: _fontFamily);
  static const IconData receipt = IconData(0xf54f, fontFamily: _fontFamily);
  static const IconData record = IconData(0xf550, fontFamily: _fontFamily);
  static const IconData rectangle = IconData(0xf551, fontFamily: _fontFamily);
  static const IconData recycle = IconData(0xf552, fontFamily: _fontFamily);
  static const IconData reddit_logo = IconData(0xf553, fontFamily: _fontFamily);
  static const IconData repeat = IconData(0xf554, fontFamily: _fontFamily);
  static const IconData repeat_once = IconData(0xf555, fontFamily: _fontFamily);
  static const IconData rewind = IconData(0xf556, fontFamily: _fontFamily);
  static const IconData rewind_circle = IconData(0xf557, fontFamily: _fontFamily);
  static const IconData robot = IconData(0xf558, fontFamily: _fontFamily);
  static const IconData rocket = IconData(0xf559, fontFamily: _fontFamily);
  static const IconData rocket_launch = IconData(0xf55a, fontFamily: _fontFamily);
  static const IconData rows = IconData(0xf55b, fontFamily: _fontFamily);
  static const IconData rss = IconData(0xf55c, fontFamily: _fontFamily);
  static const IconData rss_simple = IconData(0xf55d, fontFamily: _fontFamily);
  static const IconData rug = IconData(0xf55e, fontFamily: _fontFamily);
  static const IconData ruler = IconData(0xf55f, fontFamily: _fontFamily);
  static const IconData scales = IconData(0xf560, fontFamily: _fontFamily);
  static const IconData scan = IconData(0xf561, fontFamily: _fontFamily);
  static const IconData scissors = IconData(0xf562, fontFamily: _fontFamily);
  static const IconData screencast = IconData(0xf563, fontFamily: _fontFamily);
  static const IconData scribble_loop = IconData(0xf564, fontFamily: _fontFamily);
  static const IconData scroll = IconData(0xf565, fontFamily: _fontFamily);
  static const IconData selection = IconData(0xf566, fontFamily: _fontFamily);
  static const IconData selection_all = IconData(0xf567, fontFamily: _fontFamily);
  static const IconData selection_background = IconData(0xf568, fontFamily: _fontFamily);
  static const IconData selection_foreground = IconData(0xf569, fontFamily: _fontFamily);
  static const IconData selection_inverse = IconData(0xf56a, fontFamily: _fontFamily);
  static const IconData selection_plus = IconData(0xf56b, fontFamily: _fontFamily);
  static const IconData selection_slash = IconData(0xf56c, fontFamily: _fontFamily);
  static const IconData share = IconData(0xf56d, fontFamily: _fontFamily);
  static const IconData share_network = IconData(0xf56e, fontFamily: _fontFamily);
  static const IconData shield = IconData(0xf56f, fontFamily: _fontFamily);
  static const IconData shield_check = IconData(0xf570, fontFamily: _fontFamily);
  static const IconData shield_checkered = IconData(0xf571, fontFamily: _fontFamily);
  static const IconData shield_chevron = IconData(0xf572, fontFamily: _fontFamily);
  static const IconData shield_plus = IconData(0xf573, fontFamily: _fontFamily);
  static const IconData shield_slash = IconData(0xf574, fontFamily: _fontFamily);
  static const IconData shield_star = IconData(0xf575, fontFamily: _fontFamily);
  static const IconData shield_warning = IconData(0xf576, fontFamily: _fontFamily);
  static const IconData shopping_bag = IconData(0xf577, fontFamily: _fontFamily);
  static const IconData shopping_bag_open = IconData(0xf578, fontFamily: _fontFamily);
  static const IconData shopping_cart = IconData(0xf579, fontFamily: _fontFamily);
  static const IconData shopping_cart_simple = IconData(0xf57a, fontFamily: _fontFamily);
  static const IconData shower = IconData(0xf57b, fontFamily: _fontFamily);
  static const IconData shuffle = IconData(0xf57c, fontFamily: _fontFamily);
  static const IconData shuffle_angular = IconData(0xf57d, fontFamily: _fontFamily);
  static const IconData shuffle_simple = IconData(0xf57e, fontFamily: _fontFamily);
  static const IconData sidebar = IconData(0xf57f, fontFamily: _fontFamily);
  static const IconData sidebar_simple = IconData(0xf580, fontFamily: _fontFamily);
  static const IconData sign_in = IconData(0xf581, fontFamily: _fontFamily);
  static const IconData sign_out = IconData(0xf582, fontFamily: _fontFamily);
  static const IconData signpost = IconData(0xf583, fontFamily: _fontFamily);
  static const IconData sim_card = IconData(0xf584, fontFamily: _fontFamily);
  static const IconData sketch_logo = IconData(0xf585, fontFamily: _fontFamily);
  static const IconData skip_back = IconData(0xf586, fontFamily: _fontFamily);
  static const IconData skip_back_circle = IconData(0xf587, fontFamily: _fontFamily);
  static const IconData skip_forward = IconData(0xf588, fontFamily: _fontFamily);
  static const IconData skip_forward_circle = IconData(0xf589, fontFamily: _fontFamily);
  static const IconData skull = IconData(0xf58a, fontFamily: _fontFamily);
  static const IconData slack_logo = IconData(0xf58b, fontFamily: _fontFamily);
  static const IconData sliders = IconData(0xf58c, fontFamily: _fontFamily);
  static const IconData sliders_horizontal = IconData(0xf58d, fontFamily: _fontFamily);
  static const IconData smiley = IconData(0xf58e, fontFamily: _fontFamily);
  static const IconData smiley_blank = IconData(0xf58f, fontFamily: _fontFamily);
  static const IconData smiley_meh = IconData(0xf590, fontFamily: _fontFamily);
  static const IconData smiley_nervous = IconData(0xf591, fontFamily: _fontFamily);
  static const IconData smiley_sad = IconData(0xf592, fontFamily: _fontFamily);
  static const IconData smiley_sticker = IconData(0xf593, fontFamily: _fontFamily);
  static const IconData smiley_wink = IconData(0xf594, fontFamily: _fontFamily);
  static const IconData smiley_x_eyes = IconData(0xf595, fontFamily: _fontFamily);
  static const IconData snapchat_logo = IconData(0xf596, fontFamily: _fontFamily);
  static const IconData snowflake = IconData(0xf597, fontFamily: _fontFamily);
  static const IconData soccer_ball = IconData(0xf598, fontFamily: _fontFamily);
  static const IconData sort_ascending = IconData(0xf599, fontFamily: _fontFamily);
  static const IconData sort_descending = IconData(0xf59a, fontFamily: _fontFamily);
  static const IconData spade = IconData(0xf59b, fontFamily: _fontFamily);
  static const IconData sparkle = IconData(0xf59c, fontFamily: _fontFamily);
  static const IconData speaker_high = IconData(0xf59d, fontFamily: _fontFamily);
  static const IconData speaker_low = IconData(0xf59e, fontFamily: _fontFamily);
  static const IconData speaker_none = IconData(0xf59f, fontFamily: _fontFamily);
  static const IconData speaker_simple_high = IconData(0xf5a0, fontFamily: _fontFamily);
  static const IconData speaker_simple_low = IconData(0xf5a1, fontFamily: _fontFamily);
  static const IconData speaker_simple_none = IconData(0xf5a2, fontFamily: _fontFamily);
  static const IconData speaker_simple_slash = IconData(0xf5a3, fontFamily: _fontFamily);
  static const IconData speaker_simple_x = IconData(0xf5a4, fontFamily: _fontFamily);
  static const IconData speaker_slash = IconData(0xf5a5, fontFamily: _fontFamily);
  static const IconData speaker_x = IconData(0xf5a6, fontFamily: _fontFamily);
  static const IconData spinner = IconData(0xf5a7, fontFamily: _fontFamily);
  static const IconData spinner_gap = IconData(0xf5a8, fontFamily: _fontFamily);
  static const IconData spiral = IconData(0xf5a9, fontFamily: _fontFamily);
  static const IconData spotify_logo = IconData(0xf5aa, fontFamily: _fontFamily);
  static const IconData square = IconData(0xf5ab, fontFamily: _fontFamily);
  static const IconData square_half = IconData(0xf5ac, fontFamily: _fontFamily);
  static const IconData square_half_bottom = IconData(0xf5ad, fontFamily: _fontFamily);
  static const IconData square_logo = IconData(0xf5ae, fontFamily: _fontFamily);
  static const IconData squares_four = IconData(0xf5af, fontFamily: _fontFamily);
  static const IconData stack = IconData(0xf5b0, fontFamily: _fontFamily);
  static const IconData stack_overflow_logo = IconData(0xf5b1, fontFamily: _fontFamily);
  static const IconData stack_simple = IconData(0xf5b2, fontFamily: _fontFamily);
  static const IconData stamp = IconData(0xf5b3, fontFamily: _fontFamily);
  static const IconData star = IconData(0xf5b4, fontFamily: _fontFamily);
  static const IconData star_four = IconData(0xf5b5, fontFamily: _fontFamily);
  static const IconData star_half = IconData(0xf5b6, fontFamily: _fontFamily);
  static const IconData sticker = IconData(0xf5b7, fontFamily: _fontFamily);
  static const IconData stop = IconData(0xf5b8, fontFamily: _fontFamily);
  static const IconData stop_circle = IconData(0xf5b9, fontFamily: _fontFamily);
  static const IconData storefront = IconData(0xf5ba, fontFamily: _fontFamily);
  static const IconData strategy = IconData(0xf5bb, fontFamily: _fontFamily);
  static const IconData stripe_logo = IconData(0xf5bc, fontFamily: _fontFamily);
  static const IconData student = IconData(0xf5bd, fontFamily: _fontFamily);
  static const IconData suitcase = IconData(0xf5be, fontFamily: _fontFamily);
  static const IconData suitcase_simple = IconData(0xf5bf, fontFamily: _fontFamily);
  static const IconData sun = IconData(0xf5c0, fontFamily: _fontFamily);
  static const IconData sun_dim = IconData(0xf5c1, fontFamily: _fontFamily);
  static const IconData sun_horizon = IconData(0xf5c2, fontFamily: _fontFamily);
  static const IconData sunglasses = IconData(0xf5c3, fontFamily: _fontFamily);
  static const IconData swap = IconData(0xf5c4, fontFamily: _fontFamily);
  static const IconData swatches = IconData(0xf5c5, fontFamily: _fontFamily);
  static const IconData sword = IconData(0xf5c6, fontFamily: _fontFamily);
  static const IconData syringe = IconData(0xf5c7, fontFamily: _fontFamily);
  static const IconData t_shirt = IconData(0xf5c8, fontFamily: _fontFamily);
  static const IconData table = IconData(0xf5c9, fontFamily: _fontFamily);
  static const IconData tabs = IconData(0xf5ca, fontFamily: _fontFamily);
  static const IconData tag = IconData(0xf5cb, fontFamily: _fontFamily);
  static const IconData tag_chevron = IconData(0xf5cc, fontFamily: _fontFamily);
  static const IconData tag_simple = IconData(0xf5cd, fontFamily: _fontFamily);
  static const IconData target = IconData(0xf5ce, fontFamily: _fontFamily);
  static const IconData taxi = IconData(0xf5cf, fontFamily: _fontFamily);
  static const IconData telegram_logo = IconData(0xf5d0, fontFamily: _fontFamily);
  static const IconData television = IconData(0xf5d1, fontFamily: _fontFamily);
  static const IconData television_simple = IconData(0xf5d2, fontFamily: _fontFamily);
  static const IconData tennis_ball = IconData(0xf5d3, fontFamily: _fontFamily);
  static const IconData terminal = IconData(0xf5d4, fontFamily: _fontFamily);
  static const IconData terminal_window = IconData(0xf5d5, fontFamily: _fontFamily);
  static const IconData test_tube = IconData(0xf5d6, fontFamily: _fontFamily);
  static const IconData text_aa = IconData(0xf5d7, fontFamily: _fontFamily);
  static const IconData text_align_center = IconData(0xf5d8, fontFamily: _fontFamily);
  static const IconData text_align_justify = IconData(0xf5d9, fontFamily: _fontFamily);
  static const IconData text_align_left = IconData(0xf5da, fontFamily: _fontFamily);
  static const IconData text_align_right = IconData(0xf5db, fontFamily: _fontFamily);
  static const IconData text_bolder = IconData(0xf5dc, fontFamily: _fontFamily);
  static const IconData text_h = IconData(0xf5dd, fontFamily: _fontFamily);
  static const IconData text_h_five = IconData(0xf5de, fontFamily: _fontFamily);
  static const IconData text_h_four = IconData(0xf5df, fontFamily: _fontFamily);
  static const IconData text_h_one = IconData(0xf5e0, fontFamily: _fontFamily);
  static const IconData text_h_six = IconData(0xf5e1, fontFamily: _fontFamily);
  static const IconData text_h_three = IconData(0xf5e2, fontFamily: _fontFamily);
  static const IconData text_h_two = IconData(0xf5e3, fontFamily: _fontFamily);
  static const IconData text_indent = IconData(0xf5e4, fontFamily: _fontFamily);
  static const IconData text_italic = IconData(0xf5e5, fontFamily: _fontFamily);
  static const IconData text_outdent = IconData(0xf5e6, fontFamily: _fontFamily);
  static const IconData text_strikethrough = IconData(0xf5e7, fontFamily: _fontFamily);
  static const IconData text_t = IconData(0xf5e8, fontFamily: _fontFamily);
  static const IconData text_underline = IconData(0xf5e9, fontFamily: _fontFamily);
  static const IconData textbox = IconData(0xf5ea, fontFamily: _fontFamily);
  static const IconData thermometer = IconData(0xf5eb, fontFamily: _fontFamily);
  static const IconData thermometer_cold = IconData(0xf5ec, fontFamily: _fontFamily);
  static const IconData thermometer_hot = IconData(0xf5ed, fontFamily: _fontFamily);
  static const IconData thermometer_simple = IconData(0xf5ee, fontFamily: _fontFamily);
  static const IconData thumbs_down = IconData(0xf5ef, fontFamily: _fontFamily);
  static const IconData thumbs_up = IconData(0xf5f0, fontFamily: _fontFamily);
  static const IconData ticket = IconData(0xf5f1, fontFamily: _fontFamily);
  static const IconData tiktok_logo = IconData(0xf5f2, fontFamily: _fontFamily);
  static const IconData timer = IconData(0xf5f3, fontFamily: _fontFamily);
  static const IconData toggle_left = IconData(0xf5f4, fontFamily: _fontFamily);
  static const IconData toggle_right = IconData(0xf5f5, fontFamily: _fontFamily);
  static const IconData toilet = IconData(0xf5f6, fontFamily: _fontFamily);
  static const IconData toilet_paper = IconData(0xf5f7, fontFamily: _fontFamily);
  static const IconData tote = IconData(0xf5f8, fontFamily: _fontFamily);
  static const IconData tote_simple = IconData(0xf5f9, fontFamily: _fontFamily);
  static const IconData trademark_registered = IconData(0xf5fa, fontFamily: _fontFamily);
  static const IconData traffic_cone = IconData(0xf5fb, fontFamily: _fontFamily);
  static const IconData traffic_sign = IconData(0xf5fc, fontFamily: _fontFamily);
  static const IconData traffic_signal = IconData(0xf5fd, fontFamily: _fontFamily);
  static const IconData train = IconData(0xf5fe, fontFamily: _fontFamily);
  static const IconData train_regional = IconData(0xf5ff, fontFamily: _fontFamily);
  static const IconData train_simple = IconData(0xf600, fontFamily: _fontFamily);
  static const IconData translate = IconData(0xf601, fontFamily: _fontFamily);
  static const IconData trash = IconData(0xf602, fontFamily: _fontFamily);
  static const IconData trash_simple = IconData(0xf603, fontFamily: _fontFamily);
  static const IconData tray = IconData(0xf604, fontFamily: _fontFamily);
  static const IconData tree = IconData(0xf605, fontFamily: _fontFamily);
  static const IconData tree_evergreen = IconData(0xf606, fontFamily: _fontFamily);
  static const IconData tree_structure = IconData(0xf607, fontFamily: _fontFamily);
  static const IconData trend_down = IconData(0xf608, fontFamily: _fontFamily);
  static const IconData trend_up = IconData(0xf609, fontFamily: _fontFamily);
  static const IconData triangle = IconData(0xf60a, fontFamily: _fontFamily);
  static const IconData trophy = IconData(0xf60b, fontFamily: _fontFamily);
  static const IconData truck = IconData(0xf60c, fontFamily: _fontFamily);
  static const IconData twitch_logo = IconData(0xf60d, fontFamily: _fontFamily);
  static const IconData twitter_logo = IconData(0xf60e, fontFamily: _fontFamily);
  static const IconData umbrella = IconData(0xf60f, fontFamily: _fontFamily);
  static const IconData umbrella_simple = IconData(0xf610, fontFamily: _fontFamily);
  static const IconData upload = IconData(0xf611, fontFamily: _fontFamily);
  static const IconData upload_simple = IconData(0xf612, fontFamily: _fontFamily);
  static const IconData user = IconData(0xf613, fontFamily: _fontFamily);
  static const IconData user_circle = IconData(0xf614, fontFamily: _fontFamily);
  static const IconData user_circle_gear = IconData(0xf615, fontFamily: _fontFamily);
  static const IconData user_circle_minus = IconData(0xf616, fontFamily: _fontFamily);
  static const IconData user_circle_plus = IconData(0xf617, fontFamily: _fontFamily);
  static const IconData user_focus = IconData(0xf618, fontFamily: _fontFamily);
  static const IconData user_gear = IconData(0xf619, fontFamily: _fontFamily);
  static const IconData user_list = IconData(0xf61a, fontFamily: _fontFamily);
  static const IconData user_minus = IconData(0xf61b, fontFamily: _fontFamily);
  static const IconData user_plus = IconData(0xf61c, fontFamily: _fontFamily);
  static const IconData user_rectangle = IconData(0xf61d, fontFamily: _fontFamily);
  static const IconData user_square = IconData(0xf61e, fontFamily: _fontFamily);
  static const IconData user_switch = IconData(0xf61f, fontFamily: _fontFamily);
  static const IconData users = IconData(0xf620, fontFamily: _fontFamily);
  static const IconData users_four = IconData(0xf621, fontFamily: _fontFamily);
  static const IconData users_three = IconData(0xf622, fontFamily: _fontFamily);
  static const IconData vault = IconData(0xf623, fontFamily: _fontFamily);
  static const IconData vibrate = IconData(0xf624, fontFamily: _fontFamily);
  static const IconData video_camera = IconData(0xf625, fontFamily: _fontFamily);
  static const IconData video_camera_slash = IconData(0xf626, fontFamily: _fontFamily);
  static const IconData vignette = IconData(0xf627, fontFamily: _fontFamily);
  static const IconData voicemail = IconData(0xf628, fontFamily: _fontFamily);
  static const IconData volleyball = IconData(0xf629, fontFamily: _fontFamily);
  static const IconData wall = IconData(0xf62a, fontFamily: _fontFamily);
  static const IconData wallet = IconData(0xf62b, fontFamily: _fontFamily);
  static const IconData warning = IconData(0xf62c, fontFamily: _fontFamily);
  static const IconData warning_circle = IconData(0xf62d, fontFamily: _fontFamily);
  static const IconData warning_octagon = IconData(0xf62e, fontFamily: _fontFamily);
  static const IconData watch = IconData(0xf62f, fontFamily: _fontFamily);
  static const IconData wave_sawtooth = IconData(0xf630, fontFamily: _fontFamily);
  static const IconData wave_sine = IconData(0xf631, fontFamily: _fontFamily);
  static const IconData wave_square = IconData(0xf632, fontFamily: _fontFamily);
  static const IconData wave_triangle = IconData(0xf633, fontFamily: _fontFamily);
  static const IconData waves = IconData(0xf634, fontFamily: _fontFamily);
  static const IconData webcam = IconData(0xf635, fontFamily: _fontFamily);
  static const IconData whatsapp_logo = IconData(0xf636, fontFamily: _fontFamily);
  static const IconData wheelchair = IconData(0xf637, fontFamily: _fontFamily);
  static const IconData wifi_high = IconData(0xf638, fontFamily: _fontFamily);
  static const IconData wifi_low = IconData(0xf639, fontFamily: _fontFamily);
  static const IconData wifi_medium = IconData(0xf63a, fontFamily: _fontFamily);
  static const IconData wifi_none = IconData(0xf63b, fontFamily: _fontFamily);
  static const IconData wifi_slash = IconData(0xf63c, fontFamily: _fontFamily);
  static const IconData wifi_x = IconData(0xf63d, fontFamily: _fontFamily);
  static const IconData wind = IconData(0xf63e, fontFamily: _fontFamily);
  static const IconData windows_logo = IconData(0xf63f, fontFamily: _fontFamily);
  static const IconData wine = IconData(0xf640, fontFamily: _fontFamily);
  static const IconData wrench = IconData(0xf641, fontFamily: _fontFamily);
  static const IconData x = IconData(0xf642, fontFamily: _fontFamily);
  static const IconData x_circle = IconData(0xf643, fontFamily: _fontFamily);
  static const IconData x_square = IconData(0xf644, fontFamily: _fontFamily);
  static const IconData yin_yang = IconData(0xf645, fontFamily: _fontFamily);
  static const IconData youtube_logo = IconData(0xf646, fontFamily: _fontFamily);
  static const IconData activity_bold = IconData(0xf647, fontFamily: _fontFamily);
  static const IconData address_book_bold = IconData(0xf648, fontFamily: _fontFamily);
  static const IconData airplane_bold = IconData(0xf649, fontFamily: _fontFamily);
  static const IconData airplane_in_flight_bold = IconData(0xf64a, fontFamily: _fontFamily);
  static const IconData airplane_landing_bold = IconData(0xf64b, fontFamily: _fontFamily);
  static const IconData airplane_takeoff_bold = IconData(0xf64c, fontFamily: _fontFamily);
  static const IconData airplane_tilt_bold = IconData(0xf64d, fontFamily: _fontFamily);
  static const IconData airplay_bold = IconData(0xf64e, fontFamily: _fontFamily);
  static const IconData alarm_bold = IconData(0xf64f, fontFamily: _fontFamily);
  static const IconData alien_bold = IconData(0xf650, fontFamily: _fontFamily);
  static const IconData align_bottom_bold = IconData(0xf651, fontFamily: _fontFamily);
  static const IconData align_bottom_simple_bold = IconData(0xf652, fontFamily: _fontFamily);
  static const IconData align_center_horizontal_bold = IconData(0xf653, fontFamily: _fontFamily);
  static const IconData align_center_horizontal_simple_bold = IconData(0xf654, fontFamily: _fontFamily);
  static const IconData align_center_vertical_bold = IconData(0xf655, fontFamily: _fontFamily);
  static const IconData align_center_vertical_simple_bold = IconData(0xf656, fontFamily: _fontFamily);
  static const IconData align_left_bold = IconData(0xf657, fontFamily: _fontFamily);
  static const IconData align_left_simple_bold = IconData(0xf658, fontFamily: _fontFamily);
  static const IconData align_right_bold = IconData(0xf659, fontFamily: _fontFamily);
  static const IconData align_right_simple_bold = IconData(0xf65a, fontFamily: _fontFamily);
  static const IconData align_top_bold = IconData(0xf65b, fontFamily: _fontFamily);
  static const IconData align_top_simple_bold = IconData(0xf65c, fontFamily: _fontFamily);
  static const IconData anchor_bold = IconData(0xf65d, fontFamily: _fontFamily);
  static const IconData anchor_simple_bold = IconData(0xf65e, fontFamily: _fontFamily);
  static const IconData android_logo_bold = IconData(0xf65f, fontFamily: _fontFamily);
  static const IconData angular_logo_bold = IconData(0xf660, fontFamily: _fontFamily);
  static const IconData aperture_bold = IconData(0xf661, fontFamily: _fontFamily);
  static const IconData app_store_logo_bold = IconData(0xf662, fontFamily: _fontFamily);
  static const IconData app_window_bold = IconData(0xf663, fontFamily: _fontFamily);
  static const IconData apple_logo_bold = IconData(0xf664, fontFamily: _fontFamily);
  static const IconData apple_podcasts_logo_bold = IconData(0xf665, fontFamily: _fontFamily);
  static const IconData archive_bold = IconData(0xf666, fontFamily: _fontFamily);
  static const IconData archive_box_bold = IconData(0xf667, fontFamily: _fontFamily);
  static const IconData archive_tray_bold = IconData(0xf668, fontFamily: _fontFamily);
  static const IconData armchair_bold = IconData(0xf669, fontFamily: _fontFamily);
  static const IconData arrow_arc_left_bold = IconData(0xf66a, fontFamily: _fontFamily);
  static const IconData arrow_arc_right_bold = IconData(0xf66b, fontFamily: _fontFamily);
  static const IconData arrow_bend_double_up_left_bold = IconData(0xf66c, fontFamily: _fontFamily);
  static const IconData arrow_bend_double_up_right_bold = IconData(0xf66d, fontFamily: _fontFamily);
  static const IconData arrow_bend_down_left_bold = IconData(0xf66e, fontFamily: _fontFamily);
  static const IconData arrow_bend_down_right_bold = IconData(0xf66f, fontFamily: _fontFamily);
  static const IconData arrow_bend_left_down_bold = IconData(0xf670, fontFamily: _fontFamily);
  static const IconData arrow_bend_left_up_bold = IconData(0xf671, fontFamily: _fontFamily);
  static const IconData arrow_bend_right_down_bold = IconData(0xf672, fontFamily: _fontFamily);
  static const IconData arrow_bend_right_up_bold = IconData(0xf673, fontFamily: _fontFamily);
  static const IconData arrow_bend_up_left_bold = IconData(0xf674, fontFamily: _fontFamily);
  static const IconData arrow_bend_up_right_bold = IconData(0xf675, fontFamily: _fontFamily);
  static const IconData arrow_circle_down_bold = IconData(0xf676, fontFamily: _fontFamily);
  static const IconData arrow_circle_down_left_bold = IconData(0xf677, fontFamily: _fontFamily);
  static const IconData arrow_circle_down_right_bold = IconData(0xf678, fontFamily: _fontFamily);
  static const IconData arrow_circle_left_bold = IconData(0xf679, fontFamily: _fontFamily);
  static const IconData arrow_circle_right_bold = IconData(0xf67a, fontFamily: _fontFamily);
  static const IconData arrow_circle_up_bold = IconData(0xf67b, fontFamily: _fontFamily);
  static const IconData arrow_circle_up_left_bold = IconData(0xf67c, fontFamily: _fontFamily);
  static const IconData arrow_circle_up_right_bold = IconData(0xf67d, fontFamily: _fontFamily);
  static const IconData arrow_clockwise_bold = IconData(0xf67e, fontFamily: _fontFamily);
  static const IconData arrow_counter_clockwise_bold = IconData(0xf67f, fontFamily: _fontFamily);
  static const IconData arrow_down_bold = IconData(0xf680, fontFamily: _fontFamily);
  static const IconData arrow_down_left_bold = IconData(0xf681, fontFamily: _fontFamily);
  static const IconData arrow_down_right_bold = IconData(0xf682, fontFamily: _fontFamily);
  static const IconData arrow_elbow_down_left_bold = IconData(0xf683, fontFamily: _fontFamily);
  static const IconData arrow_elbow_down_right_bold = IconData(0xf684, fontFamily: _fontFamily);
  static const IconData arrow_elbow_left_bold = IconData(0xf685, fontFamily: _fontFamily);
  static const IconData arrow_elbow_left_down_bold = IconData(0xf686, fontFamily: _fontFamily);
  static const IconData arrow_elbow_left_up_bold = IconData(0xf687, fontFamily: _fontFamily);
  static const IconData arrow_elbow_right_bold = IconData(0xf688, fontFamily: _fontFamily);
  static const IconData arrow_elbow_right_down_bold = IconData(0xf689, fontFamily: _fontFamily);
  static const IconData arrow_elbow_right_up_bold = IconData(0xf68a, fontFamily: _fontFamily);
  static const IconData arrow_elbow_up_left_bold = IconData(0xf68b, fontFamily: _fontFamily);
  static const IconData arrow_elbow_up_right_bold = IconData(0xf68c, fontFamily: _fontFamily);
  static const IconData arrow_fat_down_bold = IconData(0xf68d, fontFamily: _fontFamily);
  static const IconData arrow_fat_left_bold = IconData(0xf68e, fontFamily: _fontFamily);
  static const IconData arrow_fat_line_down_bold = IconData(0xf68f, fontFamily: _fontFamily);
  static const IconData arrow_fat_line_left_bold = IconData(0xf690, fontFamily: _fontFamily);
  static const IconData arrow_fat_line_right_bold = IconData(0xf691, fontFamily: _fontFamily);
  static const IconData arrow_fat_line_up_bold = IconData(0xf692, fontFamily: _fontFamily);
  static const IconData arrow_fat_lines_down_bold = IconData(0xf693, fontFamily: _fontFamily);
  static const IconData arrow_fat_lines_left_bold = IconData(0xf694, fontFamily: _fontFamily);
  static const IconData arrow_fat_lines_right_bold = IconData(0xf695, fontFamily: _fontFamily);
  static const IconData arrow_fat_lines_up_bold = IconData(0xf696, fontFamily: _fontFamily);
  static const IconData arrow_fat_right_bold = IconData(0xf697, fontFamily: _fontFamily);
  static const IconData arrow_fat_up_bold = IconData(0xf698, fontFamily: _fontFamily);
  static const IconData arrow_left_bold = IconData(0xf699, fontFamily: _fontFamily);
  static const IconData arrow_line_down_bold = IconData(0xf69a, fontFamily: _fontFamily);
  static const IconData arrow_line_down_left_bold = IconData(0xf69b, fontFamily: _fontFamily);
  static const IconData arrow_line_down_right_bold = IconData(0xf69c, fontFamily: _fontFamily);
  static const IconData arrow_line_left_bold = IconData(0xf69d, fontFamily: _fontFamily);
  static const IconData arrow_line_right_bold = IconData(0xf69e, fontFamily: _fontFamily);
  static const IconData arrow_line_up_bold = IconData(0xf69f, fontFamily: _fontFamily);
  static const IconData arrow_line_up_left_bold = IconData(0xf6a0, fontFamily: _fontFamily);
  static const IconData arrow_line_up_right_bold = IconData(0xf6a1, fontFamily: _fontFamily);
  static const IconData arrow_right_bold = IconData(0xf6a2, fontFamily: _fontFamily);
  static const IconData arrow_square_down_bold = IconData(0xf6a3, fontFamily: _fontFamily);
  static const IconData arrow_square_down_left_bold = IconData(0xf6a4, fontFamily: _fontFamily);
  static const IconData arrow_square_down_right_bold = IconData(0xf6a5, fontFamily: _fontFamily);
  static const IconData arrow_square_in_bold = IconData(0xf6a6, fontFamily: _fontFamily);
  static const IconData arrow_square_left_bold = IconData(0xf6a7, fontFamily: _fontFamily);
  static const IconData arrow_square_out_bold = IconData(0xf6a8, fontFamily: _fontFamily);
  static const IconData arrow_square_right_bold = IconData(0xf6a9, fontFamily: _fontFamily);
  static const IconData arrow_square_up_bold = IconData(0xf6aa, fontFamily: _fontFamily);
  static const IconData arrow_square_up_left_bold = IconData(0xf6ab, fontFamily: _fontFamily);
  static const IconData arrow_square_up_right_bold = IconData(0xf6ac, fontFamily: _fontFamily);
  static const IconData arrow_u_down_left_bold = IconData(0xf6ad, fontFamily: _fontFamily);
  static const IconData arrow_u_down_right_bold = IconData(0xf6ae, fontFamily: _fontFamily);
  static const IconData arrow_u_left_down_bold = IconData(0xf6af, fontFamily: _fontFamily);
  static const IconData arrow_u_left_up_bold = IconData(0xf6b0, fontFamily: _fontFamily);
  static const IconData arrow_u_right_down_bold = IconData(0xf6b1, fontFamily: _fontFamily);
  static const IconData arrow_u_right_up_bold = IconData(0xf6b2, fontFamily: _fontFamily);
  static const IconData arrow_u_up_left_bold = IconData(0xf6b3, fontFamily: _fontFamily);
  static const IconData arrow_u_up_right_bold = IconData(0xf6b4, fontFamily: _fontFamily);
  static const IconData arrow_up_bold = IconData(0xf6b5, fontFamily: _fontFamily);
  static const IconData arrow_up_left_bold = IconData(0xf6b6, fontFamily: _fontFamily);
  static const IconData arrow_up_right_bold = IconData(0xf6b7, fontFamily: _fontFamily);
  static const IconData arrows_clockwise_bold = IconData(0xf6b8, fontFamily: _fontFamily);
  static const IconData arrows_counter_clockwise_bold = IconData(0xf6b9, fontFamily: _fontFamily);
  static const IconData arrows_down_up_bold = IconData(0xf6ba, fontFamily: _fontFamily);
  static const IconData arrows_horizontal_bold = IconData(0xf6bb, fontFamily: _fontFamily);
  static const IconData arrows_in_bold = IconData(0xf6bc, fontFamily: _fontFamily);
  static const IconData arrows_in_cardinal_bold = IconData(0xf6bd, fontFamily: _fontFamily);
  static const IconData arrows_in_line_horizontal_bold = IconData(0xf6be, fontFamily: _fontFamily);
  static const IconData arrows_in_line_vertical_bold = IconData(0xf6bf, fontFamily: _fontFamily);
  static const IconData arrows_in_simple_bold = IconData(0xf6c0, fontFamily: _fontFamily);
  static const IconData arrows_left_right_bold = IconData(0xf6c1, fontFamily: _fontFamily);
  static const IconData arrows_out_bold = IconData(0xf6c2, fontFamily: _fontFamily);
  static const IconData arrows_out_cardinal_bold = IconData(0xf6c3, fontFamily: _fontFamily);
  static const IconData arrows_out_line_horizontal_bold = IconData(0xf6c4, fontFamily: _fontFamily);
  static const IconData arrows_out_line_vertical_bold = IconData(0xf6c5, fontFamily: _fontFamily);
  static const IconData arrows_out_simple_bold = IconData(0xf6c6, fontFamily: _fontFamily);
  static const IconData arrows_vertical_bold = IconData(0xf6c7, fontFamily: _fontFamily);
  static const IconData article_bold = IconData(0xf6c8, fontFamily: _fontFamily);
  static const IconData article_medium_bold = IconData(0xf6c9, fontFamily: _fontFamily);
  static const IconData article_ny_times_bold = IconData(0xf6ca, fontFamily: _fontFamily);
  static const IconData asterisk_bold = IconData(0xf6cb, fontFamily: _fontFamily);
  static const IconData asterisk_simple_bold = IconData(0xf6cc, fontFamily: _fontFamily);
  static const IconData at_bold = IconData(0xf6cd, fontFamily: _fontFamily);
  static const IconData atom_bold = IconData(0xf6ce, fontFamily: _fontFamily);
  static const IconData baby_bold = IconData(0xf6cf, fontFamily: _fontFamily);
  static const IconData backpack_bold = IconData(0xf6d0, fontFamily: _fontFamily);
  static const IconData backspace_bold = IconData(0xf6d1, fontFamily: _fontFamily);
  static const IconData bag_bold = IconData(0xf6d2, fontFamily: _fontFamily);
  static const IconData bag_simple_bold = IconData(0xf6d3, fontFamily: _fontFamily);
  static const IconData balloon_bold = IconData(0xf6d4, fontFamily: _fontFamily);
  static const IconData bandaids_bold = IconData(0xf6d5, fontFamily: _fontFamily);
  static const IconData bank_bold = IconData(0xf6d6, fontFamily: _fontFamily);
  static const IconData barbell_bold = IconData(0xf6d7, fontFamily: _fontFamily);
  static const IconData barcode_bold = IconData(0xf6d8, fontFamily: _fontFamily);
  static const IconData barricade_bold = IconData(0xf6d9, fontFamily: _fontFamily);
  static const IconData baseball_bold = IconData(0xf6da, fontFamily: _fontFamily);
  static const IconData basketball_bold = IconData(0xf6db, fontFamily: _fontFamily);
  static const IconData bathtub_bold = IconData(0xf6dc, fontFamily: _fontFamily);
  static const IconData battery_charging_bold = IconData(0xf6dd, fontFamily: _fontFamily);
  static const IconData battery_charging_vertical_bold = IconData(0xf6de, fontFamily: _fontFamily);
  static const IconData battery_empty_bold = IconData(0xf6df, fontFamily: _fontFamily);
  static const IconData battery_full_bold = IconData(0xf6e0, fontFamily: _fontFamily);
  static const IconData battery_high_bold = IconData(0xf6e1, fontFamily: _fontFamily);
  static const IconData battery_low_bold = IconData(0xf6e2, fontFamily: _fontFamily);
  static const IconData battery_medium_bold = IconData(0xf6e3, fontFamily: _fontFamily);
  static const IconData battery_plus_bold = IconData(0xf6e4, fontFamily: _fontFamily);
  static const IconData battery_warning_bold = IconData(0xf6e5, fontFamily: _fontFamily);
  static const IconData battery_warning_vertical_bold = IconData(0xf6e6, fontFamily: _fontFamily);
  static const IconData bed_bold = IconData(0xf6e7, fontFamily: _fontFamily);
  static const IconData beer_bottle_bold = IconData(0xf6e8, fontFamily: _fontFamily);
  static const IconData behance_logo_bold = IconData(0xf6e9, fontFamily: _fontFamily);
  static const IconData bell_bold = IconData(0xf6ea, fontFamily: _fontFamily);
  static const IconData bell_ringing_bold = IconData(0xf6eb, fontFamily: _fontFamily);
  static const IconData bell_simple_bold = IconData(0xf6ec, fontFamily: _fontFamily);
  static const IconData bell_simple_ringing_bold = IconData(0xf6ed, fontFamily: _fontFamily);
  static const IconData bell_simple_slash_bold = IconData(0xf6ee, fontFamily: _fontFamily);
  static const IconData bell_simple_z_bold = IconData(0xf6ef, fontFamily: _fontFamily);
  static const IconData bell_slash_bold = IconData(0xf6f0, fontFamily: _fontFamily);
  static const IconData bell_z_bold = IconData(0xf6f1, fontFamily: _fontFamily);
  static const IconData bezier_curve_bold = IconData(0xf6f2, fontFamily: _fontFamily);
  static const IconData bicycle_bold = IconData(0xf6f3, fontFamily: _fontFamily);
  static const IconData binoculars_bold = IconData(0xf6f4, fontFamily: _fontFamily);
  static const IconData bird_bold = IconData(0xf6f5, fontFamily: _fontFamily);
  static const IconData bluetooth_bold = IconData(0xf6f6, fontFamily: _fontFamily);
  static const IconData bluetooth_connected_bold = IconData(0xf6f7, fontFamily: _fontFamily);
  static const IconData bluetooth_slash_bold = IconData(0xf6f8, fontFamily: _fontFamily);
  static const IconData bluetooth_x_bold = IconData(0xf6f9, fontFamily: _fontFamily);
  static const IconData boat_bold = IconData(0xf6fa, fontFamily: _fontFamily);
  static const IconData book_bold = IconData(0xf6fb, fontFamily: _fontFamily);
  static const IconData book_bookmark_bold = IconData(0xf6fc, fontFamily: _fontFamily);
  static const IconData book_open_bold = IconData(0xf6fd, fontFamily: _fontFamily);
  static const IconData bookmark_bold = IconData(0xf6fe, fontFamily: _fontFamily);
  static const IconData bookmark_simple_bold = IconData(0xf6ff, fontFamily: _fontFamily);
  static const IconData bookmarks_bold = IconData(0xf700, fontFamily: _fontFamily);
  static const IconData bookmarks_simple_bold = IconData(0xf701, fontFamily: _fontFamily);
  static const IconData books_bold = IconData(0xf702, fontFamily: _fontFamily);
  static const IconData bounding_box_bold = IconData(0xf703, fontFamily: _fontFamily);
  static const IconData brackets_angle_bold = IconData(0xf704, fontFamily: _fontFamily);
  static const IconData brackets_curly_bold = IconData(0xf705, fontFamily: _fontFamily);
  static const IconData brackets_round_bold = IconData(0xf706, fontFamily: _fontFamily);
  static const IconData brackets_square_bold = IconData(0xf707, fontFamily: _fontFamily);
  static const IconData brain_bold = IconData(0xf708, fontFamily: _fontFamily);
  static const IconData brandy_bold = IconData(0xf709, fontFamily: _fontFamily);
  static const IconData briefcase_bold = IconData(0xf70a, fontFamily: _fontFamily);
  static const IconData briefcase_metal_bold = IconData(0xf70b, fontFamily: _fontFamily);
  static const IconData broadcast_bold = IconData(0xf70c, fontFamily: _fontFamily);
  static const IconData browser_bold = IconData(0xf70d, fontFamily: _fontFamily);
  static const IconData browsers_bold = IconData(0xf70e, fontFamily: _fontFamily);
  static const IconData bug_beetle_bold = IconData(0xf70f, fontFamily: _fontFamily);
  static const IconData bug_bold = IconData(0xf710, fontFamily: _fontFamily);
  static const IconData bug_droid_bold = IconData(0xf711, fontFamily: _fontFamily);
  static const IconData buildings_bold = IconData(0xf712, fontFamily: _fontFamily);
  static const IconData bus_bold = IconData(0xf713, fontFamily: _fontFamily);
  static const IconData butterfly_bold = IconData(0xf714, fontFamily: _fontFamily);
  static const IconData cactus_bold = IconData(0xf715, fontFamily: _fontFamily);
  static const IconData cake_bold = IconData(0xf716, fontFamily: _fontFamily);
  static const IconData calculator_bold = IconData(0xf717, fontFamily: _fontFamily);
  static const IconData calendar_blank_bold = IconData(0xf718, fontFamily: _fontFamily);
  static const IconData calendar_bold = IconData(0xf719, fontFamily: _fontFamily);
  static const IconData calendar_check_bold = IconData(0xf71a, fontFamily: _fontFamily);
  static const IconData calendar_plus_bold = IconData(0xf71b, fontFamily: _fontFamily);
  static const IconData calendar_x_bold = IconData(0xf71c, fontFamily: _fontFamily);
  static const IconData camera_bold = IconData(0xf71d, fontFamily: _fontFamily);
  static const IconData camera_rotate_bold = IconData(0xf71e, fontFamily: _fontFamily);
  static const IconData camera_slash_bold = IconData(0xf71f, fontFamily: _fontFamily);
  static const IconData campfire_bold = IconData(0xf720, fontFamily: _fontFamily);
  static const IconData car_bold = IconData(0xf721, fontFamily: _fontFamily);
  static const IconData car_simple_bold = IconData(0xf722, fontFamily: _fontFamily);
  static const IconData cardholder_bold = IconData(0xf723, fontFamily: _fontFamily);
  static const IconData cards_bold = IconData(0xf724, fontFamily: _fontFamily);
  static const IconData caret_circle_double_down_bold = IconData(0xf725, fontFamily: _fontFamily);
  static const IconData caret_circle_double_left_bold = IconData(0xf726, fontFamily: _fontFamily);
  static const IconData caret_circle_double_right_bold = IconData(0xf727, fontFamily: _fontFamily);
  static const IconData caret_circle_double_up_bold = IconData(0xf728, fontFamily: _fontFamily);
  static const IconData caret_circle_down_bold = IconData(0xf729, fontFamily: _fontFamily);
  static const IconData caret_circle_left_bold = IconData(0xf72a, fontFamily: _fontFamily);
  static const IconData caret_circle_right_bold = IconData(0xf72b, fontFamily: _fontFamily);
  static const IconData caret_circle_up_bold = IconData(0xf72c, fontFamily: _fontFamily);
  static const IconData caret_double_down_bold = IconData(0xf72d, fontFamily: _fontFamily);
  static const IconData caret_double_left_bold = IconData(0xf72e, fontFamily: _fontFamily);
  static const IconData caret_double_right_bold = IconData(0xf72f, fontFamily: _fontFamily);
  static const IconData caret_double_up_bold = IconData(0xf730, fontFamily: _fontFamily);
  static const IconData caret_down_bold = IconData(0xf731, fontFamily: _fontFamily);
  static const IconData caret_left_bold = IconData(0xf732, fontFamily: _fontFamily);
  static const IconData caret_right_bold = IconData(0xf733, fontFamily: _fontFamily);
  static const IconData caret_up_bold = IconData(0xf734, fontFamily: _fontFamily);
  static const IconData cat_bold = IconData(0xf735, fontFamily: _fontFamily);
  static const IconData cell_signal_full_bold = IconData(0xf736, fontFamily: _fontFamily);
  static const IconData cell_signal_high_bold = IconData(0xf737, fontFamily: _fontFamily);
  static const IconData cell_signal_low_bold = IconData(0xf738, fontFamily: _fontFamily);
  static const IconData cell_signal_medium_bold = IconData(0xf739, fontFamily: _fontFamily);
  static const IconData cell_signal_none_bold = IconData(0xf73a, fontFamily: _fontFamily);
  static const IconData cell_signal_slash_bold = IconData(0xf73b, fontFamily: _fontFamily);
  static const IconData cell_signal_x_bold = IconData(0xf73c, fontFamily: _fontFamily);
  static const IconData chalkboard_bold = IconData(0xf73d, fontFamily: _fontFamily);
  static const IconData chalkboard_simple_bold = IconData(0xf73e, fontFamily: _fontFamily);
  static const IconData chalkboard_teacher_bold = IconData(0xf73f, fontFamily: _fontFamily);
  static const IconData chart_bar_bold = IconData(0xf740, fontFamily: _fontFamily);
  static const IconData chart_bar_horizontal_bold = IconData(0xf741, fontFamily: _fontFamily);
  static const IconData chart_line_bold = IconData(0xf742, fontFamily: _fontFamily);
  static const IconData chart_line_up_bold = IconData(0xf743, fontFamily: _fontFamily);
  static const IconData chart_pie_bold = IconData(0xf744, fontFamily: _fontFamily);
  static const IconData chart_pie_slice_bold = IconData(0xf745, fontFamily: _fontFamily);
  static const IconData chat_bold = IconData(0xf746, fontFamily: _fontFamily);
  static const IconData chat_centered_bold = IconData(0xf747, fontFamily: _fontFamily);
  static const IconData chat_centered_dots_bold = IconData(0xf748, fontFamily: _fontFamily);
  static const IconData chat_centered_text_bold = IconData(0xf749, fontFamily: _fontFamily);
  static const IconData chat_circle_bold = IconData(0xf74a, fontFamily: _fontFamily);
  static const IconData chat_circle_dots_bold = IconData(0xf74b, fontFamily: _fontFamily);
  static const IconData chat_circle_text_bold = IconData(0xf74c, fontFamily: _fontFamily);
  static const IconData chat_dots_bold = IconData(0xf74d, fontFamily: _fontFamily);
  static const IconData chat_teardrop_bold = IconData(0xf74e, fontFamily: _fontFamily);
  static const IconData chat_teardrop_dots_bold = IconData(0xf74f, fontFamily: _fontFamily);
  static const IconData chat_teardrop_text_bold = IconData(0xf750, fontFamily: _fontFamily);
  static const IconData chat_text_bold = IconData(0xf751, fontFamily: _fontFamily);
  static const IconData chats_bold = IconData(0xf752, fontFamily: _fontFamily);
  static const IconData chats_circle_bold = IconData(0xf753, fontFamily: _fontFamily);
  static const IconData chats_teardrop_bold = IconData(0xf754, fontFamily: _fontFamily);
  static const IconData check_bold = IconData(0xf755, fontFamily: _fontFamily);
  static const IconData check_circle_bold = IconData(0xf756, fontFamily: _fontFamily);
  static const IconData check_square_bold = IconData(0xf757, fontFamily: _fontFamily);
  static const IconData check_square_offset_bold = IconData(0xf758, fontFamily: _fontFamily);
  static const IconData checks_bold = IconData(0xf759, fontFamily: _fontFamily);
  static const IconData circle_bold = IconData(0xf75a, fontFamily: _fontFamily);
  static const IconData circle_dashed_bold = IconData(0xf75b, fontFamily: _fontFamily);
  static const IconData circle_half_bold = IconData(0xf75c, fontFamily: _fontFamily);
  static const IconData circle_half_tilt_bold = IconData(0xf75d, fontFamily: _fontFamily);
  static const IconData circle_notch_bold = IconData(0xf75e, fontFamily: _fontFamily);
  static const IconData circle_wavy_bold = IconData(0xf75f, fontFamily: _fontFamily);
  static const IconData circle_wavy_check_bold = IconData(0xf760, fontFamily: _fontFamily);
  static const IconData circle_wavy_question_bold = IconData(0xf761, fontFamily: _fontFamily);
  static const IconData circle_wavy_warning_bold = IconData(0xf762, fontFamily: _fontFamily);
  static const IconData circles_four_bold = IconData(0xf763, fontFamily: _fontFamily);
  static const IconData circles_three_bold = IconData(0xf764, fontFamily: _fontFamily);
  static const IconData circles_three_plus_bold = IconData(0xf765, fontFamily: _fontFamily);
  static const IconData clipboard_bold = IconData(0xf766, fontFamily: _fontFamily);
  static const IconData clipboard_text_bold = IconData(0xf767, fontFamily: _fontFamily);
  static const IconData clock_afternoon_bold = IconData(0xf768, fontFamily: _fontFamily);
  static const IconData clock_bold = IconData(0xf769, fontFamily: _fontFamily);
  static const IconData clock_clockwise_bold = IconData(0xf76a, fontFamily: _fontFamily);
  static const IconData clock_counter_clockwise_bold = IconData(0xf76b, fontFamily: _fontFamily);
  static const IconData closed_captioning_bold = IconData(0xf76c, fontFamily: _fontFamily);
  static const IconData cloud_arrow_down_bold = IconData(0xf76d, fontFamily: _fontFamily);
  static const IconData cloud_arrow_up_bold = IconData(0xf76e, fontFamily: _fontFamily);
  static const IconData cloud_bold = IconData(0xf76f, fontFamily: _fontFamily);
  static const IconData cloud_check_bold = IconData(0xf770, fontFamily: _fontFamily);
  static const IconData cloud_fog_bold = IconData(0xf771, fontFamily: _fontFamily);
  static const IconData cloud_lightning_bold = IconData(0xf772, fontFamily: _fontFamily);
  static const IconData cloud_moon_bold = IconData(0xf773, fontFamily: _fontFamily);
  static const IconData cloud_rain_bold = IconData(0xf774, fontFamily: _fontFamily);
  static const IconData cloud_slash_bold = IconData(0xf775, fontFamily: _fontFamily);
  static const IconData cloud_snow_bold = IconData(0xf776, fontFamily: _fontFamily);
  static const IconData cloud_sun_bold = IconData(0xf777, fontFamily: _fontFamily);
  static const IconData club_bold = IconData(0xf778, fontFamily: _fontFamily);
  static const IconData coat_hanger_bold = IconData(0xf779, fontFamily: _fontFamily);
  static const IconData code_bold = IconData(0xf77a, fontFamily: _fontFamily);
  static const IconData code_simple_bold = IconData(0xf77b, fontFamily: _fontFamily);
  static const IconData codepen_logo_bold = IconData(0xf77c, fontFamily: _fontFamily);
  static const IconData codesandbox_logo_bold = IconData(0xf77d, fontFamily: _fontFamily);
  static const IconData coffee_bold = IconData(0xf77e, fontFamily: _fontFamily);
  static const IconData coin_bold = IconData(0xf77f, fontFamily: _fontFamily);
  static const IconData coin_vertical_bold = IconData(0xf780, fontFamily: _fontFamily);
  static const IconData coins_bold = IconData(0xf781, fontFamily: _fontFamily);
  static const IconData columns_bold = IconData(0xf782, fontFamily: _fontFamily);
  static const IconData command_bold = IconData(0xf783, fontFamily: _fontFamily);
  static const IconData compass_bold = IconData(0xf784, fontFamily: _fontFamily);
  static const IconData computer_tower_bold = IconData(0xf785, fontFamily: _fontFamily);
  static const IconData confetti_bold = IconData(0xf786, fontFamily: _fontFamily);
  static const IconData cookie_bold = IconData(0xf787, fontFamily: _fontFamily);
  static const IconData cooking_pot_bold = IconData(0xf788, fontFamily: _fontFamily);
  static const IconData copy_bold = IconData(0xf789, fontFamily: _fontFamily);
  static const IconData copy_simple_bold = IconData(0xf78a, fontFamily: _fontFamily);
  static const IconData copyleft_bold = IconData(0xf78b, fontFamily: _fontFamily);
  static const IconData copyright_bold = IconData(0xf78c, fontFamily: _fontFamily);
  static const IconData corners_in_bold = IconData(0xf78d, fontFamily: _fontFamily);
  static const IconData corners_out_bold = IconData(0xf78e, fontFamily: _fontFamily);
  static const IconData cpu_bold = IconData(0xf78f, fontFamily: _fontFamily);
  static const IconData credit_card_bold = IconData(0xf790, fontFamily: _fontFamily);
  static const IconData crop_bold = IconData(0xf791, fontFamily: _fontFamily);
  static const IconData crosshair_bold = IconData(0xf792, fontFamily: _fontFamily);
  static const IconData crosshair_simple_bold = IconData(0xf793, fontFamily: _fontFamily);
  static const IconData crown_bold = IconData(0xf794, fontFamily: _fontFamily);
  static const IconData crown_simple_bold = IconData(0xf795, fontFamily: _fontFamily);
  static const IconData cube_bold = IconData(0xf796, fontFamily: _fontFamily);
  static const IconData currency_btc_bold = IconData(0xf797, fontFamily: _fontFamily);
  static const IconData currency_circle_dollar_bold = IconData(0xf798, fontFamily: _fontFamily);
  static const IconData currency_cny_bold = IconData(0xf799, fontFamily: _fontFamily);
  static const IconData currency_dollar_bold = IconData(0xf79a, fontFamily: _fontFamily);
  static const IconData currency_dollar_simple_bold = IconData(0xf79b, fontFamily: _fontFamily);
  static const IconData currency_eth_bold = IconData(0xf79c, fontFamily: _fontFamily);
  static const IconData currency_eur_bold = IconData(0xf79d, fontFamily: _fontFamily);
  static const IconData currency_gbp_bold = IconData(0xf79e, fontFamily: _fontFamily);
  static const IconData currency_inr_bold = IconData(0xf79f, fontFamily: _fontFamily);
  static const IconData currency_jpy_bold = IconData(0xf7a0, fontFamily: _fontFamily);
  static const IconData currency_krw_bold = IconData(0xf7a1, fontFamily: _fontFamily);
  static const IconData currency_kzt_bold = IconData(0xf7a2, fontFamily: _fontFamily);
  static const IconData currency_ngn_bold = IconData(0xf7a3, fontFamily: _fontFamily);
  static const IconData currency_rub_bold = IconData(0xf7a4, fontFamily: _fontFamily);
  static const IconData cursor_bold = IconData(0xf7a5, fontFamily: _fontFamily);
  static const IconData cursor_text_bold = IconData(0xf7a6, fontFamily: _fontFamily);
  static const IconData cylinder_bold = IconData(0xf7a7, fontFamily: _fontFamily);
  static const IconData database_bold = IconData(0xf7a8, fontFamily: _fontFamily);
  static const IconData desktop_bold = IconData(0xf7a9, fontFamily: _fontFamily);
  static const IconData desktop_tower_bold = IconData(0xf7aa, fontFamily: _fontFamily);
  static const IconData detective_bold = IconData(0xf7ab, fontFamily: _fontFamily);
  static const IconData device_mobile_bold = IconData(0xf7ac, fontFamily: _fontFamily);
  static const IconData device_mobile_camera_bold = IconData(0xf7ad, fontFamily: _fontFamily);
  static const IconData device_mobile_speaker_bold = IconData(0xf7ae, fontFamily: _fontFamily);
  static const IconData device_tablet_bold = IconData(0xf7af, fontFamily: _fontFamily);
  static const IconData device_tablet_camera_bold = IconData(0xf7b0, fontFamily: _fontFamily);
  static const IconData device_tablet_speaker_bold = IconData(0xf7b1, fontFamily: _fontFamily);
  static const IconData diamond_bold = IconData(0xf7b2, fontFamily: _fontFamily);
  static const IconData diamonds_four_bold = IconData(0xf7b3, fontFamily: _fontFamily);
  static const IconData dice_five_bold = IconData(0xf7b4, fontFamily: _fontFamily);
  static const IconData dice_four_bold = IconData(0xf7b5, fontFamily: _fontFamily);
  static const IconData dice_one_bold = IconData(0xf7b6, fontFamily: _fontFamily);
  static const IconData dice_six_bold = IconData(0xf7b7, fontFamily: _fontFamily);
  static const IconData dice_three_bold = IconData(0xf7b8, fontFamily: _fontFamily);
  static const IconData dice_two_bold = IconData(0xf7b9, fontFamily: _fontFamily);
  static const IconData disc_bold = IconData(0xf7ba, fontFamily: _fontFamily);
  static const IconData discord_logo_bold = IconData(0xf7bb, fontFamily: _fontFamily);
  static const IconData divide_bold = IconData(0xf7bc, fontFamily: _fontFamily);
  static const IconData dog_bold = IconData(0xf7bd, fontFamily: _fontFamily);
  static const IconData door_bold = IconData(0xf7be, fontFamily: _fontFamily);
  static const IconData dots_nine_bold = IconData(0xf7bf, fontFamily: _fontFamily);
  static const IconData dots_six_bold = IconData(0xf7c0, fontFamily: _fontFamily);
  static const IconData dots_six_vertical_bold = IconData(0xf7c1, fontFamily: _fontFamily);
  static const IconData dots_three_bold = IconData(0xf7c2, fontFamily: _fontFamily);
  static const IconData dots_three_circle_bold = IconData(0xf7c3, fontFamily: _fontFamily);
  static const IconData dots_three_circle_vertical_bold = IconData(0xf7c4, fontFamily: _fontFamily);
  static const IconData dots_three_outline_bold = IconData(0xf7c5, fontFamily: _fontFamily);
  static const IconData dots_three_outline_vertical_bold = IconData(0xf7c6, fontFamily: _fontFamily);
  static const IconData dots_three_vertical_bold = IconData(0xf7c7, fontFamily: _fontFamily);
  static const IconData download_bold = IconData(0xf7c8, fontFamily: _fontFamily);
  static const IconData download_simple_bold = IconData(0xf7c9, fontFamily: _fontFamily);
  static const IconData dribbble_logo_bold = IconData(0xf7ca, fontFamily: _fontFamily);
  static const IconData drop_bold = IconData(0xf7cb, fontFamily: _fontFamily);
  static const IconData drop_half_bold = IconData(0xf7cc, fontFamily: _fontFamily);
  static const IconData drop_half_bottom_bold = IconData(0xf7cd, fontFamily: _fontFamily);
  static const IconData ear_bold = IconData(0xf7ce, fontFamily: _fontFamily);
  static const IconData ear_slash_bold = IconData(0xf7cf, fontFamily: _fontFamily);
  static const IconData egg_bold = IconData(0xf7d0, fontFamily: _fontFamily);
  static const IconData egg_crack_bold = IconData(0xf7d1, fontFamily: _fontFamily);
  static const IconData eject_bold = IconData(0xf7d2, fontFamily: _fontFamily);
  static const IconData eject_simple_bold = IconData(0xf7d3, fontFamily: _fontFamily);
  static const IconData envelope_bold = IconData(0xf7d4, fontFamily: _fontFamily);
  static const IconData envelope_open_bold = IconData(0xf7d5, fontFamily: _fontFamily);
  static const IconData envelope_simple_bold = IconData(0xf7d6, fontFamily: _fontFamily);
  static const IconData envelope_simple_open_bold = IconData(0xf7d7, fontFamily: _fontFamily);
  static const IconData equalizer_bold = IconData(0xf7d8, fontFamily: _fontFamily);
  static const IconData equals_bold = IconData(0xf7d9, fontFamily: _fontFamily);
  static const IconData eraser_bold = IconData(0xf7da, fontFamily: _fontFamily);
  static const IconData exam_bold = IconData(0xf7db, fontFamily: _fontFamily);
  static const IconData export_bold = IconData(0xf7dc, fontFamily: _fontFamily);
  static const IconData eye_bold = IconData(0xf7dd, fontFamily: _fontFamily);
  static const IconData eye_closed_bold = IconData(0xf7de, fontFamily: _fontFamily);
  static const IconData eye_slash_bold = IconData(0xf7df, fontFamily: _fontFamily);
  static const IconData eyedropper_bold = IconData(0xf7e0, fontFamily: _fontFamily);
  static const IconData eyedropper_sample_bold = IconData(0xf7e1, fontFamily: _fontFamily);
  static const IconData eyeglasses_bold = IconData(0xf7e2, fontFamily: _fontFamily);
  static const IconData face_mask_bold = IconData(0xf7e3, fontFamily: _fontFamily);
  static const IconData facebook_logo_bold = IconData(0xf7e4, fontFamily: _fontFamily);
  static const IconData factory_bold = IconData(0xf7e5, fontFamily: _fontFamily);
  static const IconData faders_bold = IconData(0xf7e6, fontFamily: _fontFamily);
  static const IconData faders_horizontal_bold = IconData(0xf7e7, fontFamily: _fontFamily);
  static const IconData fast_forward_bold = IconData(0xf7e8, fontFamily: _fontFamily);
  static const IconData fast_forward_circle_bold = IconData(0xf7e9, fontFamily: _fontFamily);
  static const IconData figma_logo_bold = IconData(0xf7ea, fontFamily: _fontFamily);
  static const IconData file_arrow_down_bold = IconData(0xf7eb, fontFamily: _fontFamily);
  static const IconData file_arrow_up_bold = IconData(0xf7ec, fontFamily: _fontFamily);
  static const IconData file_audio_bold = IconData(0xf7ed, fontFamily: _fontFamily);
  static const IconData file_bold = IconData(0xf7ee, fontFamily: _fontFamily);
  static const IconData file_cloud_bold = IconData(0xf7ef, fontFamily: _fontFamily);
  static const IconData file_code_bold = IconData(0xf7f0, fontFamily: _fontFamily);
  static const IconData file_css_bold = IconData(0xf7f1, fontFamily: _fontFamily);
  static const IconData file_csv_bold = IconData(0xf7f2, fontFamily: _fontFamily);
  static const IconData file_doc_bold = IconData(0xf7f3, fontFamily: _fontFamily);
  static const IconData file_dotted_bold = IconData(0xf7f4, fontFamily: _fontFamily);
  static const IconData file_html_bold = IconData(0xf7f5, fontFamily: _fontFamily);
  static const IconData file_image_bold = IconData(0xf7f6, fontFamily: _fontFamily);
  static const IconData file_jpg_bold = IconData(0xf7f7, fontFamily: _fontFamily);
  static const IconData file_js_bold = IconData(0xf7f8, fontFamily: _fontFamily);
  static const IconData file_jsx_bold = IconData(0xf7f9, fontFamily: _fontFamily);
  static const IconData file_lock_bold = IconData(0xf7fa, fontFamily: _fontFamily);
  static const IconData file_minus_bold = IconData(0xf7fb, fontFamily: _fontFamily);
  static const IconData file_pdf_bold = IconData(0xf7fc, fontFamily: _fontFamily);
  static const IconData file_plus_bold = IconData(0xf7fd, fontFamily: _fontFamily);
  static const IconData file_png_bold = IconData(0xf7fe, fontFamily: _fontFamily);
  static const IconData file_ppt_bold = IconData(0xf7ff, fontFamily: _fontFamily);
  static const IconData file_rs_bold = IconData(0xf800, fontFamily: _fontFamily);
  static const IconData file_search_bold = IconData(0xf801, fontFamily: _fontFamily);
  static const IconData file_text_bold = IconData(0xf802, fontFamily: _fontFamily);
  static const IconData file_ts_bold = IconData(0xf803, fontFamily: _fontFamily);
  static const IconData file_tsx_bold = IconData(0xf804, fontFamily: _fontFamily);
  static const IconData file_video_bold = IconData(0xf805, fontFamily: _fontFamily);
  static const IconData file_vue_bold = IconData(0xf806, fontFamily: _fontFamily);
  static const IconData file_x_bold = IconData(0xf807, fontFamily: _fontFamily);
  static const IconData file_xls_bold = IconData(0xf808, fontFamily: _fontFamily);
  static const IconData file_zip_bold = IconData(0xf809, fontFamily: _fontFamily);
  static const IconData files_bold = IconData(0xf80a, fontFamily: _fontFamily);
  static const IconData film_script_bold = IconData(0xf80b, fontFamily: _fontFamily);
  static const IconData film_slate_bold = IconData(0xf80c, fontFamily: _fontFamily);
  static const IconData film_strip_bold = IconData(0xf80d, fontFamily: _fontFamily);
  static const IconData fingerprint_bold = IconData(0xf80e, fontFamily: _fontFamily);
  static const IconData fingerprint_simple_bold = IconData(0xf80f, fontFamily: _fontFamily);
  static const IconData finn_the_human_bold = IconData(0xf810, fontFamily: _fontFamily);
  static const IconData fire_bold = IconData(0xf811, fontFamily: _fontFamily);
  static const IconData fire_simple_bold = IconData(0xf812, fontFamily: _fontFamily);
  static const IconData first_aid_bold = IconData(0xf813, fontFamily: _fontFamily);
  static const IconData first_aid_kit_bold = IconData(0xf814, fontFamily: _fontFamily);
  static const IconData fish_bold = IconData(0xf815, fontFamily: _fontFamily);
  static const IconData fish_simple_bold = IconData(0xf816, fontFamily: _fontFamily);
  static const IconData flag_banner_bold = IconData(0xf817, fontFamily: _fontFamily);
  static const IconData flag_bold = IconData(0xf818, fontFamily: _fontFamily);
  static const IconData flag_checkered_bold = IconData(0xf819, fontFamily: _fontFamily);
  static const IconData flame_bold = IconData(0xf81a, fontFamily: _fontFamily);
  static const IconData flashlight_bold = IconData(0xf81b, fontFamily: _fontFamily);
  static const IconData flask_bold = IconData(0xf81c, fontFamily: _fontFamily);
  static const IconData floppy_disk_back_bold = IconData(0xf81d, fontFamily: _fontFamily);
  static const IconData floppy_disk_bold = IconData(0xf81e, fontFamily: _fontFamily);
  static const IconData flow_arrow_bold = IconData(0xf81f, fontFamily: _fontFamily);
  static const IconData flower_bold = IconData(0xf820, fontFamily: _fontFamily);
  static const IconData flower_lotus_bold = IconData(0xf821, fontFamily: _fontFamily);
  static const IconData flying_saucer_bold = IconData(0xf822, fontFamily: _fontFamily);
  static const IconData folder_bold = IconData(0xf823, fontFamily: _fontFamily);
  static const IconData folder_dotted_bold = IconData(0xf824, fontFamily: _fontFamily);
  static const IconData folder_lock_bold = IconData(0xf825, fontFamily: _fontFamily);
  static const IconData folder_minus_bold = IconData(0xf826, fontFamily: _fontFamily);
  static const IconData folder_notch_bold = IconData(0xf827, fontFamily: _fontFamily);
  static const IconData folder_notch_minus_bold = IconData(0xf828, fontFamily: _fontFamily);
  static const IconData folder_notch_open_bold = IconData(0xf829, fontFamily: _fontFamily);
  static const IconData folder_notch_plus_bold = IconData(0xf82a, fontFamily: _fontFamily);
  static const IconData folder_open_bold = IconData(0xf82b, fontFamily: _fontFamily);
  static const IconData folder_plus_bold = IconData(0xf82c, fontFamily: _fontFamily);
  static const IconData folder_simple_bold = IconData(0xf82d, fontFamily: _fontFamily);
  static const IconData folder_simple_dotted_bold = IconData(0xf82e, fontFamily: _fontFamily);
  static const IconData folder_simple_lock_bold = IconData(0xf82f, fontFamily: _fontFamily);
  static const IconData folder_simple_minus_bold = IconData(0xf830, fontFamily: _fontFamily);
  static const IconData folder_simple_plus_bold = IconData(0xf831, fontFamily: _fontFamily);
  static const IconData folder_simple_star_bold = IconData(0xf832, fontFamily: _fontFamily);
  static const IconData folder_simple_user_bold = IconData(0xf833, fontFamily: _fontFamily);
  static const IconData folder_star_bold = IconData(0xf834, fontFamily: _fontFamily);
  static const IconData folder_user_bold = IconData(0xf835, fontFamily: _fontFamily);
  static const IconData folders_bold = IconData(0xf836, fontFamily: _fontFamily);
  static const IconData football_bold = IconData(0xf837, fontFamily: _fontFamily);
  static const IconData fork_knife_bold = IconData(0xf838, fontFamily: _fontFamily);
  static const IconData frame_corners_bold = IconData(0xf839, fontFamily: _fontFamily);
  static const IconData framer_logo_bold = IconData(0xf83a, fontFamily: _fontFamily);
  static const IconData function_bold = IconData(0xf83b, fontFamily: _fontFamily);
  static const IconData funnel_bold = IconData(0xf83c, fontFamily: _fontFamily);
  static const IconData funnel_simple_bold = IconData(0xf83d, fontFamily: _fontFamily);
  static const IconData game_controller_bold = IconData(0xf83e, fontFamily: _fontFamily);
  static const IconData gas_pump_bold = IconData(0xf83f, fontFamily: _fontFamily);
  static const IconData gauge_bold = IconData(0xf840, fontFamily: _fontFamily);
  static const IconData gear_bold = IconData(0xf841, fontFamily: _fontFamily);
  static const IconData gear_six_bold = IconData(0xf842, fontFamily: _fontFamily);
  static const IconData gender_female_bold = IconData(0xf843, fontFamily: _fontFamily);
  static const IconData gender_intersex_bold = IconData(0xf844, fontFamily: _fontFamily);
  static const IconData gender_male_bold = IconData(0xf845, fontFamily: _fontFamily);
  static const IconData gender_neuter_bold = IconData(0xf846, fontFamily: _fontFamily);
  static const IconData gender_nonbinary_bold = IconData(0xf847, fontFamily: _fontFamily);
  static const IconData gender_transgender_bold = IconData(0xf848, fontFamily: _fontFamily);
  static const IconData ghost_bold = IconData(0xf849, fontFamily: _fontFamily);
  static const IconData gif_bold = IconData(0xf84a, fontFamily: _fontFamily);
  static const IconData gift_bold = IconData(0xf84b, fontFamily: _fontFamily);
  static const IconData git_branch_bold = IconData(0xf84c, fontFamily: _fontFamily);
  static const IconData git_commit_bold = IconData(0xf84d, fontFamily: _fontFamily);
  static const IconData git_diff_bold = IconData(0xf84e, fontFamily: _fontFamily);
  static const IconData git_fork_bold = IconData(0xf84f, fontFamily: _fontFamily);
  static const IconData git_merge_bold = IconData(0xf850, fontFamily: _fontFamily);
  static const IconData git_pull_request_bold = IconData(0xf851, fontFamily: _fontFamily);
  static const IconData github_logo_bold = IconData(0xf852, fontFamily: _fontFamily);
  static const IconData gitlab_logo_bold = IconData(0xf853, fontFamily: _fontFamily);
  static const IconData gitlab_logo_simple_bold = IconData(0xf854, fontFamily: _fontFamily);
  static const IconData globe_bold = IconData(0xf855, fontFamily: _fontFamily);
  static const IconData globe_hemisphere_east_bold = IconData(0xf856, fontFamily: _fontFamily);
  static const IconData globe_hemisphere_west_bold = IconData(0xf857, fontFamily: _fontFamily);
  static const IconData globe_simple_bold = IconData(0xf858, fontFamily: _fontFamily);
  static const IconData globe_stand_bold = IconData(0xf859, fontFamily: _fontFamily);
  static const IconData google_chrome_logo_bold = IconData(0xf85a, fontFamily: _fontFamily);
  static const IconData google_logo_bold = IconData(0xf85b, fontFamily: _fontFamily);
  static const IconData google_photos_logo_bold = IconData(0xf85c, fontFamily: _fontFamily);
  static const IconData google_play_logo_bold = IconData(0xf85d, fontFamily: _fontFamily);
  static const IconData google_podcasts_logo_bold = IconData(0xf85e, fontFamily: _fontFamily);
  static const IconData gradient_bold = IconData(0xf85f, fontFamily: _fontFamily);
  static const IconData graduation_cap_bold = IconData(0xf860, fontFamily: _fontFamily);
  static const IconData graph_bold = IconData(0xf861, fontFamily: _fontFamily);
  static const IconData grid_four_bold = IconData(0xf862, fontFamily: _fontFamily);
  static const IconData hamburger_bold = IconData(0xf863, fontFamily: _fontFamily);
  static const IconData hand_bold = IconData(0xf864, fontFamily: _fontFamily);
  static const IconData hand_eye_bold = IconData(0xf865, fontFamily: _fontFamily);
  static const IconData hand_fist_bold = IconData(0xf866, fontFamily: _fontFamily);
  static const IconData hand_grabbing_bold = IconData(0xf867, fontFamily: _fontFamily);
  static const IconData hand_palm_bold = IconData(0xf868, fontFamily: _fontFamily);
  static const IconData hand_pointing_bold = IconData(0xf869, fontFamily: _fontFamily);
  static const IconData hand_soap_bold = IconData(0xf86a, fontFamily: _fontFamily);
  static const IconData hand_waving_bold = IconData(0xf86b, fontFamily: _fontFamily);
  static const IconData handbag_bold = IconData(0xf86c, fontFamily: _fontFamily);
  static const IconData handbag_simple_bold = IconData(0xf86d, fontFamily: _fontFamily);
  static const IconData hands_clapping_bold = IconData(0xf86e, fontFamily: _fontFamily);
  static const IconData handshake_bold = IconData(0xf86f, fontFamily: _fontFamily);
  static const IconData hard_drive_bold = IconData(0xf870, fontFamily: _fontFamily);
  static const IconData hard_drives_bold = IconData(0xf871, fontFamily: _fontFamily);
  static const IconData hash_bold = IconData(0xf872, fontFamily: _fontFamily);
  static const IconData hash_straight_bold = IconData(0xf873, fontFamily: _fontFamily);
  static const IconData headlights_bold = IconData(0xf874, fontFamily: _fontFamily);
  static const IconData headphones_bold = IconData(0xf875, fontFamily: _fontFamily);
  static const IconData headset_bold = IconData(0xf876, fontFamily: _fontFamily);
  static const IconData heart_bold = IconData(0xf877, fontFamily: _fontFamily);
  static const IconData heart_break_bold = IconData(0xf878, fontFamily: _fontFamily);
  static const IconData heart_straight_bold = IconData(0xf879, fontFamily: _fontFamily);
  static const IconData heart_straight_break_bold = IconData(0xf87a, fontFamily: _fontFamily);
  static const IconData heartbeat_bold = IconData(0xf87b, fontFamily: _fontFamily);
  static const IconData hexagon_bold = IconData(0xf87c, fontFamily: _fontFamily);
  static const IconData highlighter_circle_bold = IconData(0xf87d, fontFamily: _fontFamily);
  static const IconData horse_bold = IconData(0xf87e, fontFamily: _fontFamily);
  static const IconData hourglass_bold = IconData(0xf87f, fontFamily: _fontFamily);
  static const IconData hourglass_high_bold = IconData(0xf880, fontFamily: _fontFamily);
  static const IconData hourglass_low_bold = IconData(0xf881, fontFamily: _fontFamily);
  static const IconData hourglass_medium_bold = IconData(0xf882, fontFamily: _fontFamily);
  static const IconData hourglass_simple_bold = IconData(0xf883, fontFamily: _fontFamily);
  static const IconData hourglass_simple_high_bold = IconData(0xf884, fontFamily: _fontFamily);
  static const IconData hourglass_simple_low_bold = IconData(0xf885, fontFamily: _fontFamily);
  static const IconData hourglass_simple_medium_bold = IconData(0xf886, fontFamily: _fontFamily);
  static const IconData house_bold = IconData(0xf887, fontFamily: _fontFamily);
  static const IconData house_line_bold = IconData(0xf888, fontFamily: _fontFamily);
  static const IconData house_simple_bold = IconData(0xf889, fontFamily: _fontFamily);
  static const IconData identification_badge_bold = IconData(0xf88a, fontFamily: _fontFamily);
  static const IconData identification_card_bold = IconData(0xf88b, fontFamily: _fontFamily);
  static const IconData image_bold = IconData(0xf88c, fontFamily: _fontFamily);
  static const IconData image_square_bold = IconData(0xf88d, fontFamily: _fontFamily);
  static const IconData infinity_bold = IconData(0xf88e, fontFamily: _fontFamily);
  static const IconData info_bold = IconData(0xf88f, fontFamily: _fontFamily);
  static const IconData instagram_logo_bold = IconData(0xf890, fontFamily: _fontFamily);
  static const IconData intersect_bold = IconData(0xf891, fontFamily: _fontFamily);
  static const IconData jeep_bold = IconData(0xf892, fontFamily: _fontFamily);
  static const IconData kanban_bold = IconData(0xf893, fontFamily: _fontFamily);
  static const IconData key_bold = IconData(0xf894, fontFamily: _fontFamily);
  static const IconData key_return_bold = IconData(0xf895, fontFamily: _fontFamily);
  static const IconData keyboard_bold = IconData(0xf896, fontFamily: _fontFamily);
  static const IconData keyhole_bold = IconData(0xf897, fontFamily: _fontFamily);
  static const IconData knife_bold = IconData(0xf898, fontFamily: _fontFamily);
  static const IconData ladder_bold = IconData(0xf899, fontFamily: _fontFamily);
  static const IconData ladder_simple_bold = IconData(0xf89a, fontFamily: _fontFamily);
  static const IconData lamp_bold = IconData(0xf89b, fontFamily: _fontFamily);
  static const IconData laptop_bold = IconData(0xf89c, fontFamily: _fontFamily);
  static const IconData layout_bold = IconData(0xf89d, fontFamily: _fontFamily);
  static const IconData leaf_bold = IconData(0xf89e, fontFamily: _fontFamily);
  static const IconData lifebuoy_bold = IconData(0xf89f, fontFamily: _fontFamily);
  static const IconData lightbulb_bold = IconData(0xf8a0, fontFamily: _fontFamily);
  static const IconData lightbulb_filament_bold = IconData(0xf8a1, fontFamily: _fontFamily);
  static const IconData lightning_bold = IconData(0xf8a2, fontFamily: _fontFamily);
  static const IconData lightning_slash_bold = IconData(0xf8a3, fontFamily: _fontFamily);
  static const IconData line_segment_bold = IconData(0xf8a4, fontFamily: _fontFamily);
  static const IconData line_segments_bold = IconData(0xf8a5, fontFamily: _fontFamily);
  static const IconData link_bold = IconData(0xf8a6, fontFamily: _fontFamily);
  static const IconData link_break_bold = IconData(0xf8a7, fontFamily: _fontFamily);
  static const IconData link_simple_bold = IconData(0xf8a8, fontFamily: _fontFamily);
  static const IconData link_simple_break_bold = IconData(0xf8a9, fontFamily: _fontFamily);
  static const IconData link_simple_horizontal_bold = IconData(0xf8aa, fontFamily: _fontFamily);
  static const IconData link_simple_horizontal_break_bold = IconData(0xf8ab, fontFamily: _fontFamily);
  static const IconData linkedin_logo_bold = IconData(0xf8ac, fontFamily: _fontFamily);
  static const IconData linux_logo_bold = IconData(0xf8ad, fontFamily: _fontFamily);
  static const IconData list_bold = IconData(0xf8ae, fontFamily: _fontFamily);
  static const IconData list_bullets_bold = IconData(0xf8af, fontFamily: _fontFamily);
  static const IconData list_checks_bold = IconData(0xf8b0, fontFamily: _fontFamily);
  static const IconData list_dashes_bold = IconData(0xf8b1, fontFamily: _fontFamily);
  static const IconData list_numbers_bold = IconData(0xf8b2, fontFamily: _fontFamily);
  static const IconData list_plus_bold = IconData(0xf8b3, fontFamily: _fontFamily);
  static const IconData lock_bold = IconData(0xf8b4, fontFamily: _fontFamily);
  static const IconData lock_key_bold = IconData(0xf8b5, fontFamily: _fontFamily);
  static const IconData lock_key_open_bold = IconData(0xf8b6, fontFamily: _fontFamily);
  static const IconData lock_laminated_bold = IconData(0xf8b7, fontFamily: _fontFamily);
  static const IconData lock_laminated_open_bold = IconData(0xf8b8, fontFamily: _fontFamily);
  static const IconData lock_open_bold = IconData(0xf8b9, fontFamily: _fontFamily);
  static const IconData lock_simple_bold = IconData(0xf8ba, fontFamily: _fontFamily);
  static const IconData lock_simple_open_bold = IconData(0xf8bb, fontFamily: _fontFamily);
  static const IconData magic_wand_bold = IconData(0xf8bc, fontFamily: _fontFamily);
  static const IconData magnet_bold = IconData(0xf8bd, fontFamily: _fontFamily);
  static const IconData magnet_straight_bold = IconData(0xf8be, fontFamily: _fontFamily);
  static const IconData magnifying_glass_bold = IconData(0xf8bf, fontFamily: _fontFamily);
  static const IconData magnifying_glass_minus_bold = IconData(0xf8c0, fontFamily: _fontFamily);
  static const IconData magnifying_glass_plus_bold = IconData(0xf8c1, fontFamily: _fontFamily);
  static const IconData map_pin_bold = IconData(0xf8c2, fontFamily: _fontFamily);
  static const IconData map_pin_line_bold = IconData(0xf8c3, fontFamily: _fontFamily);
  static const IconData map_trifold_bold = IconData(0xf8c4, fontFamily: _fontFamily);
  static const IconData marker_circle_bold = IconData(0xf8c5, fontFamily: _fontFamily);
  static const IconData martini_bold = IconData(0xf8c6, fontFamily: _fontFamily);
  static const IconData mask_happy_bold = IconData(0xf8c7, fontFamily: _fontFamily);
  static const IconData mask_sad_bold = IconData(0xf8c8, fontFamily: _fontFamily);
  static const IconData math_operations_bold = IconData(0xf8c9, fontFamily: _fontFamily);
  static const IconData medal_bold = IconData(0xf8ca, fontFamily: _fontFamily);
  static const IconData medium_logo_bold = IconData(0xf8cb, fontFamily: _fontFamily);
  static const IconData megaphone_bold = IconData(0xf8cc, fontFamily: _fontFamily);
  static const IconData megaphone_simple_bold = IconData(0xf8cd, fontFamily: _fontFamily);
  static const IconData messenger_logo_bold = IconData(0xf8ce, fontFamily: _fontFamily);
  static const IconData microphone_bold = IconData(0xf8cf, fontFamily: _fontFamily);
  static const IconData microphone_slash_bold = IconData(0xf8d0, fontFamily: _fontFamily);
  static const IconData microphone_stage_bold = IconData(0xf8d1, fontFamily: _fontFamily);
  static const IconData microsoft_excel_logo_bold = IconData(0xf8d2, fontFamily: _fontFamily);
  static const IconData microsoft_powerpoint_logo_bold = IconData(0xf8d3, fontFamily: _fontFamily);
  static const IconData microsoft_teams_logo_bold = IconData(0xf8d4, fontFamily: _fontFamily);
  static const IconData microsoft_word_logo_bold = IconData(0xf8d5, fontFamily: _fontFamily);
  static const IconData minus_bold = IconData(0xf8d6, fontFamily: _fontFamily);
  static const IconData minus_circle_bold = IconData(0xf8d7, fontFamily: _fontFamily);
  static const IconData money_bold = IconData(0xf8d8, fontFamily: _fontFamily);
  static const IconData monitor_bold = IconData(0xf8d9, fontFamily: _fontFamily);
  static const IconData monitor_play_bold = IconData(0xf8da, fontFamily: _fontFamily);
  static const IconData moon_bold = IconData(0xf8db, fontFamily: _fontFamily);
  static const IconData moon_stars_bold = IconData(0xf8dc, fontFamily: _fontFamily);
  static const IconData mountains_bold = IconData(0xf8dd, fontFamily: _fontFamily);
  static const IconData mouse_bold = IconData(0xf8de, fontFamily: _fontFamily);
  static const IconData mouse_simple_bold = IconData(0xf8df, fontFamily: _fontFamily);
  static const IconData music_note_bold = IconData(0xf8e0, fontFamily: _fontFamily);
  static const IconData music_note_simple_bold = IconData(0xf8e1, fontFamily: _fontFamily);
  static const IconData music_notes_bold = IconData(0xf8e2, fontFamily: _fontFamily);
  static const IconData music_notes_plus_bold = IconData(0xf8e3, fontFamily: _fontFamily);
  static const IconData music_notes_simple_bold = IconData(0xf8e4, fontFamily: _fontFamily);
  static const IconData navigation_arrow_bold = IconData(0xf8e5, fontFamily: _fontFamily);
  static const IconData needle_bold = IconData(0xf8e6, fontFamily: _fontFamily);
  static const IconData newspaper_bold = IconData(0xf8e7, fontFamily: _fontFamily);
  static const IconData newspaper_clipping_bold = IconData(0xf8e8, fontFamily: _fontFamily);
  static const IconData note_blank_bold = IconData(0xf8e9, fontFamily: _fontFamily);
  static const IconData note_bold = IconData(0xf8ea, fontFamily: _fontFamily);
  static const IconData note_pencil_bold = IconData(0xf8eb, fontFamily: _fontFamily);
  static const IconData notebook_bold = IconData(0xf8ec, fontFamily: _fontFamily);
  static const IconData notepad_bold = IconData(0xf8ed, fontFamily: _fontFamily);
  static const IconData notification_bold = IconData(0xf8ee, fontFamily: _fontFamily);
  static const IconData number_circle_eight_bold = IconData(0xf8ef, fontFamily: _fontFamily);
  static const IconData number_circle_five_bold = IconData(0xf8f0, fontFamily: _fontFamily);
  static const IconData number_circle_four_bold = IconData(0xf8f1, fontFamily: _fontFamily);
  static const IconData number_circle_nine_bold = IconData(0xf8f2, fontFamily: _fontFamily);
  static const IconData number_circle_one_bold = IconData(0xf8f3, fontFamily: _fontFamily);
  static const IconData number_circle_seven_bold = IconData(0xf8f4, fontFamily: _fontFamily);
  static const IconData number_circle_six_bold = IconData(0xf8f5, fontFamily: _fontFamily);
  static const IconData number_circle_three_bold = IconData(0xf8f6, fontFamily: _fontFamily);
  static const IconData number_circle_two_bold = IconData(0xf8f7, fontFamily: _fontFamily);
  static const IconData number_circle_zero_bold = IconData(0xf8f8, fontFamily: _fontFamily);
  static const IconData number_eight_bold = IconData(0xf8f9, fontFamily: _fontFamily);
  static const IconData number_five_bold = IconData(0xf8fa, fontFamily: _fontFamily);
  static const IconData number_four_bold = IconData(0xf8fb, fontFamily: _fontFamily);
  static const IconData number_nine_bold = IconData(0xf8fc, fontFamily: _fontFamily);
  static const IconData number_one_bold = IconData(0xf8fd, fontFamily: _fontFamily);
  static const IconData number_seven_bold = IconData(0xf8fe, fontFamily: _fontFamily);
  static const IconData number_six_bold = IconData(0xf8ff, fontFamily: _fontFamily);
  static const IconData number_square_eight_bold = IconData(0xf900, fontFamily: _fontFamily);
  static const IconData number_square_five_bold = IconData(0xf901, fontFamily: _fontFamily);
  static const IconData number_square_four_bold = IconData(0xf902, fontFamily: _fontFamily);
  static const IconData number_square_nine_bold = IconData(0xf903, fontFamily: _fontFamily);
  static const IconData number_square_one_bold = IconData(0xf904, fontFamily: _fontFamily);
  static const IconData number_square_seven_bold = IconData(0xf905, fontFamily: _fontFamily);
  static const IconData number_square_six_bold = IconData(0xf906, fontFamily: _fontFamily);
  static const IconData number_square_three_bold = IconData(0xf907, fontFamily: _fontFamily);
  static const IconData number_square_two_bold = IconData(0xf908, fontFamily: _fontFamily);
  static const IconData number_square_zero_bold = IconData(0xf909, fontFamily: _fontFamily);
  static const IconData number_three_bold = IconData(0xf90a, fontFamily: _fontFamily);
  static const IconData number_two_bold = IconData(0xf90b, fontFamily: _fontFamily);
  static const IconData number_zero_bold = IconData(0xf90c, fontFamily: _fontFamily);
  static const IconData nut_bold = IconData(0xf90d, fontFamily: _fontFamily);
  static const IconData ny_times_logo_bold = IconData(0xf90e, fontFamily: _fontFamily);
  static const IconData octagon_bold = IconData(0xf90f, fontFamily: _fontFamily);
  static const IconData option_bold = IconData(0xf910, fontFamily: _fontFamily);
  static const IconData package_bold = IconData(0xf911, fontFamily: _fontFamily);
  static const IconData paint_brush_bold = IconData(0xf912, fontFamily: _fontFamily);
  static const IconData paint_brush_broad_bold = IconData(0xf913, fontFamily: _fontFamily);
  static const IconData paint_brush_household_bold = IconData(0xf914, fontFamily: _fontFamily);
  static const IconData paint_bucket_bold = IconData(0xf915, fontFamily: _fontFamily);
  static const IconData paint_roller_bold = IconData(0xf916, fontFamily: _fontFamily);
  static const IconData palette_bold = IconData(0xf917, fontFamily: _fontFamily);
  static const IconData paper_plane_bold = IconData(0xf918, fontFamily: _fontFamily);
  static const IconData paper_plane_right_bold = IconData(0xf919, fontFamily: _fontFamily);
  static const IconData paper_plane_tilt_bold = IconData(0xf91a, fontFamily: _fontFamily);
  static const IconData paperclip_bold = IconData(0xf91b, fontFamily: _fontFamily);
  static const IconData paperclip_horizontal_bold = IconData(0xf91c, fontFamily: _fontFamily);
  static const IconData parachute_bold = IconData(0xf91d, fontFamily: _fontFamily);
  static const IconData password_bold = IconData(0xf91e, fontFamily: _fontFamily);
  static const IconData path_bold = IconData(0xf91f, fontFamily: _fontFamily);
  static const IconData pause_bold = IconData(0xf920, fontFamily: _fontFamily);
  static const IconData pause_circle_bold = IconData(0xf921, fontFamily: _fontFamily);
  static const IconData paw_print_bold = IconData(0xf922, fontFamily: _fontFamily);
  static const IconData peace_bold = IconData(0xf923, fontFamily: _fontFamily);
  static const IconData pen_bold = IconData(0xf924, fontFamily: _fontFamily);
  static const IconData pen_nib_bold = IconData(0xf925, fontFamily: _fontFamily);
  static const IconData pen_nib_straight_bold = IconData(0xf926, fontFamily: _fontFamily);
  static const IconData pencil_bold = IconData(0xf927, fontFamily: _fontFamily);
  static const IconData pencil_circle_bold = IconData(0xf928, fontFamily: _fontFamily);
  static const IconData pencil_line_bold = IconData(0xf929, fontFamily: _fontFamily);
  static const IconData pencil_simple_bold = IconData(0xf92a, fontFamily: _fontFamily);
  static const IconData pencil_simple_line_bold = IconData(0xf92b, fontFamily: _fontFamily);
  static const IconData percent_bold = IconData(0xf92c, fontFamily: _fontFamily);
  static const IconData person_bold = IconData(0xf92d, fontFamily: _fontFamily);
  static const IconData person_simple_bold = IconData(0xf92e, fontFamily: _fontFamily);
  static const IconData person_simple_run_bold = IconData(0xf92f, fontFamily: _fontFamily);
  static const IconData person_simple_walk_bold = IconData(0xf930, fontFamily: _fontFamily);
  static const IconData perspective_bold = IconData(0xf931, fontFamily: _fontFamily);
  static const IconData phone_bold = IconData(0xf932, fontFamily: _fontFamily);
  static const IconData phone_call_bold = IconData(0xf933, fontFamily: _fontFamily);
  static const IconData phone_disconnect_bold = IconData(0xf934, fontFamily: _fontFamily);
  static const IconData phone_incoming_bold = IconData(0xf935, fontFamily: _fontFamily);
  static const IconData phone_outgoing_bold = IconData(0xf936, fontFamily: _fontFamily);
  static const IconData phone_slash_bold = IconData(0xf937, fontFamily: _fontFamily);
  static const IconData phone_x_bold = IconData(0xf938, fontFamily: _fontFamily);
  static const IconData phosphor_logo_bold = IconData(0xf939, fontFamily: _fontFamily);
  static const IconData piano_keys_bold = IconData(0xf93a, fontFamily: _fontFamily);
  static const IconData picture_in_picture_bold = IconData(0xf93b, fontFamily: _fontFamily);
  static const IconData pill_bold = IconData(0xf93c, fontFamily: _fontFamily);
  static const IconData pinterest_logo_bold = IconData(0xf93d, fontFamily: _fontFamily);
  static const IconData pinwheel_bold = IconData(0xf93e, fontFamily: _fontFamily);
  static const IconData pizza_bold = IconData(0xf93f, fontFamily: _fontFamily);
  static const IconData placeholder_bold = IconData(0xf940, fontFamily: _fontFamily);
  static const IconData planet_bold = IconData(0xf941, fontFamily: _fontFamily);
  static const IconData play_bold = IconData(0xf942, fontFamily: _fontFamily);
  static const IconData play_circle_bold = IconData(0xf943, fontFamily: _fontFamily);
  static const IconData playlist_bold = IconData(0xf944, fontFamily: _fontFamily);
  static const IconData plug_bold = IconData(0xf945, fontFamily: _fontFamily);
  static const IconData plugs_bold = IconData(0xf946, fontFamily: _fontFamily);
  static const IconData plugs_connected_bold = IconData(0xf947, fontFamily: _fontFamily);
  static const IconData plus_bold = IconData(0xf948, fontFamily: _fontFamily);
  static const IconData plus_circle_bold = IconData(0xf949, fontFamily: _fontFamily);
  static const IconData plus_minus_bold = IconData(0xf94a, fontFamily: _fontFamily);
  static const IconData poker_chip_bold = IconData(0xf94b, fontFamily: _fontFamily);
  static const IconData police_car_bold = IconData(0xf94c, fontFamily: _fontFamily);
  static const IconData polygon_bold = IconData(0xf94d, fontFamily: _fontFamily);
  static const IconData popcorn_bold = IconData(0xf94e, fontFamily: _fontFamily);
  static const IconData power_bold = IconData(0xf94f, fontFamily: _fontFamily);
  static const IconData prescription_bold = IconData(0xf950, fontFamily: _fontFamily);
  static const IconData presentation_bold = IconData(0xf951, fontFamily: _fontFamily);
  static const IconData presentation_chart_bold = IconData(0xf952, fontFamily: _fontFamily);
  static const IconData printer_bold = IconData(0xf953, fontFamily: _fontFamily);
  static const IconData prohibit_bold = IconData(0xf954, fontFamily: _fontFamily);
  static const IconData prohibit_inset_bold = IconData(0xf955, fontFamily: _fontFamily);
  static const IconData projector_screen_bold = IconData(0xf956, fontFamily: _fontFamily);
  static const IconData projector_screen_chart_bold = IconData(0xf957, fontFamily: _fontFamily);
  static const IconData push_pin_bold = IconData(0xf958, fontFamily: _fontFamily);
  static const IconData push_pin_simple_bold = IconData(0xf959, fontFamily: _fontFamily);
  static const IconData push_pin_simple_slash_bold = IconData(0xf95a, fontFamily: _fontFamily);
  static const IconData push_pin_slash_bold = IconData(0xf95b, fontFamily: _fontFamily);
  static const IconData puzzle_piece_bold = IconData(0xf95c, fontFamily: _fontFamily);
  static const IconData qr_code_bold = IconData(0xf95d, fontFamily: _fontFamily);
  static const IconData question_bold = IconData(0xf95e, fontFamily: _fontFamily);
  static const IconData queue_bold = IconData(0xf95f, fontFamily: _fontFamily);
  static const IconData quotes_bold = IconData(0xf960, fontFamily: _fontFamily);
  static const IconData radical_bold = IconData(0xf961, fontFamily: _fontFamily);
  static const IconData radio_bold = IconData(0xf962, fontFamily: _fontFamily);
  static const IconData radio_button_bold = IconData(0xf963, fontFamily: _fontFamily);
  static const IconData rainbow_bold = IconData(0xf964, fontFamily: _fontFamily);
  static const IconData rainbow_cloud_bold = IconData(0xf965, fontFamily: _fontFamily);
  static const IconData receipt_bold = IconData(0xf966, fontFamily: _fontFamily);
  static const IconData record_bold = IconData(0xf967, fontFamily: _fontFamily);
  static const IconData rectangle_bold = IconData(0xf968, fontFamily: _fontFamily);
  static const IconData recycle_bold = IconData(0xf969, fontFamily: _fontFamily);
  static const IconData reddit_logo_bold = IconData(0xf96a, fontFamily: _fontFamily);
  static const IconData repeat_bold = IconData(0xf96b, fontFamily: _fontFamily);
  static const IconData repeat_once_bold = IconData(0xf96c, fontFamily: _fontFamily);
  static const IconData rewind_bold = IconData(0xf96d, fontFamily: _fontFamily);
  static const IconData rewind_circle_bold = IconData(0xf96e, fontFamily: _fontFamily);
  static const IconData robot_bold = IconData(0xf96f, fontFamily: _fontFamily);
  static const IconData rocket_bold = IconData(0xf970, fontFamily: _fontFamily);
  static const IconData rocket_launch_bold = IconData(0xf971, fontFamily: _fontFamily);
  static const IconData rows_bold = IconData(0xf972, fontFamily: _fontFamily);
  static const IconData rss_bold = IconData(0xf973, fontFamily: _fontFamily);
  static const IconData rss_simple_bold = IconData(0xf974, fontFamily: _fontFamily);
  static const IconData rug_bold = IconData(0xf975, fontFamily: _fontFamily);
  static const IconData ruler_bold = IconData(0xf976, fontFamily: _fontFamily);
  static const IconData scales_bold = IconData(0xf977, fontFamily: _fontFamily);
  static const IconData scan_bold = IconData(0xf978, fontFamily: _fontFamily);
  static const IconData scissors_bold = IconData(0xf979, fontFamily: _fontFamily);
  static const IconData screencast_bold = IconData(0xf97a, fontFamily: _fontFamily);
  static const IconData scribble_loop_bold = IconData(0xf97b, fontFamily: _fontFamily);
  static const IconData scroll_bold = IconData(0xf97c, fontFamily: _fontFamily);
  static const IconData selection_all_bold = IconData(0xf97d, fontFamily: _fontFamily);
  static const IconData selection_background_bold = IconData(0xf97e, fontFamily: _fontFamily);
  static const IconData selection_bold = IconData(0xf97f, fontFamily: _fontFamily);
  static const IconData selection_foreground_bold = IconData(0xf980, fontFamily: _fontFamily);
  static const IconData selection_inverse_bold = IconData(0xf981, fontFamily: _fontFamily);
  static const IconData selection_plus_bold = IconData(0xf982, fontFamily: _fontFamily);
  static const IconData selection_slash_bold = IconData(0xf983, fontFamily: _fontFamily);
  static const IconData share_bold = IconData(0xf984, fontFamily: _fontFamily);
  static const IconData share_network_bold = IconData(0xf985, fontFamily: _fontFamily);
  static const IconData shield_bold = IconData(0xf986, fontFamily: _fontFamily);
  static const IconData shield_check_bold = IconData(0xf987, fontFamily: _fontFamily);
  static const IconData shield_checkered_bold = IconData(0xf988, fontFamily: _fontFamily);
  static const IconData shield_chevron_bold = IconData(0xf989, fontFamily: _fontFamily);
  static const IconData shield_plus_bold = IconData(0xf98a, fontFamily: _fontFamily);
  static const IconData shield_slash_bold = IconData(0xf98b, fontFamily: _fontFamily);
  static const IconData shield_star_bold = IconData(0xf98c, fontFamily: _fontFamily);
  static const IconData shield_warning_bold = IconData(0xf98d, fontFamily: _fontFamily);
  static const IconData shopping_bag_bold = IconData(0xf98e, fontFamily: _fontFamily);
  static const IconData shopping_bag_open_bold = IconData(0xf98f, fontFamily: _fontFamily);
  static const IconData shopping_cart_bold = IconData(0xf990, fontFamily: _fontFamily);
  static const IconData shopping_cart_simple_bold = IconData(0xf991, fontFamily: _fontFamily);
  static const IconData shower_bold = IconData(0xf992, fontFamily: _fontFamily);
  static const IconData shuffle_angular_bold = IconData(0xf993, fontFamily: _fontFamily);
  static const IconData shuffle_bold = IconData(0xf994, fontFamily: _fontFamily);
  static const IconData shuffle_simple_bold = IconData(0xf995, fontFamily: _fontFamily);
  static const IconData sidebar_bold = IconData(0xf996, fontFamily: _fontFamily);
  static const IconData sidebar_simple_bold = IconData(0xf997, fontFamily: _fontFamily);
  static const IconData sign_in_bold = IconData(0xf998, fontFamily: _fontFamily);
  static const IconData sign_out_bold = IconData(0xf999, fontFamily: _fontFamily);
  static const IconData signpost_bold = IconData(0xf99a, fontFamily: _fontFamily);
  static const IconData sim_card_bold = IconData(0xf99b, fontFamily: _fontFamily);
  static const IconData sketch_logo_bold = IconData(0xf99c, fontFamily: _fontFamily);
  static const IconData skip_back_bold = IconData(0xf99d, fontFamily: _fontFamily);
  static const IconData skip_back_circle_bold = IconData(0xf99e, fontFamily: _fontFamily);
  static const IconData skip_forward_bold = IconData(0xf99f, fontFamily: _fontFamily);
  static const IconData skip_forward_circle_bold = IconData(0xf9a0, fontFamily: _fontFamily);
  static const IconData skull_bold = IconData(0xf9a1, fontFamily: _fontFamily);
  static const IconData slack_logo_bold = IconData(0xf9a2, fontFamily: _fontFamily);
  static const IconData sliders_bold = IconData(0xf9a3, fontFamily: _fontFamily);
  static const IconData sliders_horizontal_bold = IconData(0xf9a4, fontFamily: _fontFamily);
  static const IconData smiley_blank_bold = IconData(0xf9a5, fontFamily: _fontFamily);
  static const IconData smiley_bold = IconData(0xf9a6, fontFamily: _fontFamily);
  static const IconData smiley_meh_bold = IconData(0xf9a7, fontFamily: _fontFamily);
  static const IconData smiley_nervous_bold = IconData(0xf9a8, fontFamily: _fontFamily);
  static const IconData smiley_sad_bold = IconData(0xf9a9, fontFamily: _fontFamily);
  static const IconData smiley_sticker_bold = IconData(0xf9aa, fontFamily: _fontFamily);
  static const IconData smiley_wink_bold = IconData(0xf9ab, fontFamily: _fontFamily);
  static const IconData smiley_x_eyes_bold = IconData(0xf9ac, fontFamily: _fontFamily);
  static const IconData snapchat_logo_bold = IconData(0xf9ad, fontFamily: _fontFamily);
  static const IconData snowflake_bold = IconData(0xf9ae, fontFamily: _fontFamily);
  static const IconData soccer_ball_bold = IconData(0xf9af, fontFamily: _fontFamily);
  static const IconData sort_ascending_bold = IconData(0xf9b0, fontFamily: _fontFamily);
  static const IconData sort_descending_bold = IconData(0xf9b1, fontFamily: _fontFamily);
  static const IconData spade_bold = IconData(0xf9b2, fontFamily: _fontFamily);
  static const IconData sparkle_bold = IconData(0xf9b3, fontFamily: _fontFamily);
  static const IconData speaker_high_bold = IconData(0xf9b4, fontFamily: _fontFamily);
  static const IconData speaker_low_bold = IconData(0xf9b5, fontFamily: _fontFamily);
  static const IconData speaker_none_bold = IconData(0xf9b6, fontFamily: _fontFamily);
  static const IconData speaker_simple_high_bold = IconData(0xf9b7, fontFamily: _fontFamily);
  static const IconData speaker_simple_low_bold = IconData(0xf9b8, fontFamily: _fontFamily);
  static const IconData speaker_simple_none_bold = IconData(0xf9b9, fontFamily: _fontFamily);
  static const IconData speaker_simple_slash_bold = IconData(0xf9ba, fontFamily: _fontFamily);
  static const IconData speaker_simple_x_bold = IconData(0xf9bb, fontFamily: _fontFamily);
  static const IconData speaker_slash_bold = IconData(0xf9bc, fontFamily: _fontFamily);
  static const IconData speaker_x_bold = IconData(0xf9bd, fontFamily: _fontFamily);
  static const IconData spinner_bold = IconData(0xf9be, fontFamily: _fontFamily);
  static const IconData spinner_gap_bold = IconData(0xf9bf, fontFamily: _fontFamily);
  static const IconData spiral_bold = IconData(0xf9c0, fontFamily: _fontFamily);
  static const IconData spotify_logo_bold = IconData(0xf9c1, fontFamily: _fontFamily);
  static const IconData square_bold = IconData(0xf9c2, fontFamily: _fontFamily);
  static const IconData square_half_bold = IconData(0xf9c3, fontFamily: _fontFamily);
  static const IconData square_half_bottom_bold = IconData(0xf9c4, fontFamily: _fontFamily);
  static const IconData square_logo_bold = IconData(0xf9c5, fontFamily: _fontFamily);
  static const IconData squares_four_bold = IconData(0xf9c6, fontFamily: _fontFamily);
  static const IconData stack_bold = IconData(0xf9c7, fontFamily: _fontFamily);
  static const IconData stack_overflow_logo_bold = IconData(0xf9c8, fontFamily: _fontFamily);
  static const IconData stack_simple_bold = IconData(0xf9c9, fontFamily: _fontFamily);
  static const IconData stamp_bold = IconData(0xf9ca, fontFamily: _fontFamily);
  static const IconData star_bold = IconData(0xf9cb, fontFamily: _fontFamily);
  static const IconData star_four_bold = IconData(0xf9cc, fontFamily: _fontFamily);
  static const IconData star_half_bold = IconData(0xf9cd, fontFamily: _fontFamily);
  static const IconData sticker_bold = IconData(0xf9ce, fontFamily: _fontFamily);
  static const IconData stop_bold = IconData(0xf9cf, fontFamily: _fontFamily);
  static const IconData stop_circle_bold = IconData(0xf9d0, fontFamily: _fontFamily);
  static const IconData storefront_bold = IconData(0xf9d1, fontFamily: _fontFamily);
  static const IconData strategy_bold = IconData(0xf9d2, fontFamily: _fontFamily);
  static const IconData stripe_logo_bold = IconData(0xf9d3, fontFamily: _fontFamily);
  static const IconData student_bold = IconData(0xf9d4, fontFamily: _fontFamily);
  static const IconData suitcase_bold = IconData(0xf9d5, fontFamily: _fontFamily);
  static const IconData suitcase_simple_bold = IconData(0xf9d6, fontFamily: _fontFamily);
  static const IconData sun_bold = IconData(0xf9d7, fontFamily: _fontFamily);
  static const IconData sun_dim_bold = IconData(0xf9d8, fontFamily: _fontFamily);
  static const IconData sun_horizon_bold = IconData(0xf9d9, fontFamily: _fontFamily);
  static const IconData sunglasses_bold = IconData(0xf9da, fontFamily: _fontFamily);
  static const IconData swap_bold = IconData(0xf9db, fontFamily: _fontFamily);
  static const IconData swatches_bold = IconData(0xf9dc, fontFamily: _fontFamily);
  static const IconData sword_bold = IconData(0xf9dd, fontFamily: _fontFamily);
  static const IconData syringe_bold = IconData(0xf9de, fontFamily: _fontFamily);
  static const IconData t_shirt_bold = IconData(0xf9df, fontFamily: _fontFamily);
  static const IconData table_bold = IconData(0xf9e0, fontFamily: _fontFamily);
  static const IconData tabs_bold = IconData(0xf9e1, fontFamily: _fontFamily);
  static const IconData tag_bold = IconData(0xf9e2, fontFamily: _fontFamily);
  static const IconData tag_chevron_bold = IconData(0xf9e3, fontFamily: _fontFamily);
  static const IconData tag_simple_bold = IconData(0xf9e4, fontFamily: _fontFamily);
  static const IconData target_bold = IconData(0xf9e5, fontFamily: _fontFamily);
  static const IconData taxi_bold = IconData(0xf9e6, fontFamily: _fontFamily);
  static const IconData telegram_logo_bold = IconData(0xf9e7, fontFamily: _fontFamily);
  static const IconData television_bold = IconData(0xf9e8, fontFamily: _fontFamily);
  static const IconData television_simple_bold = IconData(0xf9e9, fontFamily: _fontFamily);
  static const IconData tennis_ball_bold = IconData(0xf9ea, fontFamily: _fontFamily);
  static const IconData terminal_bold = IconData(0xf9eb, fontFamily: _fontFamily);
  static const IconData terminal_window_bold = IconData(0xf9ec, fontFamily: _fontFamily);
  static const IconData test_tube_bold = IconData(0xf9ed, fontFamily: _fontFamily);
  static const IconData text_aa_bold = IconData(0xf9ee, fontFamily: _fontFamily);
  static const IconData text_align_center_bold = IconData(0xf9ef, fontFamily: _fontFamily);
  static const IconData text_align_justify_bold = IconData(0xf9f0, fontFamily: _fontFamily);
  static const IconData text_align_left_bold = IconData(0xf9f1, fontFamily: _fontFamily);
  static const IconData text_align_right_bold = IconData(0xf9f2, fontFamily: _fontFamily);
  static const IconData text_bolder_bold = IconData(0xf9f3, fontFamily: _fontFamily);
  static const IconData text_h_bold = IconData(0xf9f4, fontFamily: _fontFamily);
  static const IconData text_h_five_bold = IconData(0xf9f5, fontFamily: _fontFamily);
  static const IconData text_h_four_bold = IconData(0xf9f6, fontFamily: _fontFamily);
  static const IconData text_h_one_bold = IconData(0xf9f7, fontFamily: _fontFamily);
  static const IconData text_h_six_bold = IconData(0xf9f8, fontFamily: _fontFamily);
  static const IconData text_h_three_bold = IconData(0xf9f9, fontFamily: _fontFamily);
  static const IconData text_h_two_bold = IconData(0xf9fa, fontFamily: _fontFamily);
  static const IconData text_indent_bold = IconData(0xf9fb, fontFamily: _fontFamily);
  static const IconData text_italic_bold = IconData(0xf9fc, fontFamily: _fontFamily);
  static const IconData text_outdent_bold = IconData(0xf9fd, fontFamily: _fontFamily);
  static const IconData text_strikethrough_bold = IconData(0xf9fe, fontFamily: _fontFamily);
  static const IconData text_t_bold = IconData(0xf9ff, fontFamily: _fontFamily);
  static const IconData text_underline_bold = IconData(0xfa00, fontFamily: _fontFamily);
  static const IconData textbox_bold = IconData(0xfa01, fontFamily: _fontFamily);
  static const IconData thermometer_bold = IconData(0xfa02, fontFamily: _fontFamily);
  static const IconData thermometer_cold_bold = IconData(0xfa03, fontFamily: _fontFamily);
  static const IconData thermometer_hot_bold = IconData(0xfa04, fontFamily: _fontFamily);
  static const IconData thermometer_simple_bold = IconData(0xfa05, fontFamily: _fontFamily);
  static const IconData thumbs_down_bold = IconData(0xfa06, fontFamily: _fontFamily);
  static const IconData thumbs_up_bold = IconData(0xfa07, fontFamily: _fontFamily);
  static const IconData ticket_bold = IconData(0xfa08, fontFamily: _fontFamily);
  static const IconData tiktok_logo_bold = IconData(0xfa09, fontFamily: _fontFamily);
  static const IconData timer_bold = IconData(0xfa0a, fontFamily: _fontFamily);
  static const IconData toggle_left_bold = IconData(0xfa0b, fontFamily: _fontFamily);
  static const IconData toggle_right_bold = IconData(0xfa0c, fontFamily: _fontFamily);
  static const IconData toilet_bold = IconData(0xfa0d, fontFamily: _fontFamily);
  static const IconData toilet_paper_bold = IconData(0xfa0e, fontFamily: _fontFamily);
  static const IconData tote_bold = IconData(0xfa0f, fontFamily: _fontFamily);
  static const IconData tote_simple_bold = IconData(0xfa10, fontFamily: _fontFamily);
  static const IconData trademark_registered_bold = IconData(0xfa11, fontFamily: _fontFamily);
  static const IconData traffic_cone_bold = IconData(0xfa12, fontFamily: _fontFamily);
  static const IconData traffic_sign_bold = IconData(0xfa13, fontFamily: _fontFamily);
  static const IconData traffic_signal_bold = IconData(0xfa14, fontFamily: _fontFamily);
  static const IconData train_bold = IconData(0xfa15, fontFamily: _fontFamily);
  static const IconData train_regional_bold = IconData(0xfa16, fontFamily: _fontFamily);
  static const IconData train_simple_bold = IconData(0xfa17, fontFamily: _fontFamily);
  static const IconData translate_bold = IconData(0xfa18, fontFamily: _fontFamily);
  static const IconData trash_bold = IconData(0xfa19, fontFamily: _fontFamily);
  static const IconData trash_simple_bold = IconData(0xfa1a, fontFamily: _fontFamily);
  static const IconData tray_bold = IconData(0xfa1b, fontFamily: _fontFamily);
  static const IconData tree_bold = IconData(0xfa1c, fontFamily: _fontFamily);
  static const IconData tree_evergreen_bold = IconData(0xfa1d, fontFamily: _fontFamily);
  static const IconData tree_structure_bold = IconData(0xfa1e, fontFamily: _fontFamily);
  static const IconData trend_down_bold = IconData(0xfa1f, fontFamily: _fontFamily);
  static const IconData trend_up_bold = IconData(0xfa20, fontFamily: _fontFamily);
  static const IconData triangle_bold = IconData(0xfa21, fontFamily: _fontFamily);
  static const IconData trophy_bold = IconData(0xfa22, fontFamily: _fontFamily);
  static const IconData truck_bold = IconData(0xfa23, fontFamily: _fontFamily);
  static const IconData twitch_logo_bold = IconData(0xfa24, fontFamily: _fontFamily);
  static const IconData twitter_logo_bold = IconData(0xfa25, fontFamily: _fontFamily);
  static const IconData umbrella_bold = IconData(0xfa26, fontFamily: _fontFamily);
  static const IconData umbrella_simple_bold = IconData(0xfa27, fontFamily: _fontFamily);
  static const IconData upload_bold = IconData(0xfa28, fontFamily: _fontFamily);
  static const IconData upload_simple_bold = IconData(0xfa29, fontFamily: _fontFamily);
  static const IconData user_bold = IconData(0xfa2a, fontFamily: _fontFamily);
  static const IconData user_circle_bold = IconData(0xfa2b, fontFamily: _fontFamily);
  static const IconData user_circle_gear_bold = IconData(0xfa2c, fontFamily: _fontFamily);
  static const IconData user_circle_minus_bold = IconData(0xfa2d, fontFamily: _fontFamily);
  static const IconData user_circle_plus_bold = IconData(0xfa2e, fontFamily: _fontFamily);
  static const IconData user_focus_bold = IconData(0xfa2f, fontFamily: _fontFamily);
  static const IconData user_gear_bold = IconData(0xfa30, fontFamily: _fontFamily);
  static const IconData user_list_bold = IconData(0xfa31, fontFamily: _fontFamily);
  static const IconData user_minus_bold = IconData(0xfa32, fontFamily: _fontFamily);
  static const IconData user_plus_bold = IconData(0xfa33, fontFamily: _fontFamily);
  static const IconData user_rectangle_bold = IconData(0xfa34, fontFamily: _fontFamily);
  static const IconData user_square_bold = IconData(0xfa35, fontFamily: _fontFamily);
  static const IconData user_switch_bold = IconData(0xfa36, fontFamily: _fontFamily);
  static const IconData users_bold = IconData(0xfa37, fontFamily: _fontFamily);
  static const IconData users_four_bold = IconData(0xfa38, fontFamily: _fontFamily);
  static const IconData users_three_bold = IconData(0xfa39, fontFamily: _fontFamily);
  static const IconData vault_bold = IconData(0xfa3a, fontFamily: _fontFamily);
  static const IconData vibrate_bold = IconData(0xfa3b, fontFamily: _fontFamily);
  static const IconData video_camera_bold = IconData(0xfa3c, fontFamily: _fontFamily);
  static const IconData video_camera_slash_bold = IconData(0xfa3d, fontFamily: _fontFamily);
  static const IconData vignette_bold = IconData(0xfa3e, fontFamily: _fontFamily);
  static const IconData voicemail_bold = IconData(0xfa3f, fontFamily: _fontFamily);
  static const IconData volleyball_bold = IconData(0xfa40, fontFamily: _fontFamily);
  static const IconData wall_bold = IconData(0xfa41, fontFamily: _fontFamily);
  static const IconData wallet_bold = IconData(0xfa42, fontFamily: _fontFamily);
  static const IconData warning_bold = IconData(0xfa43, fontFamily: _fontFamily);
  static const IconData warning_circle_bold = IconData(0xfa44, fontFamily: _fontFamily);
  static const IconData warning_octagon_bold = IconData(0xfa45, fontFamily: _fontFamily);
  static const IconData watch_bold = IconData(0xfa46, fontFamily: _fontFamily);
  static const IconData wave_sawtooth_bold = IconData(0xfa47, fontFamily: _fontFamily);
  static const IconData wave_sine_bold = IconData(0xfa48, fontFamily: _fontFamily);
  static const IconData wave_square_bold = IconData(0xfa49, fontFamily: _fontFamily);
  static const IconData wave_triangle_bold = IconData(0xfa4a, fontFamily: _fontFamily);
  static const IconData waves_bold = IconData(0xfa4b, fontFamily: _fontFamily);
  static const IconData webcam_bold = IconData(0xfa4c, fontFamily: _fontFamily);
  static const IconData whatsapp_logo_bold = IconData(0xfa4d, fontFamily: _fontFamily);
  static const IconData wheelchair_bold = IconData(0xfa4e, fontFamily: _fontFamily);
  static const IconData wifi_high_bold = IconData(0xfa4f, fontFamily: _fontFamily);
  static const IconData wifi_low_bold = IconData(0xfa50, fontFamily: _fontFamily);
  static const IconData wifi_medium_bold = IconData(0xfa51, fontFamily: _fontFamily);
  static const IconData wifi_none_bold = IconData(0xfa52, fontFamily: _fontFamily);
  static const IconData wifi_slash_bold = IconData(0xfa53, fontFamily: _fontFamily);
  static const IconData wifi_x_bold = IconData(0xfa54, fontFamily: _fontFamily);
  static const IconData wind_bold = IconData(0xfa55, fontFamily: _fontFamily);
  static const IconData windows_logo_bold = IconData(0xfa56, fontFamily: _fontFamily);
  static const IconData wine_bold = IconData(0xfa57, fontFamily: _fontFamily);
  static const IconData wrench_bold = IconData(0xfa58, fontFamily: _fontFamily);
  static const IconData x_bold = IconData(0xfa59, fontFamily: _fontFamily);
  static const IconData x_circle_bold = IconData(0xfa5a, fontFamily: _fontFamily);
  static const IconData x_square_bold = IconData(0xfa5b, fontFamily: _fontFamily);
  static const IconData yin_yang_bold = IconData(0xfa5c, fontFamily: _fontFamily);
  static const IconData youtube_logo_bold = IconData(0xfa5d, fontFamily: _fontFamily);
  static const IconData activity_fill = IconData(0xfa5e, fontFamily: _fontFamily);
  static const IconData address_book_fill = IconData(0xfa5f, fontFamily: _fontFamily);
  static const IconData airplane_fill = IconData(0xfa60, fontFamily: _fontFamily);
  static const IconData airplane_in_flight_fill = IconData(0xfa61, fontFamily: _fontFamily);
  static const IconData airplane_landing_fill = IconData(0xfa62, fontFamily: _fontFamily);
  static const IconData airplane_takeoff_fill = IconData(0xfa63, fontFamily: _fontFamily);
  static const IconData airplane_tilt_fill = IconData(0xfa64, fontFamily: _fontFamily);
  static const IconData airplay_fill = IconData(0xfa65, fontFamily: _fontFamily);
  static const IconData alarm_fill = IconData(0xfa66, fontFamily: _fontFamily);
  static const IconData alien_fill = IconData(0xfa67, fontFamily: _fontFamily);
  static const IconData align_bottom_fill = IconData(0xfa68, fontFamily: _fontFamily);
  static const IconData align_bottom_simple_fill = IconData(0xfa69, fontFamily: _fontFamily);
  static const IconData align_center_horizontal_fill = IconData(0xfa6a, fontFamily: _fontFamily);
  static const IconData align_center_horizontal_simple_fill = IconData(0xfa6b, fontFamily: _fontFamily);
  static const IconData align_center_vertical_fill = IconData(0xfa6c, fontFamily: _fontFamily);
  static const IconData align_center_vertical_simple_fill = IconData(0xfa6d, fontFamily: _fontFamily);
  static const IconData align_left_fill = IconData(0xfa6e, fontFamily: _fontFamily);
  static const IconData align_left_simple_fill = IconData(0xfa6f, fontFamily: _fontFamily);
  static const IconData align_right_fill = IconData(0xfa70, fontFamily: _fontFamily);
  static const IconData align_right_simple_fill = IconData(0xfa71, fontFamily: _fontFamily);
  static const IconData align_top_fill = IconData(0xfa72, fontFamily: _fontFamily);
  static const IconData align_top_simple_fill = IconData(0xfa73, fontFamily: _fontFamily);
  static const IconData anchor_fill = IconData(0xfa74, fontFamily: _fontFamily);
  static const IconData anchor_simple_fill = IconData(0xfa75, fontFamily: _fontFamily);
  static const IconData android_logo_fill = IconData(0xfa76, fontFamily: _fontFamily);
  static const IconData angular_logo_fill = IconData(0xfa77, fontFamily: _fontFamily);
  static const IconData aperture_fill = IconData(0xfa78, fontFamily: _fontFamily);
  static const IconData app_store_logo_fill = IconData(0xfa79, fontFamily: _fontFamily);
  static const IconData app_window_fill = IconData(0xfa7a, fontFamily: _fontFamily);
  static const IconData apple_logo_fill = IconData(0xfa7b, fontFamily: _fontFamily);
  static const IconData apple_podcasts_logo_fill = IconData(0xfa7c, fontFamily: _fontFamily);
  static const IconData archive_box_fill = IconData(0xfa7d, fontFamily: _fontFamily);
  static const IconData archive_fill = IconData(0xfa7e, fontFamily: _fontFamily);
  static const IconData archive_tray_fill = IconData(0xfa7f, fontFamily: _fontFamily);
  static const IconData armchair_fill = IconData(0xfa80, fontFamily: _fontFamily);
  static const IconData arrow_arc_left_fill = IconData(0xfa81, fontFamily: _fontFamily);
  static const IconData arrow_arc_right_fill = IconData(0xfa82, fontFamily: _fontFamily);
  static const IconData arrow_bend_double_up_left_fill = IconData(0xfa83, fontFamily: _fontFamily);
  static const IconData arrow_bend_double_up_right_fill = IconData(0xfa84, fontFamily: _fontFamily);
  static const IconData arrow_bend_down_left_fill = IconData(0xfa85, fontFamily: _fontFamily);
  static const IconData arrow_bend_down_right_fill = IconData(0xfa86, fontFamily: _fontFamily);
  static const IconData arrow_bend_left_down_fill = IconData(0xfa87, fontFamily: _fontFamily);
  static const IconData arrow_bend_left_up_fill = IconData(0xfa88, fontFamily: _fontFamily);
  static const IconData arrow_bend_right_down_fill = IconData(0xfa89, fontFamily: _fontFamily);
  static const IconData arrow_bend_right_up_fill = IconData(0xfa8a, fontFamily: _fontFamily);
  static const IconData arrow_bend_up_left_fill = IconData(0xfa8b, fontFamily: _fontFamily);
  static const IconData arrow_bend_up_right_fill = IconData(0xfa8c, fontFamily: _fontFamily);
  static const IconData arrow_circle_down_fill = IconData(0xfa8d, fontFamily: _fontFamily);
  static const IconData arrow_circle_down_left_fill = IconData(0xfa8e, fontFamily: _fontFamily);
  static const IconData arrow_circle_down_right_fill = IconData(0xfa8f, fontFamily: _fontFamily);
  static const IconData arrow_circle_left_fill = IconData(0xfa90, fontFamily: _fontFamily);
  static const IconData arrow_circle_right_fill = IconData(0xfa91, fontFamily: _fontFamily);
  static const IconData arrow_circle_up_fill = IconData(0xfa92, fontFamily: _fontFamily);
  static const IconData arrow_circle_up_left_fill = IconData(0xfa93, fontFamily: _fontFamily);
  static const IconData arrow_circle_up_right_fill = IconData(0xfa94, fontFamily: _fontFamily);
  static const IconData arrow_clockwise_fill = IconData(0xfa95, fontFamily: _fontFamily);
  static const IconData arrow_counter_clockwise_fill = IconData(0xfa96, fontFamily: _fontFamily);
  static const IconData arrow_down_fill = IconData(0xfa97, fontFamily: _fontFamily);
  static const IconData arrow_down_left_fill = IconData(0xfa98, fontFamily: _fontFamily);
  static const IconData arrow_down_right_fill = IconData(0xfa99, fontFamily: _fontFamily);
  static const IconData arrow_elbow_down_left_fill = IconData(0xfa9a, fontFamily: _fontFamily);
  static const IconData arrow_elbow_down_right_fill = IconData(0xfa9b, fontFamily: _fontFamily);
  static const IconData arrow_elbow_left_down_fill = IconData(0xfa9c, fontFamily: _fontFamily);
  static const IconData arrow_elbow_left_fill = IconData(0xfa9d, fontFamily: _fontFamily);
  static const IconData arrow_elbow_left_up_fill = IconData(0xfa9e, fontFamily: _fontFamily);
  static const IconData arrow_elbow_right_down_fill = IconData(0xfa9f, fontFamily: _fontFamily);
  static const IconData arrow_elbow_right_fill = IconData(0xfaa0, fontFamily: _fontFamily);
  static const IconData arrow_elbow_right_up_fill = IconData(0xfaa1, fontFamily: _fontFamily);
  static const IconData arrow_elbow_up_left_fill = IconData(0xfaa2, fontFamily: _fontFamily);
  static const IconData arrow_elbow_up_right_fill = IconData(0xfaa3, fontFamily: _fontFamily);
  static const IconData arrow_fat_down_fill = IconData(0xfaa4, fontFamily: _fontFamily);
  static const IconData arrow_fat_left_fill = IconData(0xfaa5, fontFamily: _fontFamily);
  static const IconData arrow_fat_line_down_fill = IconData(0xfaa6, fontFamily: _fontFamily);
  static const IconData arrow_fat_line_left_fill = IconData(0xfaa7, fontFamily: _fontFamily);
  static const IconData arrow_fat_line_right_fill = IconData(0xfaa8, fontFamily: _fontFamily);
  static const IconData arrow_fat_line_up_fill = IconData(0xfaa9, fontFamily: _fontFamily);
  static const IconData arrow_fat_lines_down_fill = IconData(0xfaaa, fontFamily: _fontFamily);
  static const IconData arrow_fat_lines_left_fill = IconData(0xfaab, fontFamily: _fontFamily);
  static const IconData arrow_fat_lines_right_fill = IconData(0xfaac, fontFamily: _fontFamily);
  static const IconData arrow_fat_lines_up_fill = IconData(0xfaad, fontFamily: _fontFamily);
  static const IconData arrow_fat_right_fill = IconData(0xfaae, fontFamily: _fontFamily);
  static const IconData arrow_fat_up_fill = IconData(0xfaaf, fontFamily: _fontFamily);
  static const IconData arrow_left_fill = IconData(0xfab0, fontFamily: _fontFamily);
  static const IconData arrow_line_down_fill = IconData(0xfab1, fontFamily: _fontFamily);
  static const IconData arrow_line_down_left_fill = IconData(0xfab2, fontFamily: _fontFamily);
  static const IconData arrow_line_down_right_fill = IconData(0xfab3, fontFamily: _fontFamily);
  static const IconData arrow_line_left_fill = IconData(0xfab4, fontFamily: _fontFamily);
  static const IconData arrow_line_right_fill = IconData(0xfab5, fontFamily: _fontFamily);
  static const IconData arrow_line_up_fill = IconData(0xfab6, fontFamily: _fontFamily);
  static const IconData arrow_line_up_left_fill = IconData(0xfab7, fontFamily: _fontFamily);
  static const IconData arrow_line_up_right_fill = IconData(0xfab8, fontFamily: _fontFamily);
  static const IconData arrow_right_fill = IconData(0xfab9, fontFamily: _fontFamily);
  static const IconData arrow_square_down_fill = IconData(0xfaba, fontFamily: _fontFamily);
  static const IconData arrow_square_down_left_fill = IconData(0xfabb, fontFamily: _fontFamily);
  static const IconData arrow_square_down_right_fill = IconData(0xfabc, fontFamily: _fontFamily);
  static const IconData arrow_square_in_fill = IconData(0xfabd, fontFamily: _fontFamily);
  static const IconData arrow_square_left_fill = IconData(0xfabe, fontFamily: _fontFamily);
  static const IconData arrow_square_out_fill = IconData(0xfabf, fontFamily: _fontFamily);
  static const IconData arrow_square_right_fill = IconData(0xfac0, fontFamily: _fontFamily);
  static const IconData arrow_square_up_fill = IconData(0xfac1, fontFamily: _fontFamily);
  static const IconData arrow_square_up_left_fill = IconData(0xfac2, fontFamily: _fontFamily);
  static const IconData arrow_square_up_right_fill = IconData(0xfac3, fontFamily: _fontFamily);
  static const IconData arrow_u_down_left_fill = IconData(0xfac4, fontFamily: _fontFamily);
  static const IconData arrow_u_down_right_fill = IconData(0xfac5, fontFamily: _fontFamily);
  static const IconData arrow_u_left_down_fill = IconData(0xfac6, fontFamily: _fontFamily);
  static const IconData arrow_u_left_up_fill = IconData(0xfac7, fontFamily: _fontFamily);
  static const IconData arrow_u_right_down_fill = IconData(0xfac8, fontFamily: _fontFamily);
  static const IconData arrow_u_right_up_fill = IconData(0xfac9, fontFamily: _fontFamily);
  static const IconData arrow_u_up_left_fill = IconData(0xfaca, fontFamily: _fontFamily);
  static const IconData arrow_u_up_right_fill = IconData(0xfacb, fontFamily: _fontFamily);
  static const IconData arrow_up_fill = IconData(0xfacc, fontFamily: _fontFamily);
  static const IconData arrow_up_left_fill = IconData(0xfacd, fontFamily: _fontFamily);
  static const IconData arrow_up_right_fill = IconData(0xface, fontFamily: _fontFamily);
  static const IconData arrows_clockwise_fill = IconData(0xfacf, fontFamily: _fontFamily);
  static const IconData arrows_counter_clockwise_fill = IconData(0xfad0, fontFamily: _fontFamily);
  static const IconData arrows_down_up_fill = IconData(0xfad1, fontFamily: _fontFamily);
  static const IconData arrows_horizontal_fill = IconData(0xfad2, fontFamily: _fontFamily);
  static const IconData arrows_in_cardinal_fill = IconData(0xfad3, fontFamily: _fontFamily);
  static const IconData arrows_in_fill = IconData(0xfad4, fontFamily: _fontFamily);
  static const IconData arrows_in_line_horizontal_fill = IconData(0xfad5, fontFamily: _fontFamily);
  static const IconData arrows_in_line_vertical_fill = IconData(0xfad6, fontFamily: _fontFamily);
  static const IconData arrows_in_simple_fill = IconData(0xfad7, fontFamily: _fontFamily);
  static const IconData arrows_left_right_fill = IconData(0xfad8, fontFamily: _fontFamily);
  static const IconData arrows_out_cardinal_fill = IconData(0xfad9, fontFamily: _fontFamily);
  static const IconData arrows_out_fill = IconData(0xfada, fontFamily: _fontFamily);
  static const IconData arrows_out_line_horizontal_fill = IconData(0xfadb, fontFamily: _fontFamily);
  static const IconData arrows_out_line_vertical_fill = IconData(0xfadc, fontFamily: _fontFamily);
  static const IconData arrows_out_simple_fill = IconData(0xfadd, fontFamily: _fontFamily);
  static const IconData arrows_vertical_fill = IconData(0xfade, fontFamily: _fontFamily);
  static const IconData article_fill = IconData(0xfadf, fontFamily: _fontFamily);
  static const IconData article_medium_fill = IconData(0xfae0, fontFamily: _fontFamily);
  static const IconData article_ny_times_fill = IconData(0xfae1, fontFamily: _fontFamily);
  static const IconData asterisk_fill = IconData(0xfae2, fontFamily: _fontFamily);
  static const IconData asterisk_simple_fill = IconData(0xfae3, fontFamily: _fontFamily);
  static const IconData at_fill = IconData(0xfae4, fontFamily: _fontFamily);
  static const IconData atom_fill = IconData(0xfae5, fontFamily: _fontFamily);
  static const IconData baby_fill = IconData(0xfae6, fontFamily: _fontFamily);
  static const IconData backpack_fill = IconData(0xfae7, fontFamily: _fontFamily);
  static const IconData backspace_fill = IconData(0xfae8, fontFamily: _fontFamily);
  static const IconData bag_fill = IconData(0xfae9, fontFamily: _fontFamily);
  static const IconData bag_simple_fill = IconData(0xfaea, fontFamily: _fontFamily);
  static const IconData balloon_fill = IconData(0xfaeb, fontFamily: _fontFamily);
  static const IconData bandaids_fill = IconData(0xfaec, fontFamily: _fontFamily);
  static const IconData bank_fill = IconData(0xfaed, fontFamily: _fontFamily);
  static const IconData barbell_fill = IconData(0xfaee, fontFamily: _fontFamily);
  static const IconData barcode_fill = IconData(0xfaef, fontFamily: _fontFamily);
  static const IconData barricade_fill = IconData(0xfaf0, fontFamily: _fontFamily);
  static const IconData baseball_fill = IconData(0xfaf1, fontFamily: _fontFamily);
  static const IconData basketball_fill = IconData(0xfaf2, fontFamily: _fontFamily);
  static const IconData bathtub_fill = IconData(0xfaf3, fontFamily: _fontFamily);
  static const IconData battery_charging_fill = IconData(0xfaf4, fontFamily: _fontFamily);
  static const IconData battery_charging_vertical_fill = IconData(0xfaf5, fontFamily: _fontFamily);
  static const IconData battery_empty_fill = IconData(0xfaf6, fontFamily: _fontFamily);
  static const IconData battery_full_fill = IconData(0xfaf7, fontFamily: _fontFamily);
  static const IconData battery_high_fill = IconData(0xfaf8, fontFamily: _fontFamily);
  static const IconData battery_low_fill = IconData(0xfaf9, fontFamily: _fontFamily);
  static const IconData battery_medium_fill = IconData(0xfafa, fontFamily: _fontFamily);
  static const IconData battery_plus_fill = IconData(0xfafb, fontFamily: _fontFamily);
  static const IconData battery_warning_fill = IconData(0xfafc, fontFamily: _fontFamily);
  static const IconData battery_warning_vertical_fill = IconData(0xfafd, fontFamily: _fontFamily);
  static const IconData bed_fill = IconData(0xfafe, fontFamily: _fontFamily);
  static const IconData beer_bottle_fill = IconData(0xfaff, fontFamily: _fontFamily);
  static const IconData behance_logo_fill = IconData(0xfb00, fontFamily: _fontFamily);
  static const IconData bell_fill = IconData(0xfb01, fontFamily: _fontFamily);
  static const IconData bell_ringing_fill = IconData(0xfb02, fontFamily: _fontFamily);
  static const IconData bell_simple_fill = IconData(0xfb03, fontFamily: _fontFamily);
  static const IconData bell_simple_ringing_fill = IconData(0xfb04, fontFamily: _fontFamily);
  static const IconData bell_simple_slash_fill = IconData(0xfb05, fontFamily: _fontFamily);
  static const IconData bell_simple_z_fill = IconData(0xfb06, fontFamily: _fontFamily);
  static const IconData bell_slash_fill = IconData(0xfb07, fontFamily: _fontFamily);
  static const IconData bell_z_fill = IconData(0xfb08, fontFamily: _fontFamily);
  static const IconData bezier_curve_fill = IconData(0xfb09, fontFamily: _fontFamily);
  static const IconData bicycle_fill = IconData(0xfb0a, fontFamily: _fontFamily);
  static const IconData binoculars_fill = IconData(0xfb0b, fontFamily: _fontFamily);
  static const IconData bird_fill = IconData(0xfb0c, fontFamily: _fontFamily);
  static const IconData bluetooth_connected_fill = IconData(0xfb0d, fontFamily: _fontFamily);
  static const IconData bluetooth_fill = IconData(0xfb0e, fontFamily: _fontFamily);
  static const IconData bluetooth_slash_fill = IconData(0xfb0f, fontFamily: _fontFamily);
  static const IconData bluetooth_x_fill = IconData(0xfb10, fontFamily: _fontFamily);
  static const IconData boat_fill = IconData(0xfb11, fontFamily: _fontFamily);
  static const IconData book_bookmark_fill = IconData(0xfb12, fontFamily: _fontFamily);
  static const IconData book_fill = IconData(0xfb13, fontFamily: _fontFamily);
  static const IconData book_open_fill = IconData(0xfb14, fontFamily: _fontFamily);
  static const IconData bookmark_fill = IconData(0xfb15, fontFamily: _fontFamily);
  static const IconData bookmark_simple_fill = IconData(0xfb16, fontFamily: _fontFamily);
  static const IconData bookmarks_fill = IconData(0xfb17, fontFamily: _fontFamily);
  static const IconData bookmarks_simple_fill = IconData(0xfb18, fontFamily: _fontFamily);
  static const IconData books_fill = IconData(0xfb19, fontFamily: _fontFamily);
  static const IconData bounding_box_fill = IconData(0xfb1a, fontFamily: _fontFamily);
  static const IconData brackets_angle_fill = IconData(0xfb1b, fontFamily: _fontFamily);
  static const IconData brackets_curly_fill = IconData(0xfb1c, fontFamily: _fontFamily);
  static const IconData brackets_round_fill = IconData(0xfb1d, fontFamily: _fontFamily);
  static const IconData brackets_square_fill = IconData(0xfb1e, fontFamily: _fontFamily);
  static const IconData brain_fill = IconData(0xfb1f, fontFamily: _fontFamily);
  static const IconData brandy_fill = IconData(0xfb20, fontFamily: _fontFamily);
  static const IconData briefcase_fill = IconData(0xfb21, fontFamily: _fontFamily);
  static const IconData briefcase_metal_fill = IconData(0xfb22, fontFamily: _fontFamily);
  static const IconData broadcast_fill = IconData(0xfb23, fontFamily: _fontFamily);
  static const IconData browser_fill = IconData(0xfb24, fontFamily: _fontFamily);
  static const IconData browsers_fill = IconData(0xfb25, fontFamily: _fontFamily);
  static const IconData bug_beetle_fill = IconData(0xfb26, fontFamily: _fontFamily);
  static const IconData bug_droid_fill = IconData(0xfb27, fontFamily: _fontFamily);
  static const IconData bug_fill = IconData(0xfb28, fontFamily: _fontFamily);
  static const IconData buildings_fill = IconData(0xfb29, fontFamily: _fontFamily);
  static const IconData bus_fill = IconData(0xfb2a, fontFamily: _fontFamily);
  static const IconData butterfly_fill = IconData(0xfb2b, fontFamily: _fontFamily);
  static const IconData cactus_fill = IconData(0xfb2c, fontFamily: _fontFamily);
  static const IconData cake_fill = IconData(0xfb2d, fontFamily: _fontFamily);
  static const IconData calculator_fill = IconData(0xfb2e, fontFamily: _fontFamily);
  static const IconData calendar_blank_fill = IconData(0xfb2f, fontFamily: _fontFamily);
  static const IconData calendar_check_fill = IconData(0xfb30, fontFamily: _fontFamily);
  static const IconData calendar_fill = IconData(0xfb31, fontFamily: _fontFamily);
  static const IconData calendar_plus_fill = IconData(0xfb32, fontFamily: _fontFamily);
  static const IconData calendar_x_fill = IconData(0xfb33, fontFamily: _fontFamily);
  static const IconData camera_fill = IconData(0xfb34, fontFamily: _fontFamily);
  static const IconData camera_rotate_fill = IconData(0xfb35, fontFamily: _fontFamily);
  static const IconData camera_slash_fill = IconData(0xfb36, fontFamily: _fontFamily);
  static const IconData campfire_fill = IconData(0xfb37, fontFamily: _fontFamily);
  static const IconData car_fill = IconData(0xfb38, fontFamily: _fontFamily);
  static const IconData car_simple_fill = IconData(0xfb39, fontFamily: _fontFamily);
  static const IconData cardholder_fill = IconData(0xfb3a, fontFamily: _fontFamily);
  static const IconData cards_fill = IconData(0xfb3b, fontFamily: _fontFamily);
  static const IconData caret_circle_double_down_fill = IconData(0xfb3c, fontFamily: _fontFamily);
  static const IconData caret_circle_double_left_fill = IconData(0xfb3d, fontFamily: _fontFamily);
  static const IconData caret_circle_double_right_fill = IconData(0xfb3e, fontFamily: _fontFamily);
  static const IconData caret_circle_double_up_fill = IconData(0xfb3f, fontFamily: _fontFamily);
  static const IconData caret_circle_down_fill = IconData(0xfb40, fontFamily: _fontFamily);
  static const IconData caret_circle_left_fill = IconData(0xfb41, fontFamily: _fontFamily);
  static const IconData caret_circle_right_fill = IconData(0xfb42, fontFamily: _fontFamily);
  static const IconData caret_circle_up_fill = IconData(0xfb43, fontFamily: _fontFamily);
  static const IconData caret_double_down_fill = IconData(0xfb44, fontFamily: _fontFamily);
  static const IconData caret_double_left_fill = IconData(0xfb45, fontFamily: _fontFamily);
  static const IconData caret_double_right_fill = IconData(0xfb46, fontFamily: _fontFamily);
  static const IconData caret_double_up_fill = IconData(0xfb47, fontFamily: _fontFamily);
  static const IconData caret_down_fill = IconData(0xfb48, fontFamily: _fontFamily);
  static const IconData caret_left_fill = IconData(0xfb49, fontFamily: _fontFamily);
  static const IconData caret_right_fill = IconData(0xfb4a, fontFamily: _fontFamily);
  static const IconData caret_up_fill = IconData(0xfb4b, fontFamily: _fontFamily);
  static const IconData cat_fill = IconData(0xfb4c, fontFamily: _fontFamily);
  static const IconData cell_signal_full_fill = IconData(0xfb4d, fontFamily: _fontFamily);
  static const IconData cell_signal_high_fill = IconData(0xfb4e, fontFamily: _fontFamily);
  static const IconData cell_signal_low_fill = IconData(0xfb4f, fontFamily: _fontFamily);
  static const IconData cell_signal_medium_fill = IconData(0xfb50, fontFamily: _fontFamily);
  static const IconData cell_signal_none_fill = IconData(0xfb51, fontFamily: _fontFamily);
  static const IconData cell_signal_slash_fill = IconData(0xfb52, fontFamily: _fontFamily);
  static const IconData cell_signal_x_fill = IconData(0xfb53, fontFamily: _fontFamily);
  static const IconData chalkboard_fill = IconData(0xfb54, fontFamily: _fontFamily);
  static const IconData chalkboard_simple_fill = IconData(0xfb55, fontFamily: _fontFamily);
  static const IconData chalkboard_teacher_fill = IconData(0xfb56, fontFamily: _fontFamily);
  static const IconData chart_bar_fill = IconData(0xfb57, fontFamily: _fontFamily);
  static const IconData chart_bar_horizontal_fill = IconData(0xfb58, fontFamily: _fontFamily);
  static const IconData chart_line_fill = IconData(0xfb59, fontFamily: _fontFamily);
  static const IconData chart_line_up_fill = IconData(0xfb5a, fontFamily: _fontFamily);
  static const IconData chart_pie_fill = IconData(0xfb5b, fontFamily: _fontFamily);
  static const IconData chart_pie_slice_fill = IconData(0xfb5c, fontFamily: _fontFamily);
  static const IconData chat_centered_dots_fill = IconData(0xfb5d, fontFamily: _fontFamily);
  static const IconData chat_centered_fill = IconData(0xfb5e, fontFamily: _fontFamily);
  static const IconData chat_centered_text_fill = IconData(0xfb5f, fontFamily: _fontFamily);
  static const IconData chat_circle_dots_fill = IconData(0xfb60, fontFamily: _fontFamily);
  static const IconData chat_circle_fill = IconData(0xfb61, fontFamily: _fontFamily);
  static const IconData chat_circle_text_fill = IconData(0xfb62, fontFamily: _fontFamily);
  static const IconData chat_dots_fill = IconData(0xfb63, fontFamily: _fontFamily);
  static const IconData chat_fill = IconData(0xfb64, fontFamily: _fontFamily);
  static const IconData chat_teardrop_dots_fill = IconData(0xfb65, fontFamily: _fontFamily);
  static const IconData chat_teardrop_fill = IconData(0xfb66, fontFamily: _fontFamily);
  static const IconData chat_teardrop_text_fill = IconData(0xfb67, fontFamily: _fontFamily);
  static const IconData chat_text_fill = IconData(0xfb68, fontFamily: _fontFamily);
  static const IconData chats_circle_fill = IconData(0xfb69, fontFamily: _fontFamily);
  static const IconData chats_fill = IconData(0xfb6a, fontFamily: _fontFamily);
  static const IconData chats_teardrop_fill = IconData(0xfb6b, fontFamily: _fontFamily);
  static const IconData check_circle_fill = IconData(0xfb6c, fontFamily: _fontFamily);
  static const IconData check_fill = IconData(0xfb6d, fontFamily: _fontFamily);
  static const IconData check_square_fill = IconData(0xfb6e, fontFamily: _fontFamily);
  static const IconData check_square_offset_fill = IconData(0xfb6f, fontFamily: _fontFamily);
  static const IconData checks_fill = IconData(0xfb70, fontFamily: _fontFamily);
  static const IconData circle_dashed_fill = IconData(0xfb71, fontFamily: _fontFamily);
  static const IconData circle_fill = IconData(0xfb72, fontFamily: _fontFamily);
  static const IconData circle_half_fill = IconData(0xfb73, fontFamily: _fontFamily);
  static const IconData circle_half_tilt_fill = IconData(0xfb74, fontFamily: _fontFamily);
  static const IconData circle_notch_fill = IconData(0xfb75, fontFamily: _fontFamily);
  static const IconData circle_wavy_check_fill = IconData(0xfb76, fontFamily: _fontFamily);
  static const IconData circle_wavy_fill = IconData(0xfb77, fontFamily: _fontFamily);
  static const IconData circle_wavy_question_fill = IconData(0xfb78, fontFamily: _fontFamily);
  static const IconData circle_wavy_warning_fill = IconData(0xfb79, fontFamily: _fontFamily);
  static const IconData circles_four_fill = IconData(0xfb7a, fontFamily: _fontFamily);
  static const IconData circles_three_fill = IconData(0xfb7b, fontFamily: _fontFamily);
  static const IconData circles_three_plus_fill = IconData(0xfb7c, fontFamily: _fontFamily);
  static const IconData clipboard_fill = IconData(0xfb7d, fontFamily: _fontFamily);
  static const IconData clipboard_text_fill = IconData(0xfb7e, fontFamily: _fontFamily);
  static const IconData clock_afternoon_fill = IconData(0xfb7f, fontFamily: _fontFamily);
  static const IconData clock_clockwise_fill = IconData(0xfb80, fontFamily: _fontFamily);
  static const IconData clock_counter_clockwise_fill = IconData(0xfb81, fontFamily: _fontFamily);
  static const IconData clock_fill = IconData(0xfb82, fontFamily: _fontFamily);
  static const IconData closed_captioning_fill = IconData(0xfb83, fontFamily: _fontFamily);
  static const IconData cloud_arrow_down_fill = IconData(0xfb84, fontFamily: _fontFamily);
  static const IconData cloud_arrow_up_fill = IconData(0xfb85, fontFamily: _fontFamily);
  static const IconData cloud_check_fill = IconData(0xfb86, fontFamily: _fontFamily);
  static const IconData cloud_fill = IconData(0xfb87, fontFamily: _fontFamily);
  static const IconData cloud_fog_fill = IconData(0xfb88, fontFamily: _fontFamily);
  static const IconData cloud_lightning_fill = IconData(0xfb89, fontFamily: _fontFamily);
  static const IconData cloud_moon_fill = IconData(0xfb8a, fontFamily: _fontFamily);
  static const IconData cloud_rain_fill = IconData(0xfb8b, fontFamily: _fontFamily);
  static const IconData cloud_slash_fill = IconData(0xfb8c, fontFamily: _fontFamily);
  static const IconData cloud_snow_fill = IconData(0xfb8d, fontFamily: _fontFamily);
  static const IconData cloud_sun_fill = IconData(0xfb8e, fontFamily: _fontFamily);
  static const IconData club_fill = IconData(0xfb8f, fontFamily: _fontFamily);
  static const IconData coat_hanger_fill = IconData(0xfb90, fontFamily: _fontFamily);
  static const IconData code_fill = IconData(0xfb91, fontFamily: _fontFamily);
  static const IconData code_simple_fill = IconData(0xfb92, fontFamily: _fontFamily);
  static const IconData codepen_logo_fill = IconData(0xfb93, fontFamily: _fontFamily);
  static const IconData codesandbox_logo_fill = IconData(0xfb94, fontFamily: _fontFamily);
  static const IconData coffee_fill = IconData(0xfb95, fontFamily: _fontFamily);
  static const IconData coin_fill = IconData(0xfb96, fontFamily: _fontFamily);
  static const IconData coin_vertical_fill = IconData(0xfb97, fontFamily: _fontFamily);
  static const IconData coins_fill = IconData(0xfb98, fontFamily: _fontFamily);
  static const IconData columns_fill = IconData(0xfb99, fontFamily: _fontFamily);
  static const IconData command_fill = IconData(0xfb9a, fontFamily: _fontFamily);
  static const IconData compass_fill = IconData(0xfb9b, fontFamily: _fontFamily);
  static const IconData computer_tower_fill = IconData(0xfb9c, fontFamily: _fontFamily);
  static const IconData confetti_fill = IconData(0xfb9d, fontFamily: _fontFamily);
  static const IconData cookie_fill = IconData(0xfb9e, fontFamily: _fontFamily);
  static const IconData cooking_pot_fill = IconData(0xfb9f, fontFamily: _fontFamily);
  static const IconData copy_fill = IconData(0xfba0, fontFamily: _fontFamily);
  static const IconData copy_simple_fill = IconData(0xfba1, fontFamily: _fontFamily);
  static const IconData copyleft_fill = IconData(0xfba2, fontFamily: _fontFamily);
  static const IconData copyright_fill = IconData(0xfba3, fontFamily: _fontFamily);
  static const IconData corners_in_fill = IconData(0xfba4, fontFamily: _fontFamily);
  static const IconData corners_out_fill = IconData(0xfba5, fontFamily: _fontFamily);
  static const IconData cpu_fill = IconData(0xfba6, fontFamily: _fontFamily);
  static const IconData credit_card_fill = IconData(0xfba7, fontFamily: _fontFamily);
  static const IconData crop_fill = IconData(0xfba8, fontFamily: _fontFamily);
  static const IconData crosshair_fill = IconData(0xfba9, fontFamily: _fontFamily);
  static const IconData crosshair_simple_fill = IconData(0xfbaa, fontFamily: _fontFamily);
  static const IconData crown_fill = IconData(0xfbab, fontFamily: _fontFamily);
  static const IconData crown_simple_fill = IconData(0xfbac, fontFamily: _fontFamily);
  static const IconData cube_fill = IconData(0xfbad, fontFamily: _fontFamily);
  static const IconData currency_btc_fill = IconData(0xfbae, fontFamily: _fontFamily);
  static const IconData currency_circle_dollar_fill = IconData(0xfbaf, fontFamily: _fontFamily);
  static const IconData currency_cny_fill = IconData(0xfbb0, fontFamily: _fontFamily);
  static const IconData currency_dollar_fill = IconData(0xfbb1, fontFamily: _fontFamily);
  static const IconData currency_dollar_simple_fill = IconData(0xfbb2, fontFamily: _fontFamily);
  static const IconData currency_eth_fill = IconData(0xfbb3, fontFamily: _fontFamily);
  static const IconData currency_eur_fill = IconData(0xfbb4, fontFamily: _fontFamily);
  static const IconData currency_gbp_fill = IconData(0xfbb5, fontFamily: _fontFamily);
  static const IconData currency_inr_fill = IconData(0xfbb6, fontFamily: _fontFamily);
  static const IconData currency_jpy_fill = IconData(0xfbb7, fontFamily: _fontFamily);
  static const IconData currency_krw_fill = IconData(0xfbb8, fontFamily: _fontFamily);
  static const IconData currency_kzt_fill = IconData(0xfbb9, fontFamily: _fontFamily);
  static const IconData currency_ngn_fill = IconData(0xfbba, fontFamily: _fontFamily);
  static const IconData currency_rub_fill = IconData(0xfbbb, fontFamily: _fontFamily);
  static const IconData cursor_fill = IconData(0xfbbc, fontFamily: _fontFamily);
  static const IconData cursor_text_fill = IconData(0xfbbd, fontFamily: _fontFamily);
  static const IconData cylinder_fill = IconData(0xfbbe, fontFamily: _fontFamily);
  static const IconData database_fill = IconData(0xfbbf, fontFamily: _fontFamily);
  static const IconData desktop_fill = IconData(0xfbc0, fontFamily: _fontFamily);
  static const IconData desktop_tower_fill = IconData(0xfbc1, fontFamily: _fontFamily);
  static const IconData detective_fill = IconData(0xfbc2, fontFamily: _fontFamily);
  static const IconData device_mobile_camera_fill = IconData(0xfbc3, fontFamily: _fontFamily);
  static const IconData device_mobile_fill = IconData(0xfbc4, fontFamily: _fontFamily);
  static const IconData device_mobile_speaker_fill = IconData(0xfbc5, fontFamily: _fontFamily);
  static const IconData device_tablet_camera_fill = IconData(0xfbc6, fontFamily: _fontFamily);
  static const IconData device_tablet_fill = IconData(0xfbc7, fontFamily: _fontFamily);
  static const IconData device_tablet_speaker_fill = IconData(0xfbc8, fontFamily: _fontFamily);
  static const IconData diamond_fill = IconData(0xfbc9, fontFamily: _fontFamily);
  static const IconData diamonds_four_fill = IconData(0xfbca, fontFamily: _fontFamily);
  static const IconData dice_five_fill = IconData(0xfbcb, fontFamily: _fontFamily);
  static const IconData dice_four_fill = IconData(0xfbcc, fontFamily: _fontFamily);
  static const IconData dice_one_fill = IconData(0xfbcd, fontFamily: _fontFamily);
  static const IconData dice_six_fill = IconData(0xfbce, fontFamily: _fontFamily);
  static const IconData dice_three_fill = IconData(0xfbcf, fontFamily: _fontFamily);
  static const IconData dice_two_fill = IconData(0xfbd0, fontFamily: _fontFamily);
  static const IconData disc_fill = IconData(0xfbd1, fontFamily: _fontFamily);
  static const IconData discord_logo_fill = IconData(0xfbd2, fontFamily: _fontFamily);
  static const IconData divide_fill = IconData(0xfbd3, fontFamily: _fontFamily);
  static const IconData dog_fill = IconData(0xfbd4, fontFamily: _fontFamily);
  static const IconData door_fill = IconData(0xfbd5, fontFamily: _fontFamily);
  static const IconData dots_nine_fill = IconData(0xfbd6, fontFamily: _fontFamily);
  static const IconData dots_six_fill = IconData(0xfbd7, fontFamily: _fontFamily);
  static const IconData dots_six_vertical_fill = IconData(0xfbd8, fontFamily: _fontFamily);
  static const IconData dots_three_circle_fill = IconData(0xfbd9, fontFamily: _fontFamily);
  static const IconData dots_three_circle_vertical_fill = IconData(0xfbda, fontFamily: _fontFamily);
  static const IconData dots_three_fill = IconData(0xfbdb, fontFamily: _fontFamily);
  static const IconData dots_three_outline_fill = IconData(0xfbdc, fontFamily: _fontFamily);
  static const IconData dots_three_outline_vertical_fill = IconData(0xfbdd, fontFamily: _fontFamily);
  static const IconData dots_three_vertical_fill = IconData(0xfbde, fontFamily: _fontFamily);
  static const IconData download_fill = IconData(0xfbdf, fontFamily: _fontFamily);
  static const IconData download_simple_fill = IconData(0xfbe0, fontFamily: _fontFamily);
  static const IconData dribbble_logo_fill = IconData(0xfbe1, fontFamily: _fontFamily);
  static const IconData drop_fill = IconData(0xfbe2, fontFamily: _fontFamily);
  static const IconData drop_half_bottom_fill = IconData(0xfbe3, fontFamily: _fontFamily);
  static const IconData drop_half_fill = IconData(0xfbe4, fontFamily: _fontFamily);
  static const IconData ear_fill = IconData(0xfbe5, fontFamily: _fontFamily);
  static const IconData ear_slash_fill = IconData(0xfbe6, fontFamily: _fontFamily);
  static const IconData egg_crack_fill = IconData(0xfbe7, fontFamily: _fontFamily);
  static const IconData egg_fill = IconData(0xfbe8, fontFamily: _fontFamily);
  static const IconData eject_fill = IconData(0xfbe9, fontFamily: _fontFamily);
  static const IconData eject_simple_fill = IconData(0xfbea, fontFamily: _fontFamily);
  static const IconData envelope_fill = IconData(0xfbeb, fontFamily: _fontFamily);
  static const IconData envelope_open_fill = IconData(0xfbec, fontFamily: _fontFamily);
  static const IconData envelope_simple_fill = IconData(0xfbed, fontFamily: _fontFamily);
  static const IconData envelope_simple_open_fill = IconData(0xfbee, fontFamily: _fontFamily);
  static const IconData equalizer_fill = IconData(0xfbef, fontFamily: _fontFamily);
  static const IconData equals_fill = IconData(0xfbf0, fontFamily: _fontFamily);
  static const IconData eraser_fill = IconData(0xfbf1, fontFamily: _fontFamily);
  static const IconData exam_fill = IconData(0xfbf2, fontFamily: _fontFamily);
  static const IconData export_fill = IconData(0xfbf3, fontFamily: _fontFamily);
  static const IconData eye_closed_fill = IconData(0xfbf4, fontFamily: _fontFamily);
  static const IconData eye_fill = IconData(0xfbf5, fontFamily: _fontFamily);
  static const IconData eye_slash_fill = IconData(0xfbf6, fontFamily: _fontFamily);
  static const IconData eyedropper_fill = IconData(0xfbf7, fontFamily: _fontFamily);
  static const IconData eyedropper_sample_fill = IconData(0xfbf8, fontFamily: _fontFamily);
  static const IconData eyeglasses_fill = IconData(0xfbf9, fontFamily: _fontFamily);
  static const IconData face_mask_fill = IconData(0xfbfa, fontFamily: _fontFamily);
  static const IconData facebook_logo_fill = IconData(0xfbfb, fontFamily: _fontFamily);
  static const IconData factory_fill = IconData(0xfbfc, fontFamily: _fontFamily);
  static const IconData faders_fill = IconData(0xfbfd, fontFamily: _fontFamily);
  static const IconData faders_horizontal_fill = IconData(0xfbfe, fontFamily: _fontFamily);
  static const IconData fast_forward_circle_fill = IconData(0xfbff, fontFamily: _fontFamily);
  static const IconData fast_forward_fill = IconData(0xfc00, fontFamily: _fontFamily);
  static const IconData figma_logo_fill = IconData(0xfc01, fontFamily: _fontFamily);
  static const IconData file_arrow_down_fill = IconData(0xfc02, fontFamily: _fontFamily);
  static const IconData file_arrow_up_fill = IconData(0xfc03, fontFamily: _fontFamily);
  static const IconData file_audio_fill = IconData(0xfc04, fontFamily: _fontFamily);
  static const IconData file_cloud_fill = IconData(0xfc05, fontFamily: _fontFamily);
  static const IconData file_code_fill = IconData(0xfc06, fontFamily: _fontFamily);
  static const IconData file_css_fill = IconData(0xfc07, fontFamily: _fontFamily);
  static const IconData file_csv_fill = IconData(0xfc08, fontFamily: _fontFamily);
  static const IconData file_doc_fill = IconData(0xfc09, fontFamily: _fontFamily);
  static const IconData file_dotted_fill = IconData(0xfc0a, fontFamily: _fontFamily);
  static const IconData file_fill = IconData(0xfc0b, fontFamily: _fontFamily);
  static const IconData file_html_fill = IconData(0xfc0c, fontFamily: _fontFamily);
  static const IconData file_image_fill = IconData(0xfc0d, fontFamily: _fontFamily);
  static const IconData file_jpg_fill = IconData(0xfc0e, fontFamily: _fontFamily);
  static const IconData file_js_fill = IconData(0xfc0f, fontFamily: _fontFamily);
  static const IconData file_jsx_fill = IconData(0xfc10, fontFamily: _fontFamily);
  static const IconData file_lock_fill = IconData(0xfc11, fontFamily: _fontFamily);
  static const IconData file_minus_fill = IconData(0xfc12, fontFamily: _fontFamily);
  static const IconData file_pdf_fill = IconData(0xfc13, fontFamily: _fontFamily);
  static const IconData file_plus_fill = IconData(0xfc14, fontFamily: _fontFamily);
  static const IconData file_png_fill = IconData(0xfc15, fontFamily: _fontFamily);
  static const IconData file_ppt_fill = IconData(0xfc16, fontFamily: _fontFamily);
  static const IconData file_rs_fill = IconData(0xfc17, fontFamily: _fontFamily);
  static const IconData file_search_fill = IconData(0xfc18, fontFamily: _fontFamily);
  static const IconData file_text_fill = IconData(0xfc19, fontFamily: _fontFamily);
  static const IconData file_ts_fill = IconData(0xfc1a, fontFamily: _fontFamily);
  static const IconData file_tsx_fill = IconData(0xfc1b, fontFamily: _fontFamily);
  static const IconData file_video_fill = IconData(0xfc1c, fontFamily: _fontFamily);
  static const IconData file_vue_fill = IconData(0xfc1d, fontFamily: _fontFamily);
  static const IconData file_x_fill = IconData(0xfc1e, fontFamily: _fontFamily);
  static const IconData file_xls_fill = IconData(0xfc1f, fontFamily: _fontFamily);
  static const IconData file_zip_fill = IconData(0xfc20, fontFamily: _fontFamily);
  static const IconData files_fill = IconData(0xfc21, fontFamily: _fontFamily);
  static const IconData film_script_fill = IconData(0xfc22, fontFamily: _fontFamily);
  static const IconData film_slate_fill = IconData(0xfc23, fontFamily: _fontFamily);
  static const IconData film_strip_fill = IconData(0xfc24, fontFamily: _fontFamily);
  static const IconData fingerprint_fill = IconData(0xfc25, fontFamily: _fontFamily);
  static const IconData fingerprint_simple_fill = IconData(0xfc26, fontFamily: _fontFamily);
  static const IconData finn_the_human_fill = IconData(0xfc27, fontFamily: _fontFamily);
  static const IconData fire_fill = IconData(0xfc28, fontFamily: _fontFamily);
  static const IconData fire_simple_fill = IconData(0xfc29, fontFamily: _fontFamily);
  static const IconData first_aid_fill = IconData(0xfc2a, fontFamily: _fontFamily);
  static const IconData first_aid_kit_fill = IconData(0xfc2b, fontFamily: _fontFamily);
  static const IconData fish_fill = IconData(0xfc2c, fontFamily: _fontFamily);
  static const IconData fish_simple_fill = IconData(0xfc2d, fontFamily: _fontFamily);
  static const IconData flag_banner_fill = IconData(0xfc2e, fontFamily: _fontFamily);
  static const IconData flag_checkered_fill = IconData(0xfc2f, fontFamily: _fontFamily);
  static const IconData flag_fill = IconData(0xfc30, fontFamily: _fontFamily);
  static const IconData flame_fill = IconData(0xfc31, fontFamily: _fontFamily);
  static const IconData flashlight_fill = IconData(0xfc32, fontFamily: _fontFamily);
  static const IconData flask_fill = IconData(0xfc33, fontFamily: _fontFamily);
  static const IconData floppy_disk_back_fill = IconData(0xfc34, fontFamily: _fontFamily);
  static const IconData floppy_disk_fill = IconData(0xfc35, fontFamily: _fontFamily);
  static const IconData flow_arrow_fill = IconData(0xfc36, fontFamily: _fontFamily);
  static const IconData flower_fill = IconData(0xfc37, fontFamily: _fontFamily);
  static const IconData flower_lotus_fill = IconData(0xfc38, fontFamily: _fontFamily);
  static const IconData flying_saucer_fill = IconData(0xfc39, fontFamily: _fontFamily);
  static const IconData folder_dotted_fill = IconData(0xfc3a, fontFamily: _fontFamily);
  static const IconData folder_fill = IconData(0xfc3b, fontFamily: _fontFamily);
  static const IconData folder_lock_fill = IconData(0xfc3c, fontFamily: _fontFamily);
  static const IconData folder_minus_fill = IconData(0xfc3d, fontFamily: _fontFamily);
  static const IconData folder_notch_fill = IconData(0xfc3e, fontFamily: _fontFamily);
  static const IconData folder_notch_minus_fill = IconData(0xfc3f, fontFamily: _fontFamily);
  static const IconData folder_notch_open_fill = IconData(0xfc40, fontFamily: _fontFamily);
  static const IconData folder_notch_plus_fill = IconData(0xfc41, fontFamily: _fontFamily);
  static const IconData folder_open_fill = IconData(0xfc42, fontFamily: _fontFamily);
  static const IconData folder_plus_fill = IconData(0xfc43, fontFamily: _fontFamily);
  static const IconData folder_simple_dotted_fill = IconData(0xfc44, fontFamily: _fontFamily);
  static const IconData folder_simple_fill = IconData(0xfc45, fontFamily: _fontFamily);
  static const IconData folder_simple_lock_fill = IconData(0xfc46, fontFamily: _fontFamily);
  static const IconData folder_simple_minus_fill = IconData(0xfc47, fontFamily: _fontFamily);
  static const IconData folder_simple_plus_fill = IconData(0xfc48, fontFamily: _fontFamily);
  static const IconData folder_simple_star_fill = IconData(0xfc49, fontFamily: _fontFamily);
  static const IconData folder_simple_user_fill = IconData(0xfc4a, fontFamily: _fontFamily);
  static const IconData folder_star_fill = IconData(0xfc4b, fontFamily: _fontFamily);
  static const IconData folder_user_fill = IconData(0xfc4c, fontFamily: _fontFamily);
  static const IconData folders_fill = IconData(0xfc4d, fontFamily: _fontFamily);
  static const IconData football_fill = IconData(0xfc4e, fontFamily: _fontFamily);
  static const IconData fork_knife_fill = IconData(0xfc4f, fontFamily: _fontFamily);
  static const IconData frame_corners_fill = IconData(0xfc50, fontFamily: _fontFamily);
  static const IconData framer_logo_fill = IconData(0xfc51, fontFamily: _fontFamily);
  static const IconData function_fill = IconData(0xfc52, fontFamily: _fontFamily);
  static const IconData funnel_fill = IconData(0xfc53, fontFamily: _fontFamily);
  static const IconData funnel_simple_fill = IconData(0xfc54, fontFamily: _fontFamily);
  static const IconData game_controller_fill = IconData(0xfc55, fontFamily: _fontFamily);
  static const IconData gas_pump_fill = IconData(0xfc56, fontFamily: _fontFamily);
  static const IconData gauge_fill = IconData(0xfc57, fontFamily: _fontFamily);
  static const IconData gear_fill = IconData(0xfc58, fontFamily: _fontFamily);
  static const IconData gear_six_fill = IconData(0xfc59, fontFamily: _fontFamily);
  static const IconData gender_female_fill = IconData(0xfc5a, fontFamily: _fontFamily);
  static const IconData gender_intersex_fill = IconData(0xfc5b, fontFamily: _fontFamily);
  static const IconData gender_male_fill = IconData(0xfc5c, fontFamily: _fontFamily);
  static const IconData gender_neuter_fill = IconData(0xfc5d, fontFamily: _fontFamily);
  static const IconData gender_nonbinary_fill = IconData(0xfc5e, fontFamily: _fontFamily);
  static const IconData gender_transgender_fill = IconData(0xfc5f, fontFamily: _fontFamily);
  static const IconData ghost_fill = IconData(0xfc60, fontFamily: _fontFamily);
  static const IconData gif_fill = IconData(0xfc61, fontFamily: _fontFamily);
  static const IconData gift_fill = IconData(0xfc62, fontFamily: _fontFamily);
  static const IconData git_branch_fill = IconData(0xfc63, fontFamily: _fontFamily);
  static const IconData git_commit_fill = IconData(0xfc64, fontFamily: _fontFamily);
  static const IconData git_diff_fill = IconData(0xfc65, fontFamily: _fontFamily);
  static const IconData git_fork_fill = IconData(0xfc66, fontFamily: _fontFamily);
  static const IconData git_merge_fill = IconData(0xfc67, fontFamily: _fontFamily);
  static const IconData git_pull_request_fill = IconData(0xfc68, fontFamily: _fontFamily);
  static const IconData github_logo_fill = IconData(0xfc69, fontFamily: _fontFamily);
  static const IconData gitlab_logo_fill = IconData(0xfc6a, fontFamily: _fontFamily);
  static const IconData gitlab_logo_simple_fill = IconData(0xfc6b, fontFamily: _fontFamily);
  static const IconData globe_fill = IconData(0xfc6c, fontFamily: _fontFamily);
  static const IconData globe_hemisphere_east_fill = IconData(0xfc6d, fontFamily: _fontFamily);
  static const IconData globe_hemisphere_west_fill = IconData(0xfc6e, fontFamily: _fontFamily);
  static const IconData globe_simple_fill = IconData(0xfc6f, fontFamily: _fontFamily);
  static const IconData globe_stand_fill = IconData(0xfc70, fontFamily: _fontFamily);
  static const IconData google_chrome_logo_fill = IconData(0xfc71, fontFamily: _fontFamily);
  static const IconData google_logo_fill = IconData(0xfc72, fontFamily: _fontFamily);
  static const IconData google_photos_logo_fill = IconData(0xfc73, fontFamily: _fontFamily);
  static const IconData google_play_logo_fill = IconData(0xfc74, fontFamily: _fontFamily);
  static const IconData google_podcasts_logo_fill = IconData(0xfc75, fontFamily: _fontFamily);
  static const IconData gradient_fill = IconData(0xfc76, fontFamily: _fontFamily);
  static const IconData graduation_cap_fill = IconData(0xfc77, fontFamily: _fontFamily);
  static const IconData graph_fill = IconData(0xfc78, fontFamily: _fontFamily);
  static const IconData grid_four_fill = IconData(0xfc79, fontFamily: _fontFamily);
  static const IconData hamburger_fill = IconData(0xfc7a, fontFamily: _fontFamily);
  static const IconData hand_eye_fill = IconData(0xfc7b, fontFamily: _fontFamily);
  static const IconData hand_fill = IconData(0xfc7c, fontFamily: _fontFamily);
  static const IconData hand_fist_fill = IconData(0xfc7d, fontFamily: _fontFamily);
  static const IconData hand_grabbing_fill = IconData(0xfc7e, fontFamily: _fontFamily);
  static const IconData hand_palm_fill = IconData(0xfc7f, fontFamily: _fontFamily);
  static const IconData hand_pointing_fill = IconData(0xfc80, fontFamily: _fontFamily);
  static const IconData hand_soap_fill = IconData(0xfc81, fontFamily: _fontFamily);
  static const IconData hand_waving_fill = IconData(0xfc82, fontFamily: _fontFamily);
  static const IconData handbag_fill = IconData(0xfc83, fontFamily: _fontFamily);
  static const IconData handbag_simple_fill = IconData(0xfc84, fontFamily: _fontFamily);
  static const IconData hands_clapping_fill = IconData(0xfc85, fontFamily: _fontFamily);
  static const IconData handshake_fill = IconData(0xfc86, fontFamily: _fontFamily);
  static const IconData hard_drive_fill = IconData(0xfc87, fontFamily: _fontFamily);
  static const IconData hard_drives_fill = IconData(0xfc88, fontFamily: _fontFamily);
  static const IconData hash_fill = IconData(0xfc89, fontFamily: _fontFamily);
  static const IconData hash_straight_fill = IconData(0xfc8a, fontFamily: _fontFamily);
  static const IconData headlights_fill = IconData(0xfc8b, fontFamily: _fontFamily);
  static const IconData headphones_fill = IconData(0xfc8c, fontFamily: _fontFamily);
  static const IconData headset_fill = IconData(0xfc8d, fontFamily: _fontFamily);
  static const IconData heart_break_fill = IconData(0xfc8e, fontFamily: _fontFamily);
  static const IconData heart_fill = IconData(0xfc8f, fontFamily: _fontFamily);
  static const IconData heart_straight_break_fill = IconData(0xfc90, fontFamily: _fontFamily);
  static const IconData heart_straight_fill = IconData(0xfc91, fontFamily: _fontFamily);
  static const IconData heartbeat_fill = IconData(0xfc92, fontFamily: _fontFamily);
  static const IconData hexagon_fill = IconData(0xfc93, fontFamily: _fontFamily);
  static const IconData highlighter_circle_fill = IconData(0xfc94, fontFamily: _fontFamily);
  static const IconData horse_fill = IconData(0xfc95, fontFamily: _fontFamily);
  static const IconData hourglass_fill = IconData(0xfc96, fontFamily: _fontFamily);
  static const IconData hourglass_high_fill = IconData(0xfc97, fontFamily: _fontFamily);
  static const IconData hourglass_low_fill = IconData(0xfc98, fontFamily: _fontFamily);
  static const IconData hourglass_medium_fill = IconData(0xfc99, fontFamily: _fontFamily);
  static const IconData hourglass_simple_fill = IconData(0xfc9a, fontFamily: _fontFamily);
  static const IconData hourglass_simple_high_fill = IconData(0xfc9b, fontFamily: _fontFamily);
  static const IconData hourglass_simple_low_fill = IconData(0xfc9c, fontFamily: _fontFamily);
  static const IconData hourglass_simple_medium_fill = IconData(0xfc9d, fontFamily: _fontFamily);
  static const IconData house_fill = IconData(0xfc9e, fontFamily: _fontFamily);
  static const IconData house_line_fill = IconData(0xfc9f, fontFamily: _fontFamily);
  static const IconData house_simple_fill = IconData(0xfca0, fontFamily: _fontFamily);
  static const IconData identification_badge_fill = IconData(0xfca1, fontFamily: _fontFamily);
  static const IconData identification_card_fill = IconData(0xfca2, fontFamily: _fontFamily);
  static const IconData image_fill = IconData(0xfca3, fontFamily: _fontFamily);
  static const IconData image_square_fill = IconData(0xfca4, fontFamily: _fontFamily);
  static const IconData infinity_fill = IconData(0xfca5, fontFamily: _fontFamily);
  static const IconData info_fill = IconData(0xfca6, fontFamily: _fontFamily);
  static const IconData instagram_logo_fill = IconData(0xfca7, fontFamily: _fontFamily);
  static const IconData intersect_fill = IconData(0xfca8, fontFamily: _fontFamily);
  static const IconData jeep_fill = IconData(0xfca9, fontFamily: _fontFamily);
  static const IconData kanban_fill = IconData(0xfcaa, fontFamily: _fontFamily);
  static const IconData key_fill = IconData(0xfcab, fontFamily: _fontFamily);
  static const IconData key_return_fill = IconData(0xfcac, fontFamily: _fontFamily);
  static const IconData keyboard_fill = IconData(0xfcad, fontFamily: _fontFamily);
  static const IconData keyhole_fill = IconData(0xfcae, fontFamily: _fontFamily);
  static const IconData knife_fill = IconData(0xfcaf, fontFamily: _fontFamily);
  static const IconData ladder_fill = IconData(0xfcb0, fontFamily: _fontFamily);
  static const IconData ladder_simple_fill = IconData(0xfcb1, fontFamily: _fontFamily);
  static const IconData lamp_fill = IconData(0xfcb2, fontFamily: _fontFamily);
  static const IconData laptop_fill = IconData(0xfcb3, fontFamily: _fontFamily);
  static const IconData layout_fill = IconData(0xfcb4, fontFamily: _fontFamily);
  static const IconData leaf_fill = IconData(0xfcb5, fontFamily: _fontFamily);
  static const IconData lifebuoy_fill = IconData(0xfcb6, fontFamily: _fontFamily);
  static const IconData lightbulb_filament_fill = IconData(0xfcb7, fontFamily: _fontFamily);
  static const IconData lightbulb_fill = IconData(0xfcb8, fontFamily: _fontFamily);
  static const IconData lightning_fill = IconData(0xfcb9, fontFamily: _fontFamily);
  static const IconData lightning_slash_fill = IconData(0xfcba, fontFamily: _fontFamily);
  static const IconData line_segment_fill = IconData(0xfcbb, fontFamily: _fontFamily);
  static const IconData line_segments_fill = IconData(0xfcbc, fontFamily: _fontFamily);
  static const IconData link_break_fill = IconData(0xfcbd, fontFamily: _fontFamily);
  static const IconData link_fill = IconData(0xfcbe, fontFamily: _fontFamily);
  static const IconData link_simple_break_fill = IconData(0xfcbf, fontFamily: _fontFamily);
  static const IconData link_simple_fill = IconData(0xfcc0, fontFamily: _fontFamily);
  static const IconData link_simple_horizontal_break_fill = IconData(0xfcc1, fontFamily: _fontFamily);
  static const IconData link_simple_horizontal_fill = IconData(0xfcc2, fontFamily: _fontFamily);
  static const IconData linkedin_logo_fill = IconData(0xfcc3, fontFamily: _fontFamily);
  static const IconData linux_logo_fill = IconData(0xfcc4, fontFamily: _fontFamily);
  static const IconData list_bullets_fill = IconData(0xfcc5, fontFamily: _fontFamily);
  static const IconData list_checks_fill = IconData(0xfcc6, fontFamily: _fontFamily);
  static const IconData list_dashes_fill = IconData(0xfcc7, fontFamily: _fontFamily);
  static const IconData list_fill = IconData(0xfcc8, fontFamily: _fontFamily);
  static const IconData list_numbers_fill = IconData(0xfcc9, fontFamily: _fontFamily);
  static const IconData list_plus_fill = IconData(0xfcca, fontFamily: _fontFamily);
  static const IconData lock_fill = IconData(0xfccb, fontFamily: _fontFamily);
  static const IconData lock_key_fill = IconData(0xfccc, fontFamily: _fontFamily);
  static const IconData lock_key_open_fill = IconData(0xfccd, fontFamily: _fontFamily);
  static const IconData lock_laminated_fill = IconData(0xfcce, fontFamily: _fontFamily);
  static const IconData lock_laminated_open_fill = IconData(0xfccf, fontFamily: _fontFamily);
  static const IconData lock_open_fill = IconData(0xfcd0, fontFamily: _fontFamily);
  static const IconData lock_simple_fill = IconData(0xfcd1, fontFamily: _fontFamily);
  static const IconData lock_simple_open_fill = IconData(0xfcd2, fontFamily: _fontFamily);
  static const IconData magic_wand_fill = IconData(0xfcd3, fontFamily: _fontFamily);
  static const IconData magnet_fill = IconData(0xfcd4, fontFamily: _fontFamily);
  static const IconData magnet_straight_fill = IconData(0xfcd5, fontFamily: _fontFamily);
  static const IconData magnifying_glass_fill = IconData(0xfcd6, fontFamily: _fontFamily);
  static const IconData magnifying_glass_minus_fill = IconData(0xfcd7, fontFamily: _fontFamily);
  static const IconData magnifying_glass_plus_fill = IconData(0xfcd8, fontFamily: _fontFamily);
  static const IconData map_pin_fill = IconData(0xfcd9, fontFamily: _fontFamily);
  static const IconData map_pin_line_fill = IconData(0xfcda, fontFamily: _fontFamily);
  static const IconData map_trifold_fill = IconData(0xfcdb, fontFamily: _fontFamily);
  static const IconData marker_circle_fill = IconData(0xfcdc, fontFamily: _fontFamily);
  static const IconData martini_fill = IconData(0xfcdd, fontFamily: _fontFamily);
  static const IconData mask_happy_fill = IconData(0xfcde, fontFamily: _fontFamily);
  static const IconData mask_sad_fill = IconData(0xfcdf, fontFamily: _fontFamily);
  static const IconData math_operations_fill = IconData(0xfce0, fontFamily: _fontFamily);
  static const IconData medal_fill = IconData(0xfce1, fontFamily: _fontFamily);
  static const IconData medium_logo_fill = IconData(0xfce2, fontFamily: _fontFamily);
  static const IconData megaphone_fill = IconData(0xfce3, fontFamily: _fontFamily);
  static const IconData megaphone_simple_fill = IconData(0xfce4, fontFamily: _fontFamily);
  static const IconData messenger_logo_fill = IconData(0xfce5, fontFamily: _fontFamily);
  static const IconData microphone_fill = IconData(0xfce6, fontFamily: _fontFamily);
  static const IconData microphone_slash_fill = IconData(0xfce7, fontFamily: _fontFamily);
  static const IconData microphone_stage_fill = IconData(0xfce8, fontFamily: _fontFamily);
  static const IconData microsoft_excel_logo_fill = IconData(0xfce9, fontFamily: _fontFamily);
  static const IconData microsoft_powerpoint_logo_fill = IconData(0xfcea, fontFamily: _fontFamily);
  static const IconData microsoft_teams_logo_fill = IconData(0xfceb, fontFamily: _fontFamily);
  static const IconData microsoft_word_logo_fill = IconData(0xfcec, fontFamily: _fontFamily);
  static const IconData minus_circle_fill = IconData(0xfced, fontFamily: _fontFamily);
  static const IconData minus_fill = IconData(0xfcee, fontFamily: _fontFamily);
  static const IconData money_fill = IconData(0xfcef, fontFamily: _fontFamily);
  static const IconData monitor_fill = IconData(0xfcf0, fontFamily: _fontFamily);
  static const IconData monitor_play_fill = IconData(0xfcf1, fontFamily: _fontFamily);
  static const IconData moon_fill = IconData(0xfcf2, fontFamily: _fontFamily);
  static const IconData moon_stars_fill = IconData(0xfcf3, fontFamily: _fontFamily);
  static const IconData mountains_fill = IconData(0xfcf4, fontFamily: _fontFamily);
  static const IconData mouse_fill = IconData(0xfcf5, fontFamily: _fontFamily);
  static const IconData mouse_simple_fill = IconData(0xfcf6, fontFamily: _fontFamily);
  static const IconData music_note_fill = IconData(0xfcf7, fontFamily: _fontFamily);
  static const IconData music_note_simple_fill = IconData(0xfcf8, fontFamily: _fontFamily);
  static const IconData music_notes_fill = IconData(0xfcf9, fontFamily: _fontFamily);
  static const IconData music_notes_plus_fill = IconData(0xfcfa, fontFamily: _fontFamily);
  static const IconData music_notes_simple_fill = IconData(0xfcfb, fontFamily: _fontFamily);
  static const IconData navigation_arrow_fill = IconData(0xfcfc, fontFamily: _fontFamily);
  static const IconData needle_fill = IconData(0xfcfd, fontFamily: _fontFamily);
  static const IconData newspaper_clipping_fill = IconData(0xfcfe, fontFamily: _fontFamily);
  static const IconData newspaper_fill = IconData(0xfcff, fontFamily: _fontFamily);
  static const IconData note_blank_fill = IconData(0xfd00, fontFamily: _fontFamily);
  static const IconData note_fill = IconData(0xfd01, fontFamily: _fontFamily);
  static const IconData note_pencil_fill = IconData(0xfd02, fontFamily: _fontFamily);
  static const IconData notebook_fill = IconData(0xfd03, fontFamily: _fontFamily);
  static const IconData notepad_fill = IconData(0xfd04, fontFamily: _fontFamily);
  static const IconData notification_fill = IconData(0xfd05, fontFamily: _fontFamily);
  static const IconData number_circle_eight_fill = IconData(0xfd06, fontFamily: _fontFamily);
  static const IconData number_circle_five_fill = IconData(0xfd07, fontFamily: _fontFamily);
  static const IconData number_circle_four_fill = IconData(0xfd08, fontFamily: _fontFamily);
  static const IconData number_circle_nine_fill = IconData(0xfd09, fontFamily: _fontFamily);
  static const IconData number_circle_one_fill = IconData(0xfd0a, fontFamily: _fontFamily);
  static const IconData number_circle_seven_fill = IconData(0xfd0b, fontFamily: _fontFamily);
  static const IconData number_circle_six_fill = IconData(0xfd0c, fontFamily: _fontFamily);
  static const IconData number_circle_three_fill = IconData(0xfd0d, fontFamily: _fontFamily);
  static const IconData number_circle_two_fill = IconData(0xfd0e, fontFamily: _fontFamily);
  static const IconData number_circle_zero_fill = IconData(0xfd0f, fontFamily: _fontFamily);
  static const IconData number_eight_fill = IconData(0xfd10, fontFamily: _fontFamily);
  static const IconData number_five_fill = IconData(0xfd11, fontFamily: _fontFamily);
  static const IconData number_four_fill = IconData(0xfd12, fontFamily: _fontFamily);
  static const IconData number_nine_fill = IconData(0xfd13, fontFamily: _fontFamily);
  static const IconData number_one_fill = IconData(0xfd14, fontFamily: _fontFamily);
  static const IconData number_seven_fill = IconData(0xfd15, fontFamily: _fontFamily);
  static const IconData number_six_fill = IconData(0xfd16, fontFamily: _fontFamily);
  static const IconData number_square_eight_fill = IconData(0xfd17, fontFamily: _fontFamily);
  static const IconData number_square_five_fill = IconData(0xfd18, fontFamily: _fontFamily);
  static const IconData number_square_four_fill = IconData(0xfd19, fontFamily: _fontFamily);
  static const IconData number_square_nine_fill = IconData(0xfd1a, fontFamily: _fontFamily);
  static const IconData number_square_one_fill = IconData(0xfd1b, fontFamily: _fontFamily);
  static const IconData number_square_seven_fill = IconData(0xfd1c, fontFamily: _fontFamily);
  static const IconData number_square_six_fill = IconData(0xfd1d, fontFamily: _fontFamily);
  static const IconData number_square_three_fill = IconData(0xfd1e, fontFamily: _fontFamily);
  static const IconData number_square_two_fill = IconData(0xfd1f, fontFamily: _fontFamily);
  static const IconData number_square_zero_fill = IconData(0xfd20, fontFamily: _fontFamily);
  static const IconData number_three_fill = IconData(0xfd21, fontFamily: _fontFamily);
  static const IconData number_two_fill = IconData(0xfd22, fontFamily: _fontFamily);
  static const IconData number_zero_fill = IconData(0xfd23, fontFamily: _fontFamily);
  static const IconData nut_fill = IconData(0xfd24, fontFamily: _fontFamily);
  static const IconData ny_times_logo_fill = IconData(0xfd25, fontFamily: _fontFamily);
  static const IconData octagon_fill = IconData(0xfd26, fontFamily: _fontFamily);
  static const IconData option_fill = IconData(0xfd27, fontFamily: _fontFamily);
  static const IconData package_fill = IconData(0xfd28, fontFamily: _fontFamily);
  static const IconData paint_brush_broad_fill = IconData(0xfd29, fontFamily: _fontFamily);
  static const IconData paint_brush_fill = IconData(0xfd2a, fontFamily: _fontFamily);
  static const IconData paint_brush_household_fill = IconData(0xfd2b, fontFamily: _fontFamily);
  static const IconData paint_bucket_fill = IconData(0xfd2c, fontFamily: _fontFamily);
  static const IconData paint_roller_fill = IconData(0xfd2d, fontFamily: _fontFamily);
  static const IconData palette_fill = IconData(0xfd2e, fontFamily: _fontFamily);
  static const IconData paper_plane_fill = IconData(0xfd2f, fontFamily: _fontFamily);
  static const IconData paper_plane_right_fill = IconData(0xfd30, fontFamily: _fontFamily);
  static const IconData paper_plane_tilt_fill = IconData(0xfd31, fontFamily: _fontFamily);
  static const IconData paperclip_fill = IconData(0xfd32, fontFamily: _fontFamily);
  static const IconData paperclip_horizontal_fill = IconData(0xfd33, fontFamily: _fontFamily);
  static const IconData parachute_fill = IconData(0xfd34, fontFamily: _fontFamily);
  static const IconData password_fill = IconData(0xfd35, fontFamily: _fontFamily);
  static const IconData path_fill = IconData(0xfd36, fontFamily: _fontFamily);
  static const IconData pause_circle_fill = IconData(0xfd37, fontFamily: _fontFamily);
  static const IconData pause_fill = IconData(0xfd38, fontFamily: _fontFamily);
  static const IconData paw_print_fill = IconData(0xfd39, fontFamily: _fontFamily);
  static const IconData peace_fill = IconData(0xfd3a, fontFamily: _fontFamily);
  static const IconData pen_fill = IconData(0xfd3b, fontFamily: _fontFamily);
  static const IconData pen_nib_fill = IconData(0xfd3c, fontFamily: _fontFamily);
  static const IconData pen_nib_straight_fill = IconData(0xfd3d, fontFamily: _fontFamily);
  static const IconData pencil_circle_fill = IconData(0xfd3e, fontFamily: _fontFamily);
  static const IconData pencil_fill = IconData(0xfd3f, fontFamily: _fontFamily);
  static const IconData pencil_line_fill = IconData(0xfd40, fontFamily: _fontFamily);
  static const IconData pencil_simple_fill = IconData(0xfd41, fontFamily: _fontFamily);
  static const IconData pencil_simple_line_fill = IconData(0xfd42, fontFamily: _fontFamily);
  static const IconData percent_fill = IconData(0xfd43, fontFamily: _fontFamily);
  static const IconData person_fill = IconData(0xfd44, fontFamily: _fontFamily);
  static const IconData person_simple_fill = IconData(0xfd45, fontFamily: _fontFamily);
  static const IconData person_simple_run_fill = IconData(0xfd46, fontFamily: _fontFamily);
  static const IconData person_simple_walk_fill = IconData(0xfd47, fontFamily: _fontFamily);
  static const IconData perspective_fill = IconData(0xfd48, fontFamily: _fontFamily);
  static const IconData phone_call_fill = IconData(0xfd49, fontFamily: _fontFamily);
  static const IconData phone_disconnect_fill = IconData(0xfd4a, fontFamily: _fontFamily);
  static const IconData phone_fill = IconData(0xfd4b, fontFamily: _fontFamily);
  static const IconData phone_incoming_fill = IconData(0xfd4c, fontFamily: _fontFamily);
  static const IconData phone_outgoing_fill = IconData(0xfd4d, fontFamily: _fontFamily);
  static const IconData phone_slash_fill = IconData(0xfd4e, fontFamily: _fontFamily);
  static const IconData phone_x_fill = IconData(0xfd4f, fontFamily: _fontFamily);
  static const IconData phosphor_logo_fill = IconData(0xfd50, fontFamily: _fontFamily);
  static const IconData piano_keys_fill = IconData(0xfd51, fontFamily: _fontFamily);
  static const IconData picture_in_picture_fill = IconData(0xfd52, fontFamily: _fontFamily);
  static const IconData pill_fill = IconData(0xfd53, fontFamily: _fontFamily);
  static const IconData pinterest_logo_fill = IconData(0xfd54, fontFamily: _fontFamily);
  static const IconData pinwheel_fill = IconData(0xfd55, fontFamily: _fontFamily);
  static const IconData pizza_fill = IconData(0xfd56, fontFamily: _fontFamily);
  static const IconData placeholder_fill = IconData(0xfd57, fontFamily: _fontFamily);
  static const IconData planet_fill = IconData(0xfd58, fontFamily: _fontFamily);
  static const IconData play_circle_fill = IconData(0xfd59, fontFamily: _fontFamily);
  static const IconData play_fill = IconData(0xfd5a, fontFamily: _fontFamily);
  static const IconData playlist_fill = IconData(0xfd5b, fontFamily: _fontFamily);
  static const IconData plug_fill = IconData(0xfd5c, fontFamily: _fontFamily);
  static const IconData plugs_connected_fill = IconData(0xfd5d, fontFamily: _fontFamily);
  static const IconData plugs_fill = IconData(0xfd5e, fontFamily: _fontFamily);
  static const IconData plus_circle_fill = IconData(0xfd5f, fontFamily: _fontFamily);
  static const IconData plus_fill = IconData(0xfd60, fontFamily: _fontFamily);
  static const IconData plus_minus_fill = IconData(0xfd61, fontFamily: _fontFamily);
  static const IconData poker_chip_fill = IconData(0xfd62, fontFamily: _fontFamily);
  static const IconData police_car_fill = IconData(0xfd63, fontFamily: _fontFamily);
  static const IconData polygon_fill = IconData(0xfd64, fontFamily: _fontFamily);
  static const IconData popcorn_fill = IconData(0xfd65, fontFamily: _fontFamily);
  static const IconData power_fill = IconData(0xfd66, fontFamily: _fontFamily);
  static const IconData prescription_fill = IconData(0xfd67, fontFamily: _fontFamily);
  static const IconData presentation_chart_fill = IconData(0xfd68, fontFamily: _fontFamily);
  static const IconData presentation_fill = IconData(0xfd69, fontFamily: _fontFamily);
  static const IconData printer_fill = IconData(0xfd6a, fontFamily: _fontFamily);
  static const IconData prohibit_fill = IconData(0xfd6b, fontFamily: _fontFamily);
  static const IconData prohibit_inset_fill = IconData(0xfd6c, fontFamily: _fontFamily);
  static const IconData projector_screen_chart_fill = IconData(0xfd6d, fontFamily: _fontFamily);
  static const IconData projector_screen_fill = IconData(0xfd6e, fontFamily: _fontFamily);
  static const IconData push_pin_fill = IconData(0xfd6f, fontFamily: _fontFamily);
  static const IconData push_pin_simple_fill = IconData(0xfd70, fontFamily: _fontFamily);
  static const IconData push_pin_simple_slash_fill = IconData(0xfd71, fontFamily: _fontFamily);
  static const IconData push_pin_slash_fill = IconData(0xfd72, fontFamily: _fontFamily);
  static const IconData puzzle_piece_fill = IconData(0xfd73, fontFamily: _fontFamily);
  static const IconData qr_code_fill = IconData(0xfd74, fontFamily: _fontFamily);
  static const IconData question_fill = IconData(0xfd75, fontFamily: _fontFamily);
  static const IconData queue_fill = IconData(0xfd76, fontFamily: _fontFamily);
  static const IconData quotes_fill = IconData(0xfd77, fontFamily: _fontFamily);
  static const IconData radical_fill = IconData(0xfd78, fontFamily: _fontFamily);
  static const IconData radio_button_fill = IconData(0xfd79, fontFamily: _fontFamily);
  static const IconData radio_fill = IconData(0xfd7a, fontFamily: _fontFamily);
  static const IconData rainbow_cloud_fill = IconData(0xfd7b, fontFamily: _fontFamily);
  static const IconData rainbow_fill = IconData(0xfd7c, fontFamily: _fontFamily);
  static const IconData receipt_fill = IconData(0xfd7d, fontFamily: _fontFamily);
  static const IconData record_fill = IconData(0xfd7e, fontFamily: _fontFamily);
  static const IconData rectangle_fill = IconData(0xfd7f, fontFamily: _fontFamily);
  static const IconData recycle_fill = IconData(0xfd80, fontFamily: _fontFamily);
  static const IconData reddit_logo_fill = IconData(0xfd81, fontFamily: _fontFamily);
  static const IconData repeat_fill = IconData(0xfd82, fontFamily: _fontFamily);
  static const IconData repeat_once_fill = IconData(0xfd83, fontFamily: _fontFamily);
  static const IconData rewind_circle_fill = IconData(0xfd84, fontFamily: _fontFamily);
  static const IconData rewind_fill = IconData(0xfd85, fontFamily: _fontFamily);
  static const IconData robot_fill = IconData(0xfd86, fontFamily: _fontFamily);
  static const IconData rocket_fill = IconData(0xfd87, fontFamily: _fontFamily);
  static const IconData rocket_launch_fill = IconData(0xfd88, fontFamily: _fontFamily);
  static const IconData rows_fill = IconData(0xfd89, fontFamily: _fontFamily);
  static const IconData rss_fill = IconData(0xfd8a, fontFamily: _fontFamily);
  static const IconData rss_simple_fill = IconData(0xfd8b, fontFamily: _fontFamily);
  static const IconData rug_fill = IconData(0xfd8c, fontFamily: _fontFamily);
  static const IconData ruler_fill = IconData(0xfd8d, fontFamily: _fontFamily);
  static const IconData scales_fill = IconData(0xfd8e, fontFamily: _fontFamily);
  static const IconData scan_fill = IconData(0xfd8f, fontFamily: _fontFamily);
  static const IconData scissors_fill = IconData(0xfd90, fontFamily: _fontFamily);
  static const IconData screencast_fill = IconData(0xfd91, fontFamily: _fontFamily);
  static const IconData scribble_loop_fill = IconData(0xfd92, fontFamily: _fontFamily);
  static const IconData scroll_fill = IconData(0xfd93, fontFamily: _fontFamily);
  static const IconData selection_all_fill = IconData(0xfd94, fontFamily: _fontFamily);
  static const IconData selection_background_fill = IconData(0xfd95, fontFamily: _fontFamily);
  static const IconData selection_fill = IconData(0xfd96, fontFamily: _fontFamily);
  static const IconData selection_foreground_fill = IconData(0xfd97, fontFamily: _fontFamily);
  static const IconData selection_inverse_fill = IconData(0xfd98, fontFamily: _fontFamily);
  static const IconData selection_plus_fill = IconData(0xfd99, fontFamily: _fontFamily);
  static const IconData selection_slash_fill = IconData(0xfd9a, fontFamily: _fontFamily);
  static const IconData share_fill = IconData(0xfd9b, fontFamily: _fontFamily);
  static const IconData share_network_fill = IconData(0xfd9c, fontFamily: _fontFamily);
  static const IconData shield_check_fill = IconData(0xfd9d, fontFamily: _fontFamily);
  static const IconData shield_checkered_fill = IconData(0xfd9e, fontFamily: _fontFamily);
  static const IconData shield_chevron_fill = IconData(0xfd9f, fontFamily: _fontFamily);
  static const IconData shield_fill = IconData(0xfda0, fontFamily: _fontFamily);
  static const IconData shield_plus_fill = IconData(0xfda1, fontFamily: _fontFamily);
  static const IconData shield_slash_fill = IconData(0xfda2, fontFamily: _fontFamily);
  static const IconData shield_star_fill = IconData(0xfda3, fontFamily: _fontFamily);
  static const IconData shield_warning_fill = IconData(0xfda4, fontFamily: _fontFamily);
  static const IconData shopping_bag_fill = IconData(0xfda5, fontFamily: _fontFamily);
  static const IconData shopping_bag_open_fill = IconData(0xfda6, fontFamily: _fontFamily);
  static const IconData shopping_cart_fill = IconData(0xfda7, fontFamily: _fontFamily);
  static const IconData shopping_cart_simple_fill = IconData(0xfda8, fontFamily: _fontFamily);
  static const IconData shower_fill = IconData(0xfda9, fontFamily: _fontFamily);
  static const IconData shuffle_angular_fill = IconData(0xfdaa, fontFamily: _fontFamily);
  static const IconData shuffle_fill = IconData(0xfdab, fontFamily: _fontFamily);
  static const IconData shuffle_simple_fill = IconData(0xfdac, fontFamily: _fontFamily);
  static const IconData sidebar_fill = IconData(0xfdad, fontFamily: _fontFamily);
  static const IconData sidebar_simple_fill = IconData(0xfdae, fontFamily: _fontFamily);
  static const IconData sign_in_fill = IconData(0xfdaf, fontFamily: _fontFamily);
  static const IconData sign_out_fill = IconData(0xfdb0, fontFamily: _fontFamily);
  static const IconData signpost_fill = IconData(0xfdb1, fontFamily: _fontFamily);
  static const IconData sim_card_fill = IconData(0xfdb2, fontFamily: _fontFamily);
  static const IconData sketch_logo_fill = IconData(0xfdb3, fontFamily: _fontFamily);
  static const IconData skip_back_circle_fill = IconData(0xfdb4, fontFamily: _fontFamily);
  static const IconData skip_back_fill = IconData(0xfdb5, fontFamily: _fontFamily);
  static const IconData skip_forward_circle_fill = IconData(0xfdb6, fontFamily: _fontFamily);
  static const IconData skip_forward_fill = IconData(0xfdb7, fontFamily: _fontFamily);
  static const IconData skull_fill = IconData(0xfdb8, fontFamily: _fontFamily);
  static const IconData slack_logo_fill = IconData(0xfdb9, fontFamily: _fontFamily);
  static const IconData sliders_fill = IconData(0xfdba, fontFamily: _fontFamily);
  static const IconData sliders_horizontal_fill = IconData(0xfdbb, fontFamily: _fontFamily);
  static const IconData smiley_blank_fill = IconData(0xfdbc, fontFamily: _fontFamily);
  static const IconData smiley_fill = IconData(0xfdbd, fontFamily: _fontFamily);
  static const IconData smiley_meh_fill = IconData(0xfdbe, fontFamily: _fontFamily);
  static const IconData smiley_nervous_fill = IconData(0xfdbf, fontFamily: _fontFamily);
  static const IconData smiley_sad_fill = IconData(0xfdc0, fontFamily: _fontFamily);
  static const IconData smiley_sticker_fill = IconData(0xfdc1, fontFamily: _fontFamily);
  static const IconData smiley_wink_fill = IconData(0xfdc2, fontFamily: _fontFamily);
  static const IconData smiley_x_eyes_fill = IconData(0xfdc3, fontFamily: _fontFamily);
  static const IconData snapchat_logo_fill = IconData(0xfdc4, fontFamily: _fontFamily);
  static const IconData snowflake_fill = IconData(0xfdc5, fontFamily: _fontFamily);
  static const IconData soccer_ball_fill = IconData(0xfdc6, fontFamily: _fontFamily);
  static const IconData sort_ascending_fill = IconData(0xfdc7, fontFamily: _fontFamily);
  static const IconData sort_descending_fill = IconData(0xfdc8, fontFamily: _fontFamily);
  static const IconData spade_fill = IconData(0xfdc9, fontFamily: _fontFamily);
  static const IconData sparkle_fill = IconData(0xfdca, fontFamily: _fontFamily);
  static const IconData speaker_high_fill = IconData(0xfdcb, fontFamily: _fontFamily);
  static const IconData speaker_low_fill = IconData(0xfdcc, fontFamily: _fontFamily);
  static const IconData speaker_none_fill = IconData(0xfdcd, fontFamily: _fontFamily);
  static const IconData speaker_simple_high_fill = IconData(0xfdce, fontFamily: _fontFamily);
  static const IconData speaker_simple_low_fill = IconData(0xfdcf, fontFamily: _fontFamily);
  static const IconData speaker_simple_none_fill = IconData(0xfdd0, fontFamily: _fontFamily);
  static const IconData speaker_simple_slash_fill = IconData(0xfdd1, fontFamily: _fontFamily);
  static const IconData speaker_simple_x_fill = IconData(0xfdd2, fontFamily: _fontFamily);
  static const IconData speaker_slash_fill = IconData(0xfdd3, fontFamily: _fontFamily);
  static const IconData speaker_x_fill = IconData(0xfdd4, fontFamily: _fontFamily);
  static const IconData spinner_fill = IconData(0xfdd5, fontFamily: _fontFamily);
  static const IconData spinner_gap_fill = IconData(0xfdd6, fontFamily: _fontFamily);
  static const IconData spiral_fill = IconData(0xfdd7, fontFamily: _fontFamily);
  static const IconData spotify_logo_fill = IconData(0xfdd8, fontFamily: _fontFamily);
  static const IconData square_fill = IconData(0xfdd9, fontFamily: _fontFamily);
  static const IconData square_half_bottom_fill = IconData(0xfdda, fontFamily: _fontFamily);
  static const IconData square_half_fill = IconData(0xfddb, fontFamily: _fontFamily);
  static const IconData square_logo_fill = IconData(0xfddc, fontFamily: _fontFamily);
  static const IconData squares_four_fill = IconData(0xfddd, fontFamily: _fontFamily);
  static const IconData stack_fill = IconData(0xfdde, fontFamily: _fontFamily);
  static const IconData stack_overflow_logo_fill = IconData(0xfddf, fontFamily: _fontFamily);
  static const IconData stack_simple_fill = IconData(0xfde0, fontFamily: _fontFamily);
  static const IconData stamp_fill = IconData(0xfde1, fontFamily: _fontFamily);
  static const IconData star_fill = IconData(0xfde2, fontFamily: _fontFamily);
  static const IconData star_four_fill = IconData(0xfde3, fontFamily: _fontFamily);
  static const IconData star_half_fill = IconData(0xfde4, fontFamily: _fontFamily);
  static const IconData sticker_fill = IconData(0xfde5, fontFamily: _fontFamily);
  static const IconData stop_circle_fill = IconData(0xfde6, fontFamily: _fontFamily);
  static const IconData stop_fill = IconData(0xfde7, fontFamily: _fontFamily);
  static const IconData storefront_fill = IconData(0xfde8, fontFamily: _fontFamily);
  static const IconData strategy_fill = IconData(0xfde9, fontFamily: _fontFamily);
  static const IconData stripe_logo_fill = IconData(0xfdea, fontFamily: _fontFamily);
  static const IconData student_fill = IconData(0xfdeb, fontFamily: _fontFamily);
  static const IconData suitcase_fill = IconData(0xfdec, fontFamily: _fontFamily);
  static const IconData suitcase_simple_fill = IconData(0xfded, fontFamily: _fontFamily);
  static const IconData sun_dim_fill = IconData(0xfdee, fontFamily: _fontFamily);
  static const IconData sun_fill = IconData(0xfdef, fontFamily: _fontFamily);
  static const IconData sun_horizon_fill = IconData(0xfdf0, fontFamily: _fontFamily);
  static const IconData sunglasses_fill = IconData(0xfdf1, fontFamily: _fontFamily);
  static const IconData swap_fill = IconData(0xfdf2, fontFamily: _fontFamily);
  static const IconData swatches_fill = IconData(0xfdf3, fontFamily: _fontFamily);
  static const IconData sword_fill = IconData(0xfdf4, fontFamily: _fontFamily);
  static const IconData syringe_fill = IconData(0xfdf5, fontFamily: _fontFamily);
  static const IconData t_shirt_fill = IconData(0xfdf6, fontFamily: _fontFamily);
  static const IconData table_fill = IconData(0xfdf7, fontFamily: _fontFamily);
  static const IconData tabs_fill = IconData(0xfdf8, fontFamily: _fontFamily);
  static const IconData tag_chevron_fill = IconData(0xfdf9, fontFamily: _fontFamily);
  static const IconData tag_fill = IconData(0xfdfa, fontFamily: _fontFamily);
  static const IconData tag_simple_fill = IconData(0xfdfb, fontFamily: _fontFamily);
  static const IconData target_fill = IconData(0xfdfc, fontFamily: _fontFamily);
  static const IconData taxi_fill = IconData(0xfdfd, fontFamily: _fontFamily);
  static const IconData telegram_logo_fill = IconData(0xfdfe, fontFamily: _fontFamily);
  static const IconData television_fill = IconData(0xfdff, fontFamily: _fontFamily);
  static const IconData television_simple_fill = IconData(0xfe00, fontFamily: _fontFamily);
  static const IconData tennis_ball_fill = IconData(0xfe01, fontFamily: _fontFamily);
  static const IconData terminal_fill = IconData(0xfe02, fontFamily: _fontFamily);
  static const IconData terminal_window_fill = IconData(0xfe03, fontFamily: _fontFamily);
  static const IconData test_tube_fill = IconData(0xfe04, fontFamily: _fontFamily);
  static const IconData text_aa_fill = IconData(0xfe05, fontFamily: _fontFamily);
  static const IconData text_align_center_fill = IconData(0xfe06, fontFamily: _fontFamily);
  static const IconData text_align_justify_fill = IconData(0xfe07, fontFamily: _fontFamily);
  static const IconData text_align_left_fill = IconData(0xfe08, fontFamily: _fontFamily);
  static const IconData text_align_right_fill = IconData(0xfe09, fontFamily: _fontFamily);
  static const IconData text_bolder_fill = IconData(0xfe0a, fontFamily: _fontFamily);
  static const IconData text_h_fill = IconData(0xfe0b, fontFamily: _fontFamily);
  static const IconData text_h_five_fill = IconData(0xfe0c, fontFamily: _fontFamily);
  static const IconData text_h_four_fill = IconData(0xfe0d, fontFamily: _fontFamily);
  static const IconData text_h_one_fill = IconData(0xfe0e, fontFamily: _fontFamily);
  static const IconData text_h_six_fill = IconData(0xfe0f, fontFamily: _fontFamily);
  static const IconData text_h_three_fill = IconData(0xfe10, fontFamily: _fontFamily);
  static const IconData text_h_two_fill = IconData(0xfe11, fontFamily: _fontFamily);
  static const IconData text_indent_fill = IconData(0xfe12, fontFamily: _fontFamily);
  static const IconData text_italic_fill = IconData(0xfe13, fontFamily: _fontFamily);
  static const IconData text_outdent_fill = IconData(0xfe14, fontFamily: _fontFamily);
  static const IconData text_strikethrough_fill = IconData(0xfe15, fontFamily: _fontFamily);
  static const IconData text_t_fill = IconData(0xfe16, fontFamily: _fontFamily);
  static const IconData text_underline_fill = IconData(0xfe17, fontFamily: _fontFamily);
  static const IconData textbox_fill = IconData(0xfe18, fontFamily: _fontFamily);
  static const IconData thermometer_cold_fill = IconData(0xfe19, fontFamily: _fontFamily);
  static const IconData thermometer_fill = IconData(0xfe1a, fontFamily: _fontFamily);
  static const IconData thermometer_hot_fill = IconData(0xfe1b, fontFamily: _fontFamily);
  static const IconData thermometer_simple_fill = IconData(0xfe1c, fontFamily: _fontFamily);
  static const IconData thumbs_down_fill = IconData(0xfe1d, fontFamily: _fontFamily);
  static const IconData thumbs_up_fill = IconData(0xfe1e, fontFamily: _fontFamily);
  static const IconData ticket_fill = IconData(0xfe1f, fontFamily: _fontFamily);
  static const IconData tiktok_logo_fill = IconData(0xfe20, fontFamily: _fontFamily);
  static const IconData timer_fill = IconData(0xfe21, fontFamily: _fontFamily);
  static const IconData toggle_left_fill = IconData(0xfe22, fontFamily: _fontFamily);
  static const IconData toggle_right_fill = IconData(0xfe23, fontFamily: _fontFamily);
  static const IconData toilet_fill = IconData(0xfe24, fontFamily: _fontFamily);
  static const IconData toilet_paper_fill = IconData(0xfe25, fontFamily: _fontFamily);
  static const IconData tote_fill = IconData(0xfe26, fontFamily: _fontFamily);
  static const IconData tote_simple_fill = IconData(0xfe27, fontFamily: _fontFamily);
  static const IconData trademark_registered_fill = IconData(0xfe28, fontFamily: _fontFamily);
  static const IconData traffic_cone_fill = IconData(0xfe29, fontFamily: _fontFamily);
  static const IconData traffic_sign_fill = IconData(0xfe2a, fontFamily: _fontFamily);
  static const IconData traffic_signal_fill = IconData(0xfe2b, fontFamily: _fontFamily);
  static const IconData train_fill = IconData(0xfe2c, fontFamily: _fontFamily);
  static const IconData train_regional_fill = IconData(0xfe2d, fontFamily: _fontFamily);
  static const IconData train_simple_fill = IconData(0xfe2e, fontFamily: _fontFamily);
  static const IconData translate_fill = IconData(0xfe2f, fontFamily: _fontFamily);
  static const IconData trash_fill = IconData(0xfe30, fontFamily: _fontFamily);
  static const IconData trash_simple_fill = IconData(0xfe31, fontFamily: _fontFamily);
  static const IconData tray_fill = IconData(0xfe32, fontFamily: _fontFamily);
  static const IconData tree_evergreen_fill = IconData(0xfe33, fontFamily: _fontFamily);
  static const IconData tree_fill = IconData(0xfe34, fontFamily: _fontFamily);
  static const IconData tree_structure_fill = IconData(0xfe35, fontFamily: _fontFamily);
  static const IconData trend_down_fill = IconData(0xfe36, fontFamily: _fontFamily);
  static const IconData trend_up_fill = IconData(0xfe37, fontFamily: _fontFamily);
  static const IconData triangle_fill = IconData(0xfe38, fontFamily: _fontFamily);
  static const IconData trophy_fill = IconData(0xfe39, fontFamily: _fontFamily);
  static const IconData truck_fill = IconData(0xfe3a, fontFamily: _fontFamily);
  static const IconData twitch_logo_fill = IconData(0xfe3b, fontFamily: _fontFamily);
  static const IconData twitter_logo_fill = IconData(0xfe3c, fontFamily: _fontFamily);
  static const IconData umbrella_fill = IconData(0xfe3d, fontFamily: _fontFamily);
  static const IconData umbrella_simple_fill = IconData(0xfe3e, fontFamily: _fontFamily);
  static const IconData upload_fill = IconData(0xfe3f, fontFamily: _fontFamily);
  static const IconData upload_simple_fill = IconData(0xfe40, fontFamily: _fontFamily);
  static const IconData user_circle_fill = IconData(0xfe41, fontFamily: _fontFamily);
  static const IconData user_circle_gear_fill = IconData(0xfe42, fontFamily: _fontFamily);
  static const IconData user_circle_minus_fill = IconData(0xfe43, fontFamily: _fontFamily);
  static const IconData user_circle_plus_fill = IconData(0xfe44, fontFamily: _fontFamily);
  static const IconData user_fill = IconData(0xfe45, fontFamily: _fontFamily);
  static const IconData user_focus_fill = IconData(0xfe46, fontFamily: _fontFamily);
  static const IconData user_gear_fill = IconData(0xfe47, fontFamily: _fontFamily);
  static const IconData user_list_fill = IconData(0xfe48, fontFamily: _fontFamily);
  static const IconData user_minus_fill = IconData(0xfe49, fontFamily: _fontFamily);
  static const IconData user_plus_fill = IconData(0xfe4a, fontFamily: _fontFamily);
  static const IconData user_rectangle_fill = IconData(0xfe4b, fontFamily: _fontFamily);
  static const IconData user_square_fill = IconData(0xfe4c, fontFamily: _fontFamily);
  static const IconData user_switch_fill = IconData(0xfe4d, fontFamily: _fontFamily);
  static const IconData users_fill = IconData(0xfe4e, fontFamily: _fontFamily);
  static const IconData users_four_fill = IconData(0xfe4f, fontFamily: _fontFamily);
  static const IconData users_three_fill = IconData(0xfe50, fontFamily: _fontFamily);
  static const IconData vault_fill = IconData(0xfe51, fontFamily: _fontFamily);
  static const IconData vibrate_fill = IconData(0xfe52, fontFamily: _fontFamily);
  static const IconData video_camera_fill = IconData(0xfe53, fontFamily: _fontFamily);
  static const IconData video_camera_slash_fill = IconData(0xfe54, fontFamily: _fontFamily);
  static const IconData vignette_fill = IconData(0xfe55, fontFamily: _fontFamily);
  static const IconData voicemail_fill = IconData(0xfe56, fontFamily: _fontFamily);
  static const IconData volleyball_fill = IconData(0xfe57, fontFamily: _fontFamily);
  static const IconData wall_fill = IconData(0xfe58, fontFamily: _fontFamily);
  static const IconData wallet_fill = IconData(0xfe59, fontFamily: _fontFamily);
  static const IconData warning_circle_fill = IconData(0xfe5a, fontFamily: _fontFamily);
  static const IconData warning_fill = IconData(0xfe5b, fontFamily: _fontFamily);
  static const IconData warning_octagon_fill = IconData(0xfe5c, fontFamily: _fontFamily);
  static const IconData watch_fill = IconData(0xfe5d, fontFamily: _fontFamily);
  static const IconData wave_sawtooth_fill = IconData(0xfe5e, fontFamily: _fontFamily);
  static const IconData wave_sine_fill = IconData(0xfe5f, fontFamily: _fontFamily);
  static const IconData wave_square_fill = IconData(0xfe60, fontFamily: _fontFamily);
  static const IconData wave_triangle_fill = IconData(0xfe61, fontFamily: _fontFamily);
  static const IconData waves_fill = IconData(0xfe62, fontFamily: _fontFamily);
  static const IconData webcam_fill = IconData(0xfe63, fontFamily: _fontFamily);
  static const IconData whatsapp_logo_fill = IconData(0xfe64, fontFamily: _fontFamily);
  static const IconData wheelchair_fill = IconData(0xfe65, fontFamily: _fontFamily);
  static const IconData wifi_high_fill = IconData(0xfe66, fontFamily: _fontFamily);
  static const IconData wifi_low_fill = IconData(0xfe67, fontFamily: _fontFamily);
  static const IconData wifi_medium_fill = IconData(0xfe68, fontFamily: _fontFamily);
  static const IconData wifi_none_fill = IconData(0xfe69, fontFamily: _fontFamily);
  static const IconData wifi_slash_fill = IconData(0xfe6a, fontFamily: _fontFamily);
  static const IconData wifi_x_fill = IconData(0xfe6b, fontFamily: _fontFamily);
  static const IconData wind_fill = IconData(0xfe6c, fontFamily: _fontFamily);
  static const IconData windows_logo_fill = IconData(0xfe6d, fontFamily: _fontFamily);
  static const IconData wine_fill = IconData(0xfe6e, fontFamily: _fontFamily);
  static const IconData wrench_fill = IconData(0xfe6f, fontFamily: _fontFamily);
  static const IconData x_circle_fill = IconData(0xfe70, fontFamily: _fontFamily);
  static const IconData x_fill = IconData(0xfe71, fontFamily: _fontFamily);
  static const IconData x_square_fill = IconData(0xfe72, fontFamily: _fontFamily);
  static const IconData yin_yang_fill = IconData(0xfe73, fontFamily: _fontFamily);
  static const IconData youtube_logo_fill = IconData(0xfe74, fontFamily: _fontFamily);
}
