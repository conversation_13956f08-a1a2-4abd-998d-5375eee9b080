import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:techrar_captain/core/exports/core.dart';
import '../controller/auth_view_controller.dart';

class AuthView extends ConsumerStatefulWidget {
  const AuthView({Key? key}) : super(key: key);

  @override
  ConsumerState<AuthView> createState() => _AuthViewState();
}

class _AuthViewState extends ConsumerState<AuthView> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLogin = true;

  @override
  Widget build(BuildContext context) {
    final controller = ref.watch(authViewControllerProvider);
    
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(Insets.l),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              SizedBox(height: Insets.xl),
              Text(
                _isLogin ? 'Welcome Back' : 'Create Account',
                style: TextStyles.t1,
                textAlign: TextAlign.center,
              ),
              SizedBox(height: Insets.m),
              Text(
                _isLogin 
                  ? 'Sign in to continue your fitness journey'
                  : 'Join our community and start your fitness journey',
                style: TextStyles.body2,
                textAlign: TextAlign.center,
              ),
              SizedBox(height: Insets.xl),
              RoundedTextFormField(
                controller: _emailController,
                hintText: 'Email',
                keyboardType: TextInputType.emailAddress,
              ),
              SizedBox(height: Insets.m),
              RoundedTextFormField(
                controller: _passwordController,
                hintText: 'Password',
                obscureText: true,
              ),
              SizedBox(height: Insets.l),
              RoundedButton(
                title: _isLogin ? 'Login' : 'Sign Up',
                onTap: () {
                  if (_isLogin) {
                    controller.login(
                      _emailController.text, 
                      _passwordController.text
                    );
                  } else {
                    controller.register(
                      _emailController.text, 
                      _passwordController.text
                    );
                  }
                },
              ),
              SizedBox(height: Insets.m),
              TextButton(
                onPressed: () {
                  setState(() {
                    _isLogin = !_isLogin;
                  });
                },
                child: Text(
                  _isLogin 
                    ? 'Don\'t have an account? Sign Up' 
                    : 'Already have an account? Login',
                  style: TextStyles.body3.copyWith(color: kPrimaryColor),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}