import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:techrar_captain/core/exports/core.dart';
import '../controller/home_view_controller.dart';
import '../components/achievement_card.dart';
import '../components/class_card.dart';

class HomeView extends ConsumerWidget {
  const HomeView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.watch(homeViewControllerProvider);
    final user = controller.currentUser;
    
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.all(Insets.l),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Hello,', style: TextStyles.body1),
                        Text(user?.name ?? 'Fitness Enthusiast', style: TextStyles.h1b),
                      ],
                    ),
                    CircleAvatar(
                      radius: 24,
                      backgroundImage: user?.profileImage != null 
                          ? NetworkImage(user!.profileImage!) 
                          : null,
                      child: user?.profileImage == null 
                          ? Icon(Icons.person, size: 30) 
                          : null,
                    ),
                  ],
                ),
                SizedBox(height: Insets.xl),
                
                // Subscription Status Card
                Container(
                  padding: EdgeInsets.all(Insets.m),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [kPrimaryColor, Color(0xFF8E24AA)],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: Borders.mBorderRadius,
                    boxShadow: Styles.unifiedShadow,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Your Subscription',
                        style: TextStyles.body2b.copyWith(color: kWhite),
                      ),
                      SizedBox(height: Insets.s),
                      Text(
                        user?.subscriptionStatus == SubscriptionStatus.active
                            ? 'Active until June 30, 2023'
                            : 'No active subscription',
                        style: TextStyles.body1.copyWith(color: kWhite),
                      ),
                      SizedBox(height: Insets.m),
                      RoundedButton(
                        title: user?.subscriptionStatus == SubscriptionStatus.active
                            ? 'Manage Subscription'
                            : 'Subscribe Now',
                        height: Sizes.sCardHeight,
                        buttonColor: kWhite,
                        titleColor: kPrimaryColor,
                        onTap: () => controller.navigateToSubscription(),
                      ),
                    ],
                  ),
                ),
                
                SizedBox(height: Insets.xl),
                
                // Upcoming Classes
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('Upcoming Classes', style: TextStyles.h2b),
                    TextButton(
                      onPressed: () => controller.navigateToClasses(),
                      child: Text('See All', style: TextStyles.body3.copyWith(color: kPrimaryColor)),
                    ),
                  ],
                ),
                SizedBox(height: Insets.s),
                SizedBox(
                  height: Sizes.xlCardHeight,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: controller.upcomingClasses.length,
                    itemBuilder: (context, index) {
                      return ClassCard(
                        gymClass: controller.upcomingClasses[index],
                        onTap: () => controller.navigateToClassDetails(
                          controller.upcomingClasses[index].id
                        ),
                      );
                    },
                  ),
                ),
                
                SizedBox(height: Insets.xl),
                
                // Achievements
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('Your Achievements', style: TextStyles.h2b),
                    TextButton(
                      onPressed: () => controller.navigateToAchievements(),
                      child: Text('See All', style: TextStyles.body3.copyWith(color: kPrimaryColor)),
                    ),
                  ],
                ),
                SizedBox(height: Insets.s),
                SizedBox(
                  height: 100,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: controller.recentAchievements.length,
                    itemBuilder: (context, index) {
                      return AchievementCard(
                        achievement: controller.recentAchievements[index],
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: 0,
        onTap: (index) => controller.handleNavigation(index),
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
          BottomNavigationBarItem(icon: Icon(Icons.calendar_today), label: 'Classes'),
          BottomNavigationBarItem(icon: Icon(Icons.emoji_events), label: 'Achievements'),
          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
        ],
      ),
    );
  }
}