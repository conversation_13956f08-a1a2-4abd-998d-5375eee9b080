import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:techrar_captain/core/exports/core.dart';
import '../controller/achievements_view_controller.dart';
import '../components/achievement_tile.dart';

class AchievementsView extends ConsumerWidget {
  const AchievementsView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.watch(achievementsViewControllerProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: Text('Achievements', style: TextStyles.h1b),
        elevation: 0,
        backgroundColor: kWhite,
        foregroundColor: kFontsColor,
      ),
      body: Column(
        children: [
          // Points and level
          Container(
            padding: EdgeInsets.all(Insets.l),
            decoration: BoxDecoration(
              color: kWhite,
              boxShadow: Styles.boxShadowBottom,
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: kPrimaryColor,
                  child: Text(
                    controller.userLevel.toString(),
                    style: TextStyles.h1b.copyWith(color: kWhite),
                  ),
                ),
                SizedBox(width: Insets.l),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Level ${controller.userLevel}', style: TextStyles.h2b),
                      SizedBox(height: Insets.xs),
                      Text('${controller.userPoints} points', style: TextStyles.body2),
                      SizedBox(height: Insets.s),
                      LinearProgressIndicator(
                        value: controller.levelProgress,
                        backgroundColor: kLightGrey,
                        valueColor: AlwaysStoppedAnimation<Color>(kPrimaryColor),
                      ),
                      SizedBox(height: Insets.xs),
                      Text(
                        '${controller.pointsToNextLevel} points to next level',
                        style: TextStyles.body3,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // Tabs for different achievement categories
          TabBar(
            controller: controller.tabController,
            labelColor: kPrimaryColor,
            unselectedLabelColor: kGrey,
            indicatorColor: kPrimaryColor,
            tabs: const [
              Tab(text: 'All'),
              Tab(text: 'Unlocked'),
              Tab(text: 'Locked'),
            ],
          ),
          
          // Achievement list
          Expanded(
            child: TabBarView(
              controller: controller.tabController,
              children: [
                // All achievements
                _buildAchievementList(controller.allAchievements),
                
                // Unlocked achievements
                _buildAchievementList(controller.unlockedAchievements),
                
                // Locked achievements
                _buildAchievementList(controller.lockedAchievements),
              ],
            ),
          ),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: 2,
        onTap: (index) => controller.handleNavigation(index),
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
          BottomNavigationBarItem(icon: Icon(Icons.calendar_today), label: 'Classes'),
          BottomNavigationBarItem(icon: Icon(Icons.emoji_events), label: 'Achievements'),
          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
        ],
      ),
    );
  }
  
  Widget _buildAchievementList(List<Achievement> achievements) {
    return ListView.builder(
      padding: EdgeInsets.all(Insets.m),
      itemCount: achievements.length,
      itemBuilder: (context, index) {
        return AchievementTile(
          achievement: achievements[index],
        );
      },
    );
  }
}