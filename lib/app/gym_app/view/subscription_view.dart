import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:techrar_captain/core/exports/core.dart';
import '../controller/subscription_view_controller.dart';

class SubscriptionView extends ConsumerWidget {
  const SubscriptionView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.watch(subscriptionViewControllerProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text('Membership', style: TextStyles.h1b),
        elevation: 0,
        backgroundColor: kWhite,
        foregroundColor: kFontsColor,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(Insets.l),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Current subscription status
            Container(
              padding: EdgeInsets.all(Insets.l),
              decoration: BoxDecoration(
                color: controller.hasActiveSubscription ? kGreen.withOpacity(0.1) : kRed.withOpacity(0.1),
                borderRadius: Borders.mBorderRadius,
              ),
              child: Row(
                children: [
                  Icon(
                    controller.hasActiveSubscription ? Icons.check_circle : Icons.info,
                    color: controller.hasActiveSubscription ? kGreen : kRed,
                    size: 36,
                  ),
                  SizedBox(width: Insets.m),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          controller.hasActiveSubscription ? 'Active Membership' : 'No Active Membership',
                          style: TextStyles.h2b,
                        ),
                        SizedBox(height: Insets.xs),
                        Text(
                          controller.hasActiveSubscription
                              ? 'Valid until ${controller.expiryDate}'
                              : 'Subscribe to access all gym facilities and classes',
                          style: TextStyles.body3,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: Insets.xl),
            Text('Membership Plans', style: TextStyles.h2b),
            SizedBox(height: Insets.m),

            // Membership plans
            _buildPlanCard(
              context,
              title: 'Monthly Plan',
              price: '\$49.99',
              features: [
                'Unlimited gym access',
                'Access to all classes',
                'Personal trainer (1 session/month)',
                'Locker access',
              ],
              isSelected: controller.selectedPlan == 'monthly',
              onTap: () => controller.selectPlan('monthly'),
            ),

            SizedBox(height: Insets.m),

            _buildPlanCard(
              context,
              title: 'Quarterly Plan',
              price: '\$129.99',
              features: [
                'Unlimited gym access',
                'Access to all classes',
                'Personal trainer (2 sessions/month)',
                'Locker access',
                'Nutrition consultation',
              ],
              isSelected: controller.selectedPlan == 'quarterly',
              onTap: () => controller.selectPlan('quarterly'),
              bestValue: true,
            ),

            SizedBox(height: Insets.m),

            _buildPlanCard(
              context,
              title: 'Annual Plan',
              price: '\$449.99',
              features: [
                'Unlimited gym access',
                'Access to all classes',
                'Personal trainer (4 sessions/month)',
                'Locker access',
                'Nutrition consultation',
                'Fitness assessment quarterly',
                'Guest passes (2/month)',
              ],
              isSelected: controller.selectedPlan == 'annual',
              onTap: () => controller.selectPlan('annual'),
            ),

            SizedBox(height: Insets.xl),

            RoundedButton(
              title: controller.hasActiveSubscription ? 'Change Plan' : 'Subscribe Now',
              onTap: () => controller.subscribeToPlan(),
              enabled: controller.selectedPlan != null,
            ),

            if (controller.hasActiveSubscription) ...[
              SizedBox(height: Insets.m),
              RoundedButton(
                title: 'Cancel Subscription',
                buttonColor: kLight0,
                titleColor: kRed,
                onTap: () => controller.showCancellationDialog(context),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPlanCard(
    BuildContext context, {
    required String title,
    required String price,
    required List<String> features,
    required bool isSelected,
    required VoidCallback onTap,
    bool bestValue = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(Insets.m),
        decoration: BoxDecoration(
          color: isSelected ? kPrimaryColor.withOpacity(0.1) : kWhite,
          borderRadius: Borders.mBorderRadius,
          border: Border.all(
            color: isSelected ? kPrimaryColor : kLightGrey,
            width: 2,
          ),
          boxShadow: isSelected ? Styles.unifiedShadow : null,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(title, style: TextStyles.h2b),
                    SizedBox(height: Insets.xs),
                    Text(price, style: TextStyles.body1b),
                  ],
                ),
                if (bestValue)
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: Insets.s, vertical: Insets.xs),
                    decoration: BoxDecoration(
                      color: kSecondaryColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'Best Value',
                      style: TextStyles.body3.copyWith(color: kWhite),
                    ),
                  ),
                if (isSelected)
                  Container(
                    padding: EdgeInsets.all(Insets.xs),
                    decoration: BoxDecoration(
                      color: kPrimaryColor,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(Icons.check, color: kWhite, size: 16),
                  ),
              ],
            ),
            SizedBox(height: Insets.m),
            ...features.map((feature) => Padding(
                  padding: EdgeInsets.only(bottom: Insets.s),
                  child: Row(
                    children: [
                      Icon(Icons.check_circle, color: kPrimaryColor, size: 16),
                      SizedBox(width: Insets.s),
                      Text(feature, style: TextStyles.body3),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }
}
