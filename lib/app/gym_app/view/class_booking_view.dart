import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:techrar_captain/core/exports/core.dart';
import '../controller/class_booking_view_controller.dart';
import '../components/class_list_item.dart';

class ClassBookingView extends ConsumerWidget {
  const ClassBookingView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.watch(classBookingViewControllerProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: Text('Class Schedule', style: TextStyles.h1b),
        elevation: 0,
        backgroundColor: kWhite,
        foregroundColor: kFontsColor,
      ),
      body: Column(
        children: [
          // Filter options
          Container(
            padding: EdgeInsets.symmetric(horizontal: Insets.l, vertical: Insets.m),
            decoration: BoxDecoration(
              color: kWhite,
              boxShadow: Styles.boxShadowBottom,
            ),
            child: Row(
              children: [
                Expanded(
                  child: RoundedButton(
                    title: 'Filter',
                    height: Sizes.sCardHeight,
                    buttonColor: k<PERSON><PERSON><PERSON><PERSON>,
                    titleColor: kFontsColor,
                    onTap: () => controller.showFilterOptions(context),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.filter_list, size: 18),
                        SizedBox(width: Insets.s),
                        Text('Filter', style: TextStyles.body3),
                      ],
                    ),
                  ),
                ),
                SizedBox(width: Insets.m),
                Expanded(
                  child: RoundedButton(
                    title: 'Date',
                    height: Sizes.sCardHeight,
                    buttonColor: kLightGrey,
                    titleColor: kFontsColor,
                    onTap: () => controller.showDatePicker(context),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.calendar_today, size: 18),
                        SizedBox(width: Insets.s),
                        Text(controller.selectedDateFormatted, style: TextStyles.body3),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Class list
          Expanded(
            child: controller.isLoading
                ? Center(child: CircularProgressIndicator())
                : controller.classes.isEmpty
                    ? Center(
                        child: Text(
                          'No classes available for this date',
                          style: TextStyles.body1,
                        ),
                      )
                    : ListView.builder(
                        padding: EdgeInsets.all(Insets.m),
                        itemCount: controller.classes.length,
                        itemBuilder: (context, index) {
                          final gymClass = controller.classes[index];
                          return ClassListItem(
                            gymClass: gymClass,
                            isBooked: controller.isClassBooked(gymClass.id),
                            onTap: () => controller.navigateToClassDetails(gymClass.id),
                            onBookTap: () => controller.bookClass(gymClass.id),
                          );
                        },
                      ),
          ),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: 1,
        onTap: (index) => controller.handleNavigation(index),
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
          BottomNavigationBarItem(icon: Icon(Icons.calendar_today), label: 'Classes'),
          BottomNavigationBarItem(icon: Icon(Icons.emoji_events), label: 'Achievements'),
          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
        ],
      ),
    );
  }
}