import 'package:flutter/material.dart';
import 'package:techrar_captain/core/exports/core.dart';
import '../model/achievement.dart';

class AchievementTile extends StatelessWidget {
  final Achievement achievement;

  const AchievementTile({
    Key? key,
    required this.achievement,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: Insets.m),
      padding: EdgeInsets.all(Insets.m),
      decoration: BoxDecoration(
        color: kWhite,
        borderRadius: Borders.mBorderRadius,
        boxShadow: Styles.boxShadow,
        border: achievement.isUnlocked
            ? Border.all(color: kPrimaryColor, width: 2)
            : null,
      ),
      child: Row(
        children: [
          Container(
            height: 60,
            width: 60,
            decoration: BoxDecoration(
              color: achievement.isUnlocked ? kPrimaryColor : kLightGrey,
              shape: BoxShape.circle,
            ),
            child: Padding(
              padding: EdgeInsets.all(Insets.s),
              child: Image.asset(
                achievement.iconPath,
                color: achievement.isUnlocked ? kWhite : kGrey,
              ),
            ),
          ),
          SizedBox(width: Insets.m),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      achievement.title,
                      style: TextStyles.body1b,
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: kPrimaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '+${achievement.pointsAwarded} pts',
                        style: TextStyles.body3.copyWith(color: kPrimaryColor),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: Insets.xs),
                Text(
                  achievement.description,
                  style: TextStyles.body3,
                ),
                if (!achievement.isUnlocked) ...[
                  SizedBox(height: Insets.s),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: kLightGrey,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'Locked',
                      style: TextStyles.body3.copyWith(color: kGrey),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}