import 'package:flutter/material.dart';
import 'package:techrar_captain/core/exports/core.dart';
import '../model/gym_class.dart';
import 'package:intl/intl.dart';

class ClassListItem extends StatelessWidget {
  final GymClass gymClass;
  final bool isBooked;
  final VoidCallback onTap;
  final VoidCallback onBookTap;

  const ClassListItem({
    Key? key,
    required this.gymClass,
    required this.isBooked,
    required this.onTap,
    required this.onBookTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: Insets.m),
      decoration: BoxDecoration(
        color: kWhite,
        borderRadius: Borders.mBorderRadius,
        boxShadow: Styles.boxShadow,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: Borders.mBorderRadius,
        child: Padding(
          padding: EdgeInsets.all(Insets.m),
          child: Row(
            children: [
              // Time column
              Container(
                width: 60,
                child: Column(
                  children: [
                    Text(
                      DateFormat('HH:mm').format(gymClass.startTime),
                      style: TextStyles.body2b,
                    ),
                    Container(
                      height: 30,
                      width: 1,
                      color: kGrey.withOpacity(0.3),
                      margin: EdgeInsets.symmetric(vertical: 4),
                    ),
                    Text(
                      DateFormat('HH:mm').format(gymClass.endTime),
                      style: TextStyles.body2b,
                    ),
                  ],
                ),
              ),
              
              SizedBox(width: Insets.m),
              
              // Class details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      gymClass.name,
                      style: TextStyles.body1b,
                    ),
                    SizedBox(height: Insets.xs),
                    Row(
                      children: [
                        Icon(Icons.person, size: 14, color: kGrey),
                        SizedBox(width: 4),
                        Text(
                          gymClass.trainer,
                          style: TextStyles.body3,
                        ),
                      ],
                    ),
                    SizedBox(height: Insets.xs),
                    Row(
                      children: [
                        Icon(Icons.people, size: 14, color: kGrey),
                        SizedBox(width: 4),
                        Text(
                          '${gymClass.enrolled}/${gymClass.capacity} spots',
                          style: TextStyles.body3,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // Book button
              RoundedButton(
                title: isBooked ? 'Booked' : 'Book',
                height: 36,
                width: 80,
                buttonColor: isBooked ? kLightGrey : kPrimaryColor,
                titleColor: isBooked ? kGrey : kWhite,
                onTap: isBooked ? null : onBookTap,
                enabled: !isBooked,
              ),
            ],
          ),
        ),
      ),
    );
  }
}