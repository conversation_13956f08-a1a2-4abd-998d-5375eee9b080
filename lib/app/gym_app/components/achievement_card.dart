import 'package:flutter/material.dart';
import 'package:techrar_captain/core/exports/core.dart';
import '../model/achievement.dart';

class AchievementCard extends StatelessWidget {
  final Achievement achievement;

  const AchievementCard({
    Key? key,
    required this.achievement,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 80,
      margin: EdgeInsets.only(right: Insets.m),
      child: Column(
        children: [
          Container(
            height: 60,
            width: 60,
            decoration: BoxDecoration(
              color: achievement.isUnlocked ? kPrimaryColor : kLightGrey,
              shape: BoxShape.circle,
            ),
            child: Padding(
              padding: EdgeInsets.all(Insets.s),
              child: Image.asset(
                achievement.iconPath,
                color: achievement.isUnlocked ? kWhite : kGrey,
              ),
            ),
          ),
          SizedBox(height: Insets.xs),
          Text(
            achievement.title,
            style: TextStyles.body3,
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}