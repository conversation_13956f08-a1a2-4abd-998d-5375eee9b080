import 'package:flutter/material.dart';
import 'package:techrar_captain/core/exports/core.dart';
import '../model/gym_class.dart';
import 'package:intl/intl.dart';

class ClassCard extends StatelessWidget {
  final GymClass gymClass;
  final VoidCallback onTap;

  const ClassCard({
    Key? key,
    required this.gymClass,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 240,
        margin: EdgeInsets.only(right: Insets.m),
        decoration: BoxDecoration(
          color: kWhite,
          borderRadius: Borders.mBorderRadius,
          boxShadow: Styles.boxShadow,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Class image
            Container(
              height: 120,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
                image: DecorationImage(
                  image: NetworkImage(gymClass.imageUrl ?? 'https://via.placeholder.com/240x120'),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            
            Padding(
              padding: EdgeInsets.all(Insets.m),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Time
                  Row(
                    children: [
                      Icon(Icons.access_time, size: 14, color: kGrey),
                      SizedBox(width: Insets.xs),
                      Text(
                        '${DateFormat.jm().format(gymClass.startTime)} - ${DateFormat.jm().format(gymClass.endTime)}',
                        style: TextStyles.body3.copyWith(color: kGrey),
                      ),
                    ],
                  ),
                  SizedBox(height: Insets.xs),
                  
                  // Class name
                  Text(
                    gymClass.name,
                    style: TextStyles.body1b,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: Insets.xs),
                  
                  // Trainer
                  Row(
                    children: [
                      Icon(Icons.person, size: 14, color: kGrey),
                      SizedBox(width: Insets.xs),
                      Text(
                        gymClass.trainer,
                        style: TextStyles.body3,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                  SizedBox(height: Insets.xs),
                  
                  // Capacity
                  Row(
                    children: [
                      Icon(Icons.people, size: 14, color: kGrey),
                      SizedBox(width: Insets.xs),
                      Text(
                        '${gymClass.enrolled}/${gymClass.capacity}',
                        style: TextStyles.body3,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}