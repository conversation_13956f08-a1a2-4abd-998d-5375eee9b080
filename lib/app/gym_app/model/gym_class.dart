class GymClass {
  final String id;
  final String name;
  final String trainer;
  final DateTime startTime;
  final DateTime endTime;
  final int capacity;
  final int enrolled;
  final String description;
  final String? imageUrl;

  GymClass({
    required this.id,
    required this.name,
    required this.trainer,
    required this.startTime,
    required this.endTime,
    required this.capacity,
    required this.enrolled,
    required this.description,
    this.imageUrl,
  });
}