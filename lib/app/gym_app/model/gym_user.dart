class GymUser {
  final String id;
  final String name;
  final String email;
  final String? profileImage;
  final SubscriptionStatus subscriptionStatus;
  final List<String> bookedClasses;
  final List<String> achievements;
  final int points;

  GymUser({
    required this.id,
    required this.name,
    required this.email,
    this.profileImage,
    required this.subscriptionStatus,
    this.bookedClasses = const [],
    this.achievements = const [],
    this.points = 0,
  });
}

enum SubscriptionStatus { active, expired, none }