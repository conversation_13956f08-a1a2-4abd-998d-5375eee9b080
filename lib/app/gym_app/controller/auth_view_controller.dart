import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:techrar_captain/core/exports/core.dart';
import '../model/gym_user.dart';

final authViewControllerProvider = Provider((ref) => AuthViewController(ref));

class AuthViewController {
  final Ref _ref;

  AuthViewController(this._ref);

  Future<void> login(String email, String password) async {
    // Validate inputs
    if (email.isEmpty || password.isEmpty) {
      AlertDialogBox.showAlert(
        Nav.navigatorKey.currentContext!,
        message: 'Please enter both email and password',
      );
      return;
    }

    // Show loading
    LoadingDialog.show(Nav.navigatorKey.currentContext!);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      // Success - navigate to home
      LoadingDialog.hide();
      Nav.pushNamedAndRemoveUntil(Nav.navigatorKey.currentContext!, '/home');
    } catch (e) {
      // Error handling
      LoadingDialog.hide();
      AlertDialogBox.showAlert(
        Nav.navigatorKey.currentContext!,
        message: 'Login failed: ${e.toString()}',
      );
    }
  }

  Future<void> register(String email, String password) async {
    // Validate inputs
    if (email.isEmpty || password.isEmpty) {
      AlertDialogBox.showAlert(
        Nav.navigatorKey.currentContext!,
        message: 'Please enter both email and password',
      );
      return;
    }

    // Show loading
    LoadingDialog.show(Nav.navigatorKey.currentContext!);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      // Success - navigate to home
      LoadingDialog.hide();
      Nav.pushNamedAndRemoveUntil(Nav.navigatorKey.currentContext!, '/home');
    } catch (e) {
      // Error handling
      LoadingDialog.hide();
      AlertDialogBox.showAlert(
        Nav.navigatorKey.currentContext!,
        message: 'Registration failed: ${e.toString()}',
      );
    }
  }
}