import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:techrar_captain/core/exports/core.dart';
import '../model/gym_user.dart';
import '../model/gym_class.dart';
import '../model/achievement.dart';

final homeViewControllerProvider = Provider((ref) => HomeViewController(ref));

class HomeViewController {
  final Ref _ref;
  
  // Mock data
  final GymUser? currentUser = GymUser(
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    subscriptionStatus: SubscriptionStatus.active,
    points: 350,
  );
  
  final List<GymClass> upcomingClasses = [
    GymClass(
      id: '1',
      name: '<PERSON> <PERSON>',
      trainer: '<PERSON>',
      startTime: DateTime.now().add(const Duration(hours: 2)),
      endTime: DateTime.now().add(const Duration(hours: 3)),
      capacity: 20,
      enrolled: 15,
      description: 'A dynamic yoga class focusing on linking breath with movement.',
      imageUrl: 'https://images.unsplash.com/photo-1575052814086-f385e2e2ad1b',
    ),
    GymClass(
      id: '2',
      name: 'HIIT Circuit',
      trainer: '<PERSON>',
      startTime: DateTime.now().add(const Duration(hours: 4)),
      endTime: DateTime.now().add(const Duration(hours: 5)),
      capacity: 15,
      enrolled: 12,
      description: 'High-intensity interval training to boost your metabolism.',
      imageUrl: 'https://images.unsplash.com/photo-1549060279-7e168fcee0c2',
    ),
    GymClass(
      id: '3',
      name: 'Spin Class',
      trainer: 'Lisa Wong',
      startTime: DateTime.now().add(const Duration(hours: 6)),
      endTime: DateTime.now().add(const Duration(hours: 7)),
      capacity: 25,
      enrolled: 20,
      description: 'An energetic indoor cycling workout set to motivating music.',
      imageUrl: 'https://images.unsplash.com/photo-1534787238916-9ba6764efd4f',
    ),
  ];
  
  final List<Achievement> recentAchievements = [
    Achievement(
      id: '1',
      title: 'First Workout',
      description: 'Complete your first workout session',
      iconPath: 'assets/icons/workout.png',
      pointsAwarded: 50,
      isUnlocked: true,
    ),
    Achievement(
      id: '2',
      title: 'Early Bird',
      description: 'Attend a class before 8 AM',
      iconPath: 'assets/icons/morning.png',
      pointsAwarded: 75,
      isUnlocked: true,
    ),
    Achievement(
      id: '3',
      title: 'Class Streak',
      description: 'Attend classes 3 days in a row',
      iconPath: 'assets/icons/streak.png',
      pointsAwarded: 100,
      isUnlocked: false,
    ),
  ];

  HomeViewController(this._ref);
  
  void navigateToSubscription() {
    Nav.pushNamed(Nav.navigatorKey.currentContext!, '/subscription');
  }
  
  void navigateToClasses() {
    Nav.pushNamed(Nav.navigatorKey.currentContext!, '/classes');
  }
  
  void navigateToClassDetails(String classId) {
    Nav.pushNamed(Nav.navigatorKey.currentContext!, '/class-details', args: classId);
  }
  
  void navigateToAchievements() {
    Nav.pushNamed(Nav.navigatorKey.currentContext!, '/achievements');
  }
  
  void handleNavigation(int index) {
    switch (index) {
      case 0:
        // Already on home
        break;
      case 1:
        navigateToClasses();
        break;
      case 2:
        navigateToAchievements();
        break;
      case 3:
        Nav.openPage(
          context: Nav.mainScaffoldNavbarKey.currentContext!,
          page: const ProfileView(),
        );
        break;
    }
  }
}
