# Techrar Flutter Template

Welcome to the official Techrar Flutter template!  
This project provides a clean, scalable starting point for building robust Flutter applications, following Techrar’s standards for architecture and efficiency.

## Getting Started

### Prerequisites

- [Flutter SDK](https://docs.flutter.dev/get-started/install)
- Dart SDK (bundled with Flutter)
- Preferred IDE: [VS Code](https://code.visualstudio.com/) or [Android Studio](https://developer.android.com/studio)
- Git

### Setup Instructions

1. **Clone the repository:**

   ```sh
   git clone https://github.com/techrar-co/techrar-flutter-template.git
   cd flutter-template
   ```

2. **Initialize Flutter project files:**

   If this is your first time setting up the template, or if you notice missing platform folders (`android/`, `ios/`, etc.), run:

   ```sh
   flutter create .
   ```

   This will generate all necessary platform-specific files.

3. **Install dependencies:**

   ```sh
   flutter pub get
   ```

4. **Run the app:**

   ```sh
   flutter run
   ```

   Make sure you have a device or emulator running.

---

_This template is maintained by the Techrar product and engineering teams. For more information, visit [techrar.com](https://techrar.com)._

s2_216ea657e3494b368b462ce37285aa2d
