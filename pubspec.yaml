name: techrar_captain
description: A modern web-based scanner for gyms and restaurants to efficiently manage orders, loyalty programs and memberships

publish_to: "none" # Remove this line if you wish to publish to pub.dev

version: 1.6.2+82

environment:
  sdk: ">=3.0.6 <4.0.0"

dependencies:
  flutter:
    sdk: flutter

  flutter_localizations:
    sdk: flutter

  #UI
  cupertino_icons: ^1.0.8
  lottie: ^3.3.0

  #Webview

  #Essentials
  collection: ^1.19.0
  provider: ^6.1.2
  http: ^1.2.2
  flutter_riverpod: ^2.6.1

  #Services
  intl: ^0.19.0
  jwt_decode: ^0.3.1
  shared_preferences: ^2.3.4
  url_launcher: ^6.0.20
  permission_handler: ^11.3.1
  universal_io: ^2.0.4

  # Freezed
  freezed_annotation: ^2.4.4
  json_annotation: ^4.9.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0
  build_runner: ^2.4.14
  freezed: ^2.5.7
  json_serializable: ^6.9.0

flutter_icons:
  android: true
  ios: true
  image_path: "logo.png"
flutter:
  uses-material-design: true

  assets:
    - assets/languages/
    - assets/lottie/
