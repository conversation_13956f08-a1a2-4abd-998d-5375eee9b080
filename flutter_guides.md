#### Technical Specification

- **Framework:** Flutter
- **Architecture:** modified MVC (Model-View-Controller)
- **Modularity:**
  - The entire feature is encapsulated in a single folder: `routing/`
  - No logic in the view; all logic in controllers/services.
  - No widget-returning methods in the view.
  - All environment variables (e.g., Google Maps API key) are accessed via `String.fromEnvironment`.
  - The module is copy-paste ready and does not require modification to integrate.

##### Folder Structure

```
lib/
  └── app/
       └──feature/
          ├── model/
          │     └── model.dart
          ├── view/
          │     └── <view_name>_view.dart
          ├── controller/
          │     └── <view_name>_view_controller.dart
          ├── components/
          │     └── [all reusable UI components, e.g., map, pin, order_card, bottom_sheet, etc.]
          └── service/
                └── <service_name>_service.dart
```

- **Naming Conventions:**
  - Sheets: Suffix with `Sheet` (e.g., `OrderListSheet`)
  - Views: Suffix with `View` (e.g., `RouteBuilderView`)
  - Controllers: Suffix with `<view_name>_view_controller` (e.g., `route_builder_view_controller`)
  - Services: Suffix with `Service` (e.g., `RouteBuilderService`)

---

##### Conventions

To ensure consistency, maintainability, and portability, follow these conventions:

- **Colors:**  
  Use only the color definitions from `lib/core/constants/colors.dart`.

- **Decorators:**  
  Use only the decorators from `lib/core/constants/decorations.dart`.

- **Spacing & Insets:**  
  Do not use hardcoded numbers for padding or spacing.  
  Use `Insets.s`, `Insets.m`, etc., for all spacing and padding. found inside lib/core/constants/decorations.dart


- **Card Heights:**  
  Use `Sizes.xlCardHeight`, `Sizes.mCardHeight`, etc., for card heights. found inside lib/core/constants/decorations.dart

- **Text Styles:**  
  Use `TextStyles.body1`, `TextStyles.body1b`, `TextStyles.h1`, etc., for all text styling. found inside lib/core/constants/decorations.dart

- **Border Radius:**  
  Use `Borders.mBorderRadius`, `Borders.sBorderRadius`, etc., for border radii. found inside lib/core/constants/decorations.dart

- **Shadows:**  
  Use `Styles.unifiedShadow`, `Styles.boxShadowBottom`, etc., for shadows. found inside lib/core/constants/decorations.dart

- **Utilities:**  
  implement utility functions, in check:

  - `lib/core/util/*`

- **View Layer:**  
  The view layer is strictly for UI composition and state listening.  
  No business logic or widget-returning methods should be present in the view.

- **Controller & Service Layers:**  
  All business logic is handled in the controller and service layers.

- **Portability:**  
  The module is designed for maximum portability and minimal integration effort.  
  All dependencies and logic are encapsulated; no external modification required.

lib/core/constants/decorations.dart file start point

class Insets {
static const double xs = 2;

static const double s = 6;

static const double m = 12;

static const double l = 20;

static const double xl = 24;

static const double xxl = 36;
}

class Sizes {
static const double xlCardHeight = 120.0;

static const double lCardHeight = 80.0;

static const double mCardHeight = 60.0;

static const double sCardHeight = 40.0;
}

class Fonts {
static const String varelaRound = "VarelaRound";
static const String almarai = "Almarai";
}

class TextStyles {
static const TextStyle varelaRound = TextStyle(
fontFamily: Fonts.varelaRound,
fontWeight: FontWeight.w400,
color: kFontsColor,
height: 1.1,
fontFamilyFallback: [
'Almarai',
'Tajawal',
],
);

static const TextStyle almarai = TextStyle(
fontFamily: Fonts.almarai,
fontWeight: FontWeight.w400,
color: kFontsColor,
fontFamilyFallback: [
'varelaRound',
'Tajawal',
],
height: 1.1,
);

static TextStyle get currentFont => MyApp.lang == 'ar' ? almarai : varelaRound;

///fontWeight: FontWeight.bold, fontSize: 32
static TextStyle get logo => varelaRound.copyWith(fontWeight: FontWeight.bold, fontSize: 32);

///fontWeight: FontWeight.bold, fontSize: 26
static TextStyle get t1 => currentFont.copyWith(fontWeight: FontWeight.bold, fontSize: 26);

///fontWeight: FontWeight.bold, fontSize: 22
static TextStyle get t2 => currentFont.copyWith(fontWeight: FontWeight.bold, fontSize: 22);

///fontSize: 20
static TextStyle get h1 => currentFont.copyWith(fontSize: 20);

///fontSize: 20, fontWeight: FontWeight.bold
static TextStyle get h1b => currentFont.copyWith(fontSize: 20, fontWeight: FontWeight.bold);

///fontSize: 18
static TextStyle get h2 => currentFont.copyWith(fontSize: 18);

///fontSize: 18, fontWeight: FontWeight.bold
static TextStyle get h2b => currentFont.copyWith(fontSize: 18, fontWeight: FontWeight.bold);

///fontSize: 16
static TextStyle get body1 => currentFont.copyWith(fontSize: 16);

///fontSize: 16, fontWeight: FontWeight.bold
static TextStyle get body1b => currentFont.copyWith(fontSize: 16, fontWeight: FontWeight.bold);

///fontSize: 14
static TextStyle get body2 => currentFont.copyWith(fontSize: 14);

///fontSize: 14, fontWeight: FontWeight.bold
static TextStyle get body2b => currentFont.copyWith(fontSize: 14, fontWeight: FontWeight.bold);

///fontSize: 13
static TextStyle get body3 => currentFont.copyWith(fontSize: 13);

///fontSize: 13, fontWeight: FontWeight.bold
static TextStyle get body3b => currentFont.copyWith(fontSize: 13, fontWeight: FontWeight.bold);

///fontSize: 16
static TextStyle get callOut => currentFont.copyWith(fontSize: 16);

///fontSize: 16, fontWeight: FontWeight.bold
static TextStyle get callOutFocus => callOut.copyWith(fontWeight: FontWeight.bold);

///fontSize: 16
static TextStyle get button => callOut;

///fontSize: 16
static TextStyle get buttonSelected => button.copyWith(fontWeight: FontWeight.normal);

///fontSize: 13
static TextStyle get footnote => body3;

///fontSize: 13, color: kGrey
static TextStyle get hint => body3.withColor(kGrey);

///fontSize: 11, color: kGrey
static TextStyle get hint2 => hint.withSize(11);

///fontSize: 10, color: kGrey
static TextStyle get hint3 => hint.withSize(10);

///fontSize: 13
static TextStyle get caption => footnote;

///fontSize: 13, decoration: TextDecoration.underline, color: Colors.blue
static TextStyle get clickable => footnote.copyWith(decoration: TextDecoration.underline, color: Colors.blue);
}

class Borders {
static final Border blackBorder = Border.all(color: kFontsColor, width: 1.5);

static const BorderRadius sBorderRadius = BorderRadius.all(Radius.circular(5));
static const BorderRadius mBorderRadius = BorderRadius.all(Radius.circular(10));
static const BorderRadius lBorderRadius = BorderRadius.all(Radius.circular(15));
static const BorderRadius xlBorderRadius = BorderRadius.all(Radius.circular(25));
static BorderRadius diagonalBorderRadius(BuildContext context, double radius) => BorderRadius.only(
topLeft: !isArabic() ? Radius.circular(radius) : Radius.zero,
bottomRight: !isArabic() ? Radius.circular(radius) : Radius.zero,
topRight: isArabic() ? Radius.circular(radius) : Radius.zero,
bottomLeft: isArabic() ? Radius.circular(radius) : Radius.zero,
);
}

class Styles {
static const List<BoxShadow> boxShadow = [
BoxShadow(blurRadius: 7, color: Colors.black12, offset: Offset(0, 4)),
];
static const List<BoxShadow> unifiedShadow = [
BoxShadow(blurRadius: 1, color: Colors.black12),
];

/// blurRadius: 3, color: Colors.black12
static const List<BoxShadow> unifiedShadow2 = [
BoxShadow(blurRadius: 5, color: Colors.black12),
];
static const List<BoxShadow> boxShadowTop = [
BoxShadow(blurRadius: 2, color: Colors.black12, offset: Offset(0, -1)),
];

static const List<BoxShadow> boxShadowBottom = [
BoxShadow(blurRadius: 1, color: Colors.black12, offset: Offset(0, 1)),
];
static const List<BoxShadow> boxShadowHeavy = [
BoxShadow(blurRadius: 10, color: Colors.black38, offset: Offset(-2, 2)),
];
static const BoxDecoration kContainerDecoration =
BoxDecoration(boxShadow: boxShadow, borderRadius: Borders.mBorderRadius, color: kWhite);
}
